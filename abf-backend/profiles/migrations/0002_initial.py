# Generated by Django 4.2.20 on 2025-06-25 11:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0001_initial'),
        ('profiles', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='organizationattachmenthistory',
            name='uploaded_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.user'),
        ),
        migrations.AddField(
            model_name='organizationattachment',
            name='attachment_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='organization_attachments', to='profiles.organizationattachmenttype'),
        ),
        migrations.AddField(
            model_name='organizationattachment',
            name='organization',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments_of_organization', to='profiles.organization'),
        ),
        migrations.AddField(
            model_name='organizationattachment',
            name='status',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='profiles.organizationattachmentstatus'),
        ),
        migrations.AddField(
            model_name='organizationattachment',
            name='uploaded_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.user'),
        ),
        migrations.AddField(
            model_name='organization',
            name='address',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='profiles.address'),
        ),
        migrations.AddField(
            model_name='organization',
            name='grant_maker_organization',
            field=models.ForeignKey(blank=True, help_text='Only used if this organization is a funding entity', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='funding_entities', to='profiles.organization'),
        ),
        migrations.AddField(
            model_name='organization',
            name='organization_function_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organizations_by_function_type', to='profiles.organizationfunctiontype'),
        ),
        migrations.AddField(
            model_name='organization',
            name='organization_legal_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='organizations_by_legal_type', to='profiles.organizationlegaltype'),
        ),
        migrations.AddConstraint(
            model_name='organizationattachment',
            constraint=models.UniqueConstraint(fields=('organization', 'attachment_type'), name='unique_attachment_per_org_per_attachment_type'),
        ),
    ]
