# Generated by Django 4.2.20 on 2025-06-25 11:52

from django.db import migrations, models
import django.db.models.deletion
import django_cryptography.fields
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address_line_1', models.CharField(max_length=512)),
                ('address_line_2', models.CharField(blank=True, null=True)),
                ('locality', models.CharField(max_length=128)),
                ('city', models.CharField(max_length=256)),
                ('state', models.CharField(max_length=128)),
                ('postal_code', models.CharField(max_length=6)),
                ('country', models.CharField(max_length=128)),
            ],
        ),
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('point_of_contact_name', models.CharField(blank=True, max_length=255, null=True)),
                ('budget', models.IntegerField(null=True)),
                ('logo_key', models.CharField(blank=True, max_length=256, null=True)),
                ('organization_name', models.CharField(max_length=255)),
                ('pan_number', django_cryptography.fields.encrypt(models.CharField(blank=True, max_length=50, null=True))),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(max_length=128, region=None)),
                ('email_address', models.EmailField(blank=True, max_length=100, null=True)),
                ('website_url', models.URLField(blank=True, max_length=255, null=True)),
                ('number_of_team_members', models.IntegerField(blank=True, null=True)),
                ('mission', models.TextField(blank=True, null=True)),
                ('vision', models.TextField(blank=True, null=True)),
                ('background_history', models.TextField(blank=True, null=True)),
                ('registered_year', models.CharField(max_length=4, null=True)),
                ('csr_registration_number', models.CharField(blank=True, max_length=128, null=True)),
                ('tax_registration_number', models.CharField(blank=True, max_length=100, null=True)),
                ('tax_registration_number_under_12_a', models.CharField(blank=True, max_length=100, null=True)),
                ('fcra_registration_number', models.CharField(blank=True, max_length=100, null=True)),
                ('trust_registration_number', models.CharField(blank=True, max_length=100, null=True)),
                ('darpan_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='OrganizationAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_filename', models.CharField(blank=True, max_length=512, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('object_key', models.CharField(blank=True, max_length=1024, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='OrganizationAttachmentStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=32, unique=True)),
                ('name', models.CharField(max_length=128)),
                ('description', models.CharField(blank=True, max_length=512, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='OrganizationAttachmentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=64, unique=True)),
                ('name', models.CharField(max_length=256)),
                ('description', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='OrganizationFunctionType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=128, unique=True)),
                ('name', models.CharField(max_length=128, unique=True)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='OrganizationLegalType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=128, unique=True)),
                ('name', models.CharField(max_length=128, unique=True)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='OrganizationKMP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('designation', models.CharField(blank=True, max_length=100, null=True)),
                ('din', models.CharField(blank=True, max_length=50, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=10, null=True)),
                ('email', models.EmailField(max_length=254, null=True, unique=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='key_management_personnel_in_organization', to='profiles.organization')),
            ],
        ),
        migrations.CreateModel(
            name='OrganizationAttachmentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_key', models.CharField(max_length=1024)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('attachment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='profiles.organizationattachment')),
                ('status', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='profiles.organizationattachmentstatus')),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
