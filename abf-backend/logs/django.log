INFO 2025-06-25 11:36:24,021 autoreload Watching for file changes with StatReloader
WARNING 2025-06-25 11:36:42,212 auth_middleware Authorization header is missing
WARNING 2025-06-25 11:36:42,212 auth_middleware Authorization header is missing
WARNING 2025-06-25 11:36:42,213 log Bad Request: /api/funding/v1/expenses/quarterly-totals/
WARNING 2025-06-25 11:36:42,214 log Bad Request: /api/funding/v1/expenses/list/
INFO 2025-06-25 11:37:02,897 views data = {'username': '<EMAIL>', 'password': 'e,an6[zE4Xl1'}
INFO 2025-06-25 11:37:02,898 utils Trying Cognito Login
INFO 2025-06-25 11:37:03,445 utils Normal user password auth = {'ChallengeParameters': {}, 'AuthenticationResult': {'AccessToken': '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'ExpiresIn': 3600, 'TokenType': 'Bearer', 'RefreshToken': 'eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.rPSic28TuThfhlPQpVlqMC7QQ2sUX4ck439kpFINjQVdO9-Jb-fu9Y4KJl7hprJLA1PvaXH2PMydrbRLDqAZlKXmI6Jyjk5IQFzQHwXGYZZHzIluo9QYvGYZgQ8JMGZck14l6xbq_w7CucTLmayXWj68h6mmiXqkEzqDYpw8jBz_sfoqhgqcstnWJ26hrhqyRHAMPmz1x7gMVFmp6eJSImYNAIxJsw1u-eJFQFKtP0VpQepp4JqOEgriKvZGB51hcvY6rcGGBeyOIarvhbk1DaHqpO4qxmGC5jDSwoFFyMYb7EopUJKeBTiFZoGAVEP94A7cq5rwvQ7eqO7Xg72lsQ.8yFFTGcYsrfGujuV.zsy51ggC3z-xMJHEWw_LIluMNCP-vCqnecxlgkYUfYeIb-VwbU5R7mMPxaaOp4KLnWtzVRl2Z8LLR3Q27o-fdGVNzQjiCS2ZcLZTGHgl7okQTxUUd7LNJNSHtbTYzjTUZOoel-gJu6ZYvY_0drjrxZnxxEPlo6CdwFx5GFaUsvpZp1wrWv3f74osD1XxU9qm5ezhNkOtKYYYF4HDrdvyLHLvsl4htzbea2-GTx6rIxPtbBbWXeHGBT8gUt8VhUu1i35nvfUTEOPI7lZfIFimjtf29LoV8hLkb0-HncrZ-LBPYmcHL5FNDakNsolNAXO0GDUTLIiggz3fo3jKpFmrmnprjyhCbTrrjIhX1EYJRBbSxvgOUnFnktIeFOdcPdYqwO5-8LHNKnvgNuZjlud9-dyj1yBTxb16htxKiNzZ0wgoDsThHTDInrFbTSPpB7ick2q1wg9GQNovVAkrWWXP18LLcYjZpWPvsfOrNv6F29Lg3OnQ0azhbwF6-5Bmf9pOEh6N10KvoUwWq1CkKxk17gFtZrCzAkxLCNCMecgP-Zm7FkBPY0undjgEzxK6bdoMFkFkrVj0rYQS8ihwDZa6zZSm4vxaO8yJEW9zgeQwaPIDyXOlYi0HYqVYW5PK3pLB2paH7yLh0WXyWFjZX1645jaKCWihV3tNCbL4SlQNbSs_73aTm7C-w1uWA1Ex_Lq9k9NufIEuz_iaEALusZzRk9hZhlfxpHOFy5QSTeU4P0sxWkaBBwdOArwYMw0-8gp1z9m6GVuIGj0WEZDUeVvLqEAHmAV3YXgzdK8-ijMI5LfT0fjQBUzmDD2ErvAVi2w246h76Fjjk8fdG0KFRJf0MUHC3hKGUPDvK7UzKvGo1zS4ZXuaTHFV-ioOTMl7lKuQhCKTfMBnJqTuMFIjtdiJLTx-IkQozipSC05hTeqaBhB2hVLIzmE-y0wUyf1o35Jrs7Pci1U7z02MbdYcahZ1H6X_YGuTHAw3m5WQVaf7DtrvLsbszZEq-1M01nYy-CqGlZFhN54wmu4qm37QqSIglbBXMhiRBmrNboRxn4vGtd1YMjafysQeFKF-Le689T1B7J4mD5pwZ6v_wTjYPiiM7g5lx9mM9rE1Wht3nC5p5ZeIc4l8m9VjH02YcJv5bn7pzBuJlOw1DR0iwfCMmMsxqMRsjE-6xqa4yr_LOl73RjopoaPReGKOtyZ8yY0cPHEvk7DVZEgu7wtNr-I0kW7NrKyUiSAzYe67ThsUppv7xFwPaTq8PWMVWa2n-QlwAL0qqPIsnS9iqlSnwa8IAC2GF7101j7VKvsAAnr_eftiUD3k7aONuPs33iqZ64AZlLsoGw.kDNQySR7_JwX0U6B6pz6Hg', 'IdToken': 'eyJraWQiOiJjN3NZR3NZMVgzXC9kYjFISjZrb0d5aHlRZ1NGOXduejZCcDdoS3Y5Q2ZlVT0iLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NPEEGpmQQDI4pABW2lkJ_HN3NM8bzR2Ee35OV8buxGfZ5YCdRvTM-0M-i39f9PdW3Cdmu5RmP8165U-rHZKLH1iGC5R_VhiHHc-wK7yBLK9bNIyu-2P1t8SypAtXK0Q92v-PK7a2vyUJmAuG29czD0Sbxa7o404CMFfL-OlvyAKbf3rfJgLvglba5lM10inqqKiHpCyHVqwc8T_siePIIqg3FflF8Gjt67XLKN56-Ghy9mqVJ1Yyi-h6YNQrYCDUwlNJZ05fb3YLb38sqa0LR0vBvDDIUnSSAkE2nhRgGjaVSYmiKv7mReRkUposnVXDLMcnbB0mwQtkq2j3NkUNGg'}, 'ResponseMetadata': {'RequestId': 'fbd8a2cb-ee46-4513-94cc-a7a46613d56f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 25 Jun 2025 11:37:03 GMT', 'content-type': 'application/x-amz-json-1.1', 'content-length': '4237', 'connection': 'keep-alive', 'x-amzn-requestid': 'fbd8a2cb-ee46-4513-94cc-a7a46613d56f'}, 'RetryAttempts': 0}}
ERROR 2025-06-25 11:37:13,718 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:13,719 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:13,817 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:13,818 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:20,116 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:20,117 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:20,188 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:20,189 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:21,775 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:21,775 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:21,853 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:21,854 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:25,155 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:25,157 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:25,237 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:25,237 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:49,783 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:49,785 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:49,851 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:49,852 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:55,677 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:55,677 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:37:55,751 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:37:55,751 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:40:07,220 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:40:07,221 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:40:07,276 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:40:07,277 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:44:21,645 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:44:21,646 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:44:21,706 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:44:21,707 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:44:24,326 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:44:24,326 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:44:24,448 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:44:24,448 log Internal Server Error: /api/grantmaker/v1/grantees/
INFO 2025-06-25 11:46:04,865 autoreload Watching for file changes with StatReloader
ERROR 2025-06-25 11:46:51,026 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:46:51,027 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:46:51,117 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:46:51,117 log Internal Server Error: /api/grantmaker/v1/grantees/
INFO 2025-06-25 11:47:18,240 views data = {'username': '<EMAIL>', 'password': 'e,an6[zE4Xl1'}
INFO 2025-06-25 11:47:18,241 utils Trying Cognito Login
INFO 2025-06-25 11:47:18,581 utils Normal user password auth = {'ChallengeParameters': {}, 'AuthenticationResult': {'AccessToken': '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'ExpiresIn': 3600, 'TokenType': 'Bearer', 'RefreshToken': 'eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.kLFsl0PPQVmIi0HENJDyyGpf59OKX3_IrVWq0T1BvFkQNdQyeVi5aXqwneU2SzeB50bNM5mf9EqcBhPJToJOaqGn-jDuN2PSD1tV85dMi7vTZYuOKqFCuTpTKRly8MtMtN2A2aM4gNFfi2Sq2qNsN4UtDAwX6yFjk8LKEDca4ycsn3cQRZXSuybmKTQBoCOS4_lqBm9u3m1tUQsXs8ALaYVqWu-C3-tiu92v81Hyz2dJ-nQwQJaRLPLtfyOTANa8hsJK5y9a981fKtNRWzU4NtE3cc827jzYufLo8HSQFT6hqoLTTov0d1SvOrnmRhVyHb5iZ8AA1ukd4gZaA68WXw.Shmn4GmnvRSH0fLY.nYNKPtBiKPIyWg7lwu9mnnUlroxZvXd522O9Smwu-X3C1Udz4UmHemtZSE0e5e4C2ILkKdDTVxpDv3Kp1JJip3Z7pIlTRJe2r16am5_QajvJwCP7YZmAIBMsLwsI8nmAhs9O2q4yj7QDcY24PJCxt9INa69KfATcxV__Rp6dOl8ny5Gr9RP1nyO1_jfkU9dHvnf02PeMuUAY6wRxZ5PkpPyt9ofn1OhOr-2TulIA5sSSD9fqsEPvHUgqdqhLL8uCf8Q2YnwKGSZgj699fo6nRPB4Cf6JdrShntxj3ggAghwizwVA_ddF9aX0hM9Onwnvg_UWGLD0JiQUSq5EWyfhDzRxhQKSgBVGDd6m8sQ_K-jHpRanKSDm8ogHq-ikwLgUd9WtQwpST8rO8qQ4zvV2lgyTBx0zrx_5dCMeLEvSOsbT2E5S2NXp_EpgIrBEWG1UKeG_u28awKXxGAHyiZ2seMIZnPx8IXZcalSFdory9ZLAj2coTSdQZGkXCiHi0K9oFVMMzt6Cn9Q_GY_s9PcxzhXG7iBz1MBEOnlfyc3g03Jd_T3jiSIBS3WJePoDDI5XCgPgNsZyP-iWi6JmaDjbIQDoinknrdTBPF_WBpVfUE6qmrjIcMyDUuIloVrdiLPJQgNl8oj_N1ZEGTim_r9AM8Fbr1pOMGOSdH3PaWTSGxJMur7CXlFt20teZWV3cjvMwJMrmjH575-jxOxbrOIHEhu_MbL50ST8Q1SR8qN9BX0rOYDJdvoyMgqPH_ADgq2vtLdryzeJyq282GiVVumbtmaG7kXl5nA24WUlQEKIHUjRv8CxiLDTd-ciuPxT0bYdXn5JWX7Ful_IEm0VzXwjwbJy1fE_ZJsGt9yQUAcoEaEiZ2LEAll1PzPLJVJBLIWFwahJNyPhPhn_cOk7P5Ho_FOwdGCc8OlQteZglYqAwT-Ev4LAP4FbaE7W3huK6T9RB8Eb8MzynPo2dX5hHqZtuyzK6Yk3B-4GfQd7U74HdsdiZLGj8RfyHmx2qOcBDMu5PEymMZo2AIzQAwr25zDdn3mhM_sSSk9McCQoqxGGddAqIvdjgxrgXDtVAtBMGFTrQDGlV2ipcGTTLgJcCYHdLr83HqZLhIY8DG7k5fYCGJ1zvvf_VWDo7sytr36hJjSDVEP3ZiFh8k66h20FxLkWVbIZXmIIVJRB_7NrHXsXSfwLAh--VOL6rETD19seV7yZI4nTRvoKRx441n67gnCjrmjOdQWjQXTwaziLCPlcJSu7EnYkfqcB-ffZIj2sccSwXN_PISB64q9wcB6LOIeiRRmgCzChh3ZRylezID5iVoC-jaslNxFewlPvRlOvyqMgFA.pR12sylPH1GNRShTUYn1Cg', 'IdToken': 'eyJraWQiOiJjN3NZR3NZMVgzXC9kYjFISjZrb0d5aHlRZ1NGOXduejZCcDdoS3Y5Q2ZlVT0iLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OGjDolGUqC2FIHedYQhc3OEvvTI2EshleHfzFgvpbg0FEcJlqEhQlZiupLBKM6iq4TMnHU5Mtl231YQoWJlUkKBDIXcsYDczOQDzPOPHUqolzpEh4hqZUjlcY9zCjUfioUglGqsNYzZuYOw7e0qSHPhgDZl-yehGGc2XIvHFC5AOD8teMTkP2VL79AcBuMd0IhzY3Iq0WDBJ-Py5FKZQS2GGRVaITt5MqnDLP1k6ZpKMzOxTcjnAIkqO4BzRUR3PZV1KUHG5f00hxkZ2CyGyl2plpIEWxRzZA1--_AFM-IGOWWDY0rj7eAHALdTdAsRODzP8059ZwSYI5N5f-RA_ig'}, 'ResponseMetadata': {'RequestId': '074cacd8-a195-42b6-9730-fb7863a90066', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 25 Jun 2025 11:47:18 GMT', 'content-type': 'application/x-amz-json-1.1', 'content-length': '4237', 'connection': 'keep-alive', 'x-amzn-requestid': '074cacd8-a195-42b6-9730-fb7863a90066'}, 'RetryAttempts': 0}}
ERROR 2025-06-25 11:47:28,593 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:47:28,594 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:47:28,704 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:47:28,705 log Internal Server Error: /api/grantmaker/v1/grantees/
INFO 2025-06-25 11:52:08,555 autoreload Watching for file changes with StatReloader
ERROR 2025-06-25 11:52:16,534 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:52:16,534 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:52:16,667 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:52:16,667 log Internal Server Error: /api/grantmaker/v1/grantees/
INFO 2025-06-25 11:52:45,021 views data = {'username': '<EMAIL>', 'password': 'e,an6[zE4Xl1'}
INFO 2025-06-25 11:52:45,021 utils Trying Cognito Login
INFO 2025-06-25 11:52:45,448 utils Normal user password auth = {'ChallengeParameters': {}, 'AuthenticationResult': {'AccessToken': '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'ExpiresIn': 3600, 'TokenType': 'Bearer', 'RefreshToken': 'eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.iUW3L241rIQVpmj0jyNdqQnyZBAVZnONBNgf_znTUdBs5mLevTXs8I-xTAtuUfR1fn-OApKfPTJaYbQoUuFoCfwtCh1t194vuPVHQxHYp5701SK-MqmxtftcIBVyCwCILBfSj6Ox9BDQbeAJzrR4KZUA4QPCO3ICmfIWI-GMT8MJnxAyuPmCMcO4XRicFdyC1KgUvEXYOhnAiHu8v062aPRDkIdeUBssFJFsJKnCr0Z6bet8O0MaSrSv8P8FreIi28p01YMa77qNJswivlTfF9Ds7AwyZOGry2qF_bDdxgRO2K2IVlEXLo5m1w2g-7ycLKTOLrIvLLCh5kmqHUFZAQ.FTrdkLzHIDmXDX_s.XpvGFoSMFvScaI-YLWM3xL0Q9TgqcMQMJv9bcOku7KjBEVez0nKDtqLHD0Us4i28IIA7FDJOKksHMSaJh_bw49MfrvFAYzgHg3qrsjwp9cgXsm8VaZjv6MvPUhwcOCJi_sQFbrIuvktAmWmQnpXgNE9Zr9dBbQfE3i-xrxarhtx05Cq1j3jaJCaNUsgwNUPEHeTSUFhCV2w35Ue8BrEoWwbByhY614SYjko7VACSUMe3A4LTKUJHcD4VzrSMMw1_gkzkPVb01K7hFUSQYSIzc9JZ7zWaQ2rMhZ8fm5HkbPgpkSGZDvQIcCHKy9M16-jL859Ai3wSSCx24Y5yrOoVVlxNkYC1YuImnPqRQ7AH4CRKYiZFCGLcCIwPIyXSzOHQ6xcE-a6rtkYHpTKR0K-xwNcSCacTp-eRgs85i16pQdqLdqe6MLFv5Mpi-K9X8bx_WM0qsZ8jT9rIASgIcq4coU_I_trC1gPgUItwylIfI-feZrg60MuT09EUqFxzQBb6-ogZ_2Ge7S9otS-YfBDCVK5bBV58vilwccxrSExJu5GS_qKrHn1ls4-0ldOexX-SgBuca04C0uziwwAbqqQ8GkWHtG9AxrelnbucinQkY8tX99m7Vu3MW8Fmn0459dtok4fE97VdPv3V_HM5cPMB-ZvTJHuXprEu3sIPRs7tSoYZFvXysCddZbtJxuHrjiiCqsMLN4SrgVXTt0Yt9L-pHwgEpyvTvQ7JkA_mWXEPbay93VDT7unINiQ8Tz6mXCQ67PCvEbpRTQjsBFMxQWKfTy-OmKBUm6a_ddF30hT6NNWxx6jPpF68ZOtX0_1ywHNY2_Dtqe_-YLjRIo_MIK4EAT0qZ0f7tuKYWIr6VIdTgM3i4apvYSJ2FENNaH1kxcF2LaR9dgudEmmk0Ceo6sxubIMq2CrJOYJ54I3wdU88LiQOLblsIaqSYUzwEr4dPiJ9lFWpbDXkGGo9ygKWqPv9mAmJgxbSw3I4979Y-l-VjnB9iU2t-MVDCrPfdyXSoSUlMCfDQr5rb_rVi7AHSs3EkGhzv_2MFHhSWf0xmK89K16LMB-zTKAM4UmxR9WRxFp9pJPNFVzzB9FVnpDMc79mLZM1PtZzGos0annnNyFtG9ZUmXzlIzvaUQUxfBxyaCsGcvOsI0q9_T_vqK6LfXI99dYn12TT0aABI_8wxxUI4LulFPTuo6AseWn-Jv_SeWzirRwzKj7pshJ8YLFsK8WX3XFEIRB4WpPSqr3Lfqx0VDjYZ7Jh9t2yJ8A3LokDsCUktr5a4-wouuMf-33N00bvR8ZH9LMUKrQzx8txBwIGReMyZgzLDXasXLHPTafWQ-fvkQ.JiGsFRD9UMFEFf-22hZrUQ', 'IdToken': 'eyJraWQiOiJjN3NZR3NZMVgzXC9kYjFISjZrb0d5aHlRZ1NGOXduejZCcDdoS3Y5Q2ZlVT0iLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eYBpqucIrDDNLNaPnZfRP41IhvlsX-uZp15r9-AYYV9NyJ16ZN-ZW8FR8zW9bdQUUIUgPHP6YwyBn-4Ku8MvzgHJCNeuk5scBNoq8eJy3OSTb7T67yVLEg8s7F1zGLPPy_vVl42kz-FIL30pVFmQ3c1-mJgKDpRTCBVL39_286IV9kYg2JtyKCpwmDYbxQKNvaHohTG1VUI9OToFHGCrmXNYuIhlMYUqI7T8v6APYTt5CVREfGbv_hcuxvzD0C-CAnbBSMKhabz7S6iC-bW3haij5iUt9UPAKpAZjw3YADZCxTc4p5DYDTj0M0-hMl9LpleaS00nlJetWq5LMnDZXg'}, 'ResponseMetadata': {'RequestId': '090edc38-a472-4e1d-a581-d14fa2ebf917', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 25 Jun 2025 11:52:45 GMT', 'content-type': 'application/x-amz-json-1.1', 'content-length': '4237', 'connection': 'keep-alive', 'x-amzn-requestid': '090edc38-a472-4e1d-a581-d14fa2ebf917'}, 'RetryAttempts': 0}}
ERROR 2025-06-25 11:52:51,011 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:52:51,012 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:52:51,125 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:52:51,125 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:52:53,298 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:52:53,298 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:52:53,386 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:52:53,386 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:52:55,153 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:52:55,153 log Internal Server Error: /api/grantmaker/v1/grantees/
ERROR 2025-06-25 11:52:55,259 views Error in grantee list: column grants_grantattachment.attachment_type_id does not exist
LINE 1: ...hment"."id", "grants_grantattachment"."grant_id", "grants_gr...
                                                             ^

ERROR 2025-06-25 11:52:55,259 log Internal Server Error: /api/grantmaker/v1/grantees/
INFO 2025-06-25 12:17:17,049 autoreload Watching for file changes with StatReloader
