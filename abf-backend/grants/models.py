import datetime
import re

from django.db import models
from django.db.models import ForeignKey

from common.mixins.s3 import S3PresignedURLMixin
from users.models import User

from profiles.models import Organization
from django.core.exceptions import ValidationError


class Grant(models.Model):
    """
    Each grant. If each grant belongs to exactly ONE organization,
    add organization as a ForeignKey. If you want many-to-many, see bridging model below.
    """
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='grants_given_to_organisation'
    )

    grant_maker_organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='grants_given_by_grant_maker',
    )

    grant_name = models.CharField(max_length=255)
    start_date = models.DateField(default=datetime.date.today)
    end_date = models.DateField(default=datetime.date.today)
    grant_purpose = models.TextField(blank=True, null=True)
    annual_budget = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    funding_sources = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        pass

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.grant_name} (Org: {self.organization.organization_name})"


class GrantAttachmentType(models.Model):
    name = models.CharField(max_length=64)
    code = models.CharField(max_length=32)
    description = models.CharField(max_length=256)

    def clean(self):
        code_str = str(self.code or "")
        if not re.fullmatch(r'[A-Z_]+', code_str):
            raise ValidationError({'code': 'Code must contain only uppercase letters and underscores.'})

    @classmethod
    def seed_defaults(cls):
        defaults = [
            {'name': 'MOU X CSR', 'code': 'MOU_X_CSR'},
            {'name': 'M&E Framework', 'code': 'M_AND_E_FRAMEWORK'},
            {'name': 'Program Proposal', 'code': 'PROGRAM_PROPOSAL'},
            {'name': 'Program Budgets', 'code': 'PROGRAM_BUDGET'},
            {'name': 'Theory Of Change', 'code': 'THEORY_OF_CHANGE'},
            {'name': 'Program Organogram', 'code': 'PROGRAM_ORGANOGRAM'},
            {'name': 'Gantt Chart', 'code': 'GANTT_CHART'}
        ]

        for item in defaults:
            cls.objects.get_or_create(code=item['code'], defaults=item)

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class GrantAttachment(S3PresignedURLMixin, models.Model):
    grant = models.ForeignKey(
        Grant,
        on_delete=models.CASCADE,
        related_name='attachments'
    )
    attachment_type = models.ForeignKey(
        GrantAttachmentType,
        on_delete=models.CASCADE,
        related_name='grant_attachments',
        null=True
    )
    s3_key = models.CharField(max_length=512)  # S3 object key (e.g. "grant_attachments/abc.pdf")
    filename = models.CharField(max_length=255)  # Original uploaded filename
    file_type = models.CharField(max_length=50, blank=True, null=True)  # e.g., PDF, DOCX
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='documents'
    )
    description = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = ('grant', 'attachment_type')

    def get_download_url(self):
        return self.generate_presigned_url(s3_key=self.s3_key, method="get_object")

    def __str__(self):
        return f"{self.attachment_type.code} for grant: {self.grant.grant_name}"


class OrganizationGrantHistoryStatus(models.TextChoices):
    ACTIVE = "ACTIVE", "Active"
    PENDING = "PENDING", "Pending"
    COMPLETED = "COMPLETED", "Completed"


# Historical grant
class OrganizationGrantHistory(models.Model):
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="grant_histories"
    )
    grant_name = models.CharField(max_length=255)
    grant_purpose = models.TextField()
    start_date = models.DateField()
    end_date = models.DateField()
    budget = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(
        max_length=20,
        choices=OrganizationGrantHistoryStatus.choices,
        default=OrganizationGrantHistoryStatus.PENDING
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"GrantHistory: {self.grant_name} ({self.organization.organization_name})"