# Generated by Django 4.2.20 on 2025-06-25 11:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('grants', '0001_initial'),
        ('users', '0001_initial'),
        ('profiles', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='organizationgranthistory',
            name='organization',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grant_histories', to='profiles.organization'),
        ),
        migrations.AddField(
            model_name='grantattachment',
            name='attachment_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='grant_attachments', to='grants.grantattachmenttype'),
        ),
        migrations.AddField(
            model_name='grantattachment',
            name='grant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='grants.grant'),
        ),
        migrations.AddField(
            model_name='grantattachment',
            name='uploaded_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='users.user'),
        ),
        migrations.AddField(
            model_name='grant',
            name='grant_maker_organization',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grants_given_by_grant_maker', to='profiles.organization'),
        ),
        migrations.AddField(
            model_name='grant',
            name='organization',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grants_given_to_organisation', to='profiles.organization'),
        ),
        migrations.AlterUniqueTogether(
            name='grantattachment',
            unique_together={('grant', 'attachment_type')},
        ),
    ]
