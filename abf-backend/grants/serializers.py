from grants.models import Grant, OrganizationGrantHistory, GrantAttachmentType, GrantAttachment
from rest_framework import serializers

class OrganizationGrantHistorySerializer(serializers.ModelSerializer):
    # Optional: if you want to show the readable label (e.g. "Pending")
    status_display = serializers.SerializerMethodField()

    class Meta:
        model = OrganizationGrantHistory
        fields = [
            "id",
            "organization",
            "grant_name",
            "grant_purpose",
            "start_date",
            "end_date",
            "budget",
            "status",
            "status_display",
            "created_at",
            "updated_at"
        ]
        read_only_fields = ("organization", "created_at", "updated_at")

    def get_status_display(self, obj):
        return obj.get_status_display()


class GrantAttachmentTypeSerializer(serializers.ModelSerializer):

    class Meta:

        model = GrantAttachmentType
        fields = '__all__'


# Serializer for GrantAttachment
class GrantAttachmentSerializer(serializers.ModelSerializer):
    attachment_type_code = serializers.CharField(source="attachment_type.code", read_only=True)
    grant_name = serializers.CharField(source="grant.grant_name", read_only=True)
    download_url = serializers.SerializerMethodField()

    class Meta:
        model = GrantAttachment
        fields = [
            "id", "grant", "grant_name", "attachment_type", "attachment_type_code",
            "s3_key", "filename", "file_type", "uploaded_at", "uploaded_by",
            "description", "download_url"
        ]
        read_only_fields = ("uploaded_by", "uploaded_at")

    def get_download_url(self, obj):
        return obj.get_download_url()

class GrantSerializer(serializers.ModelSerializer):
    recipient_organization_name = serializers.CharField(source='organization.organization_name', read_only=True)
    attachments = GrantAttachmentSerializer(many=True, read_only=True)

    class Meta:
        model = Grant
        fields = [
            'id', 'grant_name', 'start_date', 'end_date', 'grant_purpose',
            'annual_budget', 'funding_sources', 'organization',
            'grant_maker_organization', 'created_at', 'updated_at',
            'recipient_organization_name', 'attachments'
        ]
