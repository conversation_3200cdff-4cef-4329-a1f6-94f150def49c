from grants.models import GrantAttachment
from grants.serializers import GrantAttachmentSerializer
# grants/views.py
from django.http import Http404
from rest_framework.exceptions import NotFound
from rest_framework import viewsets
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from rest_framework.decorators import action

from common.decorators import handle_org_exceptions
from common.exceptions import UserNotRegistered, UserOrganizationNotFound
from grants.models import Grant, OrganizationGrantHistory
from grants.serializers import GrantSerializer, OrganizationGrantHistorySerializer, GrantAttachmentTypeSerializer
from profiles.models import Organization
from users.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON>, IsGrantee
from common.mixins.org_aware import OrgAwareMixin
import logging

class GrantmakerGrantViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    serializer_class = GrantSerializer
    permission_classes = [IsGrantMaker]

    @handle_org_exceptions
    def get_queryset(self):
        user_org = self.get_user_org(self.request)
        return Grant.objects.filter(grant_maker_organization=user_org)

    @handle_org_exceptions
    def perform_create(self, serializer):
        user_org = self.get_user_org(self.request)
        org_id = self.request.data.get("organization")

        # Validate recipient organization
        try:
            recipient_org = Organization.objects.get(pk=org_id)
        except Organization.DoesNotExist:
            raise ValidationError("Recipient organization does not exist.")

        if recipient_org.organization_function_type.code != 'GRANTEE_ORGANIZATION':
            raise ValidationError("Only grantee organizations can receive grants.")

        serializer.save(
            grant_maker_organization=user_org,
            organization=recipient_org
        )

class OrganizationGrantHistoryViewSet(OrgAwareMixin, ModelViewSet):
    serializer_class = OrganizationGrantHistorySerializer
    permission_classes = [IsGrantee]

    def get_queryset(self):
        try:
            user_org = self.get_user_org(self.request)
        except UserNotRegistered as e:
            logging.error(str(e))
            return OrganizationGrantHistory.objects.none()
        except UserOrganizationNotFound as e:
            logging.error(str(e))
            return OrganizationGrantHistory.objects.none()
        except Exception as e:
            logging.error(f"Unexpected error occured: {e}")
            return OrganizationGrantHistory.objects.none()

        return OrganizationGrantHistory.objects.filter(organization=user_org)

    @handle_org_exceptions
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user_org = self.get_user_org(self.request)
        instance = serializer.save(organization=user_org)

        response_data = self.get_serializer(instance).data
        return Response({
            "status": "SUCCESS",
            "data": response_data,
            "message": "Historical grant added successfully!"
        }, status=status.HTTP_201_CREATED)

    @handle_org_exceptions
    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        user_org = self.get_user_org(self.request)
        if instance.organization != user_org:
            raise ValidationError("You do not have permission to update this grant history.")

        return super().partial_update(request, *args, **kwargs)

    @handle_org_exceptions
    def retrieve(self, request, *args, **kwargs):
        user_org = self.get_user_org(self.request)
        try:
            instance = OrganizationGrantHistory.objects.get(
                pk=kwargs.get("pk"),
                organization=user_org
            )
        except OrganizationGrantHistory.DoesNotExist:
            raise NotFound("No grant history record found for this ID and your organization.")
        except Exception as e:
            logging.debug(f"Error retrieving grant history: {e}")
            raise NotFound("Something went wrong while fetching the grant history.")

        serializer = self.get_serializer(instance)
        return Response({
            "status": "SUCCESS",
            "message": "Grant history retrieved successfully.",
            "data": serializer.data
        })


class GranteeGrantViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    permission_classes = [IsGrantee]
    serializer_class = GrantSerializer

    def get_queryset(self):
        try:
            org = self.get_user_org(self.request)

        except UserNotRegistered as e:
            logging.error(f"{e}")

        except UserOrganizationNotFound as e:
            logging.error(f"{e}")

        except Exception as e:
            logging.error(f"Unexpected error when fetching grants: {e}")

        return org.grants_given_to_organisation.all()


class GrantAttachmentTypeViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    permission_classes = [IsGrantee]
    serializer_class = GrantAttachmentTypeSerializer

    def get_queryset(self):
        from grants.models import GrantAttachmentType
        return GrantAttachmentType.objects.all()


class GrantAttachmentViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    queryset = GrantAttachment.objects.all()
    serializer_class = GrantAttachmentSerializer
    permission_classes = [IsGrantee]

    def get_queryset(self):
        org = self.get_user_org(self.request)
        return GrantAttachment.objects.filter(grant__organization=org)

    def perform_create(self, serializer):
        user = self.get_user(self.request)
        org = user.organization
        grant_id = self.request.data.get("grant")
        attachment_type_id = self.request.data.get("attachment_type")

        # Ensure the grant belongs to the requesting user's organization
        try:
            grant = Grant.objects.get(id=grant_id, organization=org)
        except Grant.DoesNotExist:
            raise ValidationError("Invalid grant for your organization.")

        existing = GrantAttachment.objects.filter(
            grant=grant,
            attachment_type_id=attachment_type_id
        ).first()

        if existing:
            existing.s3_key = serializer.validated_data["s3_key"]
            existing.filename = serializer.validated_data["filename"]
            existing.file_type = serializer.validated_data.get("file_type", "")
            existing.uploaded_by = self.request.user
            existing.save()
            self.instance = existing
        else:
            self.instance = serializer.save(grant=grant, uploaded_by=user)

    @action(detail=False, methods=['put'], url_path='upload-or-replace')
    def upload_or_replace(self, request):
        user = self.get_user(request)
        org = user.organization
        data = request.data
        grant_id = data.get("grant")
        attachment_type_id = data.get("attachment_type")

        # Validate grant
        try:
            grant = Grant.objects.get(id=grant_id, organization=org)
        except Grant.DoesNotExist:
            raise ValidationError("Invalid grant for your organization.")

        existing = GrantAttachment.objects.filter(
            grant=grant,
            attachment_type_id=attachment_type_id
        ).first()

        if existing:
            serializer = self.get_serializer(existing, data=data)
        else:
            serializer = self.get_serializer(data=data)

        serializer.is_valid(raise_exception=True)

        if existing:
            existing.s3_key = serializer.validated_data["s3_key"]
            existing.filename = serializer.validated_data["filename"]
            existing.file_type = serializer.validated_data.get("file_type", "")
            existing.uploaded_by = user
            existing.description = serializer.validated_data.get("description", "")
            existing.save()
            return Response(self.get_serializer(existing).data, status=status.HTTP_200_OK)
        else:
            new_attachment = serializer.save(grant=grant, uploaded_by=user)
            return Response(self.get_serializer(new_attachment).data, status=status.HTTP_201_CREATED)
