from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from grants.views import GrantmakerGrantViewSet, OrganizationGrantHistoryViewSet, GranteeGrantViewSet, GrantAttachmentTypeViewSet, GrantAttachmentViewSet

router = DefaultRouter()
router.register("grants", GrantmakerGrantViewSet, basename='grantmaker-grants')
router.register("organization-grant-history", OrganizationGrantHistoryViewSet, basename='organization-grant-history')
router.register('me/grants', GranteeGrantViewSet, basename='grantee-grants')
router.register("grant-attachment-types", GrantAttachmentTypeViewSet, basename='grant-attachment-types')
router.register("grant-attachments", GrantAttachmentViewSet, basename='grant-attachments')

urlpatterns = [
    path('', include(router.urls))
]
