--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4 (Debian 17.4-1.pgdg120+2)
-- Dumped by pg_dump version 17.4 (Debian 17.4-1.pgdg120+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: auth_group; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.auth_group (
    id integer NOT NULL,
    name character varying(150) NOT NULL
);


ALTER TABLE public.auth_group OWNER TO "user";

--
-- Name: auth_group_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.auth_group ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_group_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: auth_group_permissions; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.auth_group_permissions (
    id bigint NOT NULL,
    group_id integer NOT NULL,
    permission_id integer NOT NULL
);


ALTER TABLE public.auth_group_permissions OWNER TO "user";

--
-- Name: auth_group_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.auth_group_permissions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_group_permissions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: auth_permission; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.auth_permission (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    content_type_id integer NOT NULL,
    codename character varying(100) NOT NULL
);


ALTER TABLE public.auth_permission OWNER TO "user";

--
-- Name: auth_permission_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.auth_permission ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_permission_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: auth_user; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.auth_user (
    id integer NOT NULL,
    password character varying(128) NOT NULL,
    last_login timestamp with time zone,
    is_superuser boolean NOT NULL,
    username character varying(150) NOT NULL,
    first_name character varying(150) NOT NULL,
    last_name character varying(150) NOT NULL,
    email character varying(254) NOT NULL,
    is_staff boolean NOT NULL,
    is_active boolean NOT NULL,
    date_joined timestamp with time zone NOT NULL
);


ALTER TABLE public.auth_user OWNER TO "user";

--
-- Name: auth_user_groups; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.auth_user_groups (
    id bigint NOT NULL,
    user_id integer NOT NULL,
    group_id integer NOT NULL
);


ALTER TABLE public.auth_user_groups OWNER TO "user";

--
-- Name: auth_user_groups_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.auth_user_groups ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_user_groups_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: auth_user_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.auth_user ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_user_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: auth_user_user_permissions; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.auth_user_user_permissions (
    id bigint NOT NULL,
    user_id integer NOT NULL,
    permission_id integer NOT NULL
);


ALTER TABLE public.auth_user_user_permissions OWNER TO "user";

--
-- Name: auth_user_user_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.auth_user_user_permissions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_user_user_permissions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_admin_log; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.django_admin_log (
    id integer NOT NULL,
    action_time timestamp with time zone NOT NULL,
    object_id text,
    object_repr character varying(200) NOT NULL,
    action_flag smallint NOT NULL,
    change_message text NOT NULL,
    content_type_id integer,
    user_id integer NOT NULL,
    CONSTRAINT django_admin_log_action_flag_check CHECK ((action_flag >= 0))
);


ALTER TABLE public.django_admin_log OWNER TO "user";

--
-- Name: django_admin_log_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.django_admin_log ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_admin_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_content_type; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.django_content_type (
    id integer NOT NULL,
    app_label character varying(100) NOT NULL,
    model character varying(100) NOT NULL
);


ALTER TABLE public.django_content_type OWNER TO "user";

--
-- Name: django_content_type_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.django_content_type ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_content_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_migrations; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.django_migrations (
    id bigint NOT NULL,
    app character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    applied timestamp with time zone NOT NULL
);


ALTER TABLE public.django_migrations OWNER TO "user";

--
-- Name: django_migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.django_migrations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_migrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_session; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.django_session (
    session_key character varying(40) NOT NULL,
    session_data text NOT NULL,
    expire_date timestamp with time zone NOT NULL
);


ALTER TABLE public.django_session OWNER TO "user";

--
-- Name: funding_disbursement; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.funding_disbursement (
    id bigint NOT NULL,
    scheduled_payment_date date NOT NULL,
    scheduled_amount numeric(12,2) NOT NULL,
    received_amount numeric(12,2),
    pending_amount numeric(12,2),
    payment_received_date date,
    grant_id bigint NOT NULL
);


ALTER TABLE public.funding_disbursement OWNER TO "user";

--
-- Name: funding_disbursement_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.funding_disbursement ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.funding_disbursement_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: funding_expense; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.funding_expense (
    id bigint NOT NULL,
    particulars character varying(255) NOT NULL,
    main_header character varying(255) NOT NULL,
    sub_header character varying(255) NOT NULL,
    units numeric(15,2) NOT NULL,
    frequency numeric(15,2) NOT NULL,
    cost_per_unit numeric(15,2) NOT NULL,
    activity_description text NOT NULL,
    budget_q1 numeric(15,2) NOT NULL,
    budget_q2 numeric(15,2) NOT NULL,
    budget_q3 numeric(15,2) NOT NULL,
    budget_q4 numeric(15,2) NOT NULL,
    actual_q1 numeric(15,2) NOT NULL,
    actual_q2 numeric(15,2) NOT NULL,
    actual_q3 numeric(15,2) NOT NULL,
    actual_q4 numeric(15,2) NOT NULL,
    total_budget numeric(15,2) NOT NULL,
    total_grant_budget numeric(15,2) NOT NULL,
    total_actual numeric(15,2) NOT NULL,
    remarks text NOT NULL,
    expense_date date NOT NULL,
    source_type character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    is_frozen boolean NOT NULL,
    metadata jsonb NOT NULL,
    file1 character varying(100),
    file2 character varying(100),
    file3 character varying(100),
    file4 character varying(100),
    grant_id bigint NOT NULL,
    rejection_notes text
);


ALTER TABLE public.funding_expense OWNER TO "user";

--
-- Name: funding_expense_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.funding_expense ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.funding_expense_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: funding_fundingallocation; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.funding_fundingallocation (
    id bigint NOT NULL,
    amount_allocated numeric(15,2) NOT NULL,
    notes text NOT NULL,
    grant_id bigint NOT NULL,
    funding_entity_id bigint
);


ALTER TABLE public.funding_fundingallocation OWNER TO "user";

--
-- Name: funding_fundingallocation_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.funding_fundingallocation ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.funding_fundingallocation_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: funding_fundingrecord; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.funding_fundingrecord (
    id bigint NOT NULL,
    amount numeric(15,2) NOT NULL,
    date_received date NOT NULL,
    notes text NOT NULL,
    funding_entity_id bigint NOT NULL
);


ALTER TABLE public.funding_fundingrecord OWNER TO "user";

--
-- Name: funding_fundingrecord_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.funding_fundingrecord ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.funding_fundingrecord_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: grants_grant; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.grants_grant (
    id bigint NOT NULL,
    grant_name character varying(255) NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    grant_purpose text,
    annual_budget numeric(12,2),
    funding_sources text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    grant_maker_organization_id bigint NOT NULL,
    organization_id bigint NOT NULL
);


ALTER TABLE public.grants_grant OWNER TO "user";

--
-- Name: grants_grant_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.grants_grant ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.grants_grant_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: grants_grantattachment; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.grants_grantattachment (
    id bigint NOT NULL,
    s3_key character varying(512) NOT NULL,
    filename character varying(255) NOT NULL,
    file_type character varying(50),
    uploaded_at timestamp with time zone NOT NULL,
    description text,
    grant_id bigint NOT NULL,
    uploaded_by_id bigint,
    attachment_type_id bigint
);


ALTER TABLE public.grants_grantattachment OWNER TO "user";

--
-- Name: grants_grantattachment_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.grants_grantattachment ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.grants_grantattachment_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: grants_grantattachmenttype; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.grants_grantattachmenttype (
    id bigint NOT NULL,
    name character varying(64) NOT NULL,
    code character varying(32) NOT NULL,
    description character varying(256) NOT NULL
);


ALTER TABLE public.grants_grantattachmenttype OWNER TO "user";

--
-- Name: grants_grantattachmenttype_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.grants_grantattachmenttype ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.grants_grantattachmenttype_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: grants_organizationgranthistory; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.grants_organizationgranthistory (
    id bigint NOT NULL,
    grant_name character varying(255) NOT NULL,
    grant_purpose text NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    budget numeric(12,2) NOT NULL,
    status character varying(20) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    organization_id bigint NOT NULL
);


ALTER TABLE public.grants_organizationgranthistory OWNER TO "user";

--
-- Name: grants_organizationgranthistory_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.grants_organizationgranthistory ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.grants_organizationgranthistory_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: milestones_milestone; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.milestones_milestone (
    id bigint NOT NULL,
    description text NOT NULL,
    activity character varying(255),
    target_group character varying(255),
    target_q1 character varying(255),
    target_q2 character varying(255),
    target_q3 character varying(255),
    target_q4 character varying(255),
    achieved_q1 character varying(255),
    achieved_q2 character varying(255),
    achieved_q3 character varying(255),
    achieved_q4 character varying(255),
    cost_per_unit numeric(10,2) NOT NULL,
    total_planned_budget numeric(10,2) NOT NULL,
    balance numeric(10,2) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    grant_id bigint NOT NULL
);


ALTER TABLE public.milestones_milestone OWNER TO "user";

--
-- Name: milestones_milestone_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.milestones_milestone ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.milestones_milestone_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: milestones_milestonesattachment; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.milestones_milestonesattachment (
    id bigint NOT NULL,
    attachment_type character varying(100),
    file_path character varying(500),
    uploaded_at timestamp with time zone NOT NULL,
    milestone_id bigint NOT NULL
);


ALTER TABLE public.milestones_milestonesattachment OWNER TO "user";

--
-- Name: milestones_milestonesattachment_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.milestones_milestonesattachment ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.milestones_milestonesattachment_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: milestones_question; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.milestones_question (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    questioner_id bigint
);


ALTER TABLE public.milestones_question OWNER TO "user";

--
-- Name: milestones_question_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.milestones_question ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.milestones_question_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_address; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_address (
    id bigint NOT NULL,
    address_line_1 character varying(512) NOT NULL,
    address_line_2 character varying,
    locality character varying(128) NOT NULL,
    city character varying(256) NOT NULL,
    state character varying(128) NOT NULL,
    postal_code character varying(6) NOT NULL,
    country character varying(128) NOT NULL
);


ALTER TABLE public.profiles_address OWNER TO "user";

--
-- Name: profiles_address_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_address ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_address_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_grantmakerfunds; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_grantmakerfunds (
    id bigint NOT NULL,
    amount numeric(14,2) NOT NULL,
    reference_number character varying(256),
    currency character varying(8) NOT NULL,
    remarks text,
    date_received timestamp with time zone NOT NULL,
    payment_mode character varying NOT NULL,
    created_at timestamp with time zone NOT NULL,
    added_by_id bigint NOT NULL,
    funder_id bigint NOT NULL,
    recipient_id bigint NOT NULL
);


ALTER TABLE public.profiles_grantmakerfunds OWNER TO "user";

--
-- Name: profiles_grantmakerfunds_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_grantmakerfunds ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_grantmakerfunds_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_organization; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_organization (
    id bigint NOT NULL,
    organization_name character varying(255) NOT NULL,
    pan_number bytea NOT NULL,
    phone_number character varying(128),
    email_address character varying(100),
    website_url character varying(255),
    number_of_team_members integer,
    background_history text,
    csr_registration_number character varying(128),
    tax_registration_number character varying(100),
    tax_registration_number_under_12_a character varying(100),
    fcra_registration_number character varying(100),
    trust_registration_number character varying(100),
    darpan_id character varying(100),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    address_id bigint,
    organization_function_type_id bigint NOT NULL,
    organization_legal_type_id bigint,
    created_by_grant_maker_id bigint,
    point_of_contact_name character varying(255),
    budget integer,
    logo_key character varying(256),
    mission text,
    vision text,
    registered_year character varying(4),
    grant_maker_organization_id bigint
);


ALTER TABLE public.profiles_organization OWNER TO "user";

--
-- Name: profiles_organization_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_organization ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_organization_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_organizationattachment; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_organizationattachment (
    id bigint NOT NULL,
    original_filename character varying(512),
    remarks text,
    object_key character varying(1024),
    uploaded_at timestamp with time zone NOT NULL,
    attachment_type_id bigint NOT NULL,
    organization_id bigint NOT NULL,
    status_id bigint NOT NULL,
    uploaded_by_id bigint
);


ALTER TABLE public.profiles_organizationattachment OWNER TO "user";

--
-- Name: profiles_organizationattachment_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_organizationattachment ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_organizationattachment_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_organizationattachmenthistory; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_organizationattachmenthistory (
    id bigint NOT NULL,
    object_key character varying(1024) NOT NULL,
    remarks text,
    uploaded_at timestamp with time zone NOT NULL,
    attachment_id bigint NOT NULL,
    status_id bigint NOT NULL,
    uploaded_by_id bigint
);


ALTER TABLE public.profiles_organizationattachmenthistory OWNER TO "user";

--
-- Name: profiles_organizationattachmenthistory_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_organizationattachmenthistory ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_organizationattachmenthistory_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_organizationattachmentstatus; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_organizationattachmentstatus (
    id bigint NOT NULL,
    code character varying(32) NOT NULL,
    name character varying(128) NOT NULL,
    description character varying(512)
);


ALTER TABLE public.profiles_organizationattachmentstatus OWNER TO "user";

--
-- Name: profiles_organizationattachmentstatus_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_organizationattachmentstatus ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_organizationattachmentstatus_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_organizationattachmenttype; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_organizationattachmenttype (
    id bigint NOT NULL,
    code character varying(64) NOT NULL,
    name character varying(256) NOT NULL,
    description text
);


ALTER TABLE public.profiles_organizationattachmenttype OWNER TO "user";

--
-- Name: profiles_organizationattachmenttype_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_organizationattachmenttype ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_organizationattachmenttype_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_organizationfunctiontype; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_organizationfunctiontype (
    id bigint NOT NULL,
    code character varying(128) NOT NULL,
    name character varying(128) NOT NULL,
    description text NOT NULL
);


ALTER TABLE public.profiles_organizationfunctiontype OWNER TO "user";

--
-- Name: profiles_organizationfunctiontype_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_organizationfunctiontype ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_organizationfunctiontype_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_organizationkmp; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_organizationkmp (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    designation character varying(100),
    din character varying(50),
    phone_number character varying(10),
    email character varying(254),
    organization_id bigint NOT NULL
);


ALTER TABLE public.profiles_organizationkmp OWNER TO "user";

--
-- Name: profiles_organizationkmp_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_organizationkmp ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_organizationkmp_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: profiles_organizationlegaltype; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.profiles_organizationlegaltype (
    id bigint NOT NULL,
    code character varying(128) NOT NULL,
    name character varying(128) NOT NULL,
    description text NOT NULL
);


ALTER TABLE public.profiles_organizationlegaltype OWNER TO "user";

--
-- Name: profiles_organizationlegaltype_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.profiles_organizationlegaltype ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.profiles_organizationlegaltype_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: questioners; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.questioners (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    description text NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    grant_id bigint NOT NULL
);


ALTER TABLE public.questioners OWNER TO "user";

--
-- Name: questioners_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.questioners ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.questioners_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: reports_narrativereport; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.reports_narrativereport (
    id bigint NOT NULL,
    quarter integer NOT NULL,
    year integer NOT NULL,
    status character varying(20) NOT NULL,
    remarks text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    grant_id bigint NOT NULL,
    organization_id bigint NOT NULL
);


ALTER TABLE public.reports_narrativereport OWNER TO "user";

--
-- Name: reports_narrativereport_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.reports_narrativereport ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.reports_narrativereport_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: reports_narrativereportquestion; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.reports_narrativereportquestion (
    id bigint NOT NULL,
    question text NOT NULL,
    answer text NOT NULL,
    report_id bigint NOT NULL
);


ALTER TABLE public.reports_narrativereportquestion OWNER TO "user";

--
-- Name: reports_narrativereportquestion_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.reports_narrativereportquestion ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.reports_narrativereportquestion_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: reports_reportsgallery; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.reports_reportsgallery (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    s3_key character varying(512) NOT NULL,
    file_name character varying(255) NOT NULL,
    file_type character varying(255),
    uploaded_at timestamp with time zone NOT NULL,
    description text,
    organization_id bigint NOT NULL
);


ALTER TABLE public.reports_reportsgallery OWNER TO "user";

--
-- Name: reports_reportsgallery_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.reports_reportsgallery ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.reports_reportsgallery_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: resources_resource; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.resources_resource (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    file_name character varying(255),
    file_description text NOT NULL,
    file_uploaded character varying(100),
    created_at timestamp with time zone NOT NULL,
    uploaded_at timestamp with time zone
);


ALTER TABLE public.resources_resource OWNER TO "user";

--
-- Name: resources_resource_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.resources_resource ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.resources_resource_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: support_historicalticket; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.support_historicalticket (
    id bigint NOT NULL,
    category character varying(100) NOT NULL,
    priority character varying(50) NOT NULL,
    title character varying(255) NOT NULL,
    description text NOT NULL,
    email character varying(254) NOT NULL,
    phone character varying(128) NOT NULL,
    file text,
    status character varying(50) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    history_id integer NOT NULL,
    history_date timestamp with time zone NOT NULL,
    history_change_reason character varying(100),
    history_type character varying(1) NOT NULL,
    grant_id bigint,
    history_user_id integer,
    point_of_contact_name character varying(255)
);


ALTER TABLE public.support_historicalticket OWNER TO "user";

--
-- Name: support_historicalticket_history_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.support_historicalticket ALTER COLUMN history_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.support_historicalticket_history_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: support_ticket; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.support_ticket (
    id bigint NOT NULL,
    category character varying(100) NOT NULL,
    priority character varying(50) NOT NULL,
    title character varying(255) NOT NULL,
    description text NOT NULL,
    email character varying(254) NOT NULL,
    phone character varying(128) NOT NULL,
    file character varying(100),
    status character varying(50) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    grant_id bigint NOT NULL,
    point_of_contact_name character varying(255)
);


ALTER TABLE public.support_ticket OWNER TO "user";

--
-- Name: support_ticket_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.support_ticket ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.support_ticket_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: support_ticketchathistory; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.support_ticketchathistory (
    id bigint NOT NULL,
    update_text text NOT NULL,
    file character varying(100),
    updated_at timestamp with time zone NOT NULL,
    ticket_id bigint NOT NULL,
    user_id bigint
);


ALTER TABLE public.support_ticketchathistory OWNER TO "user";

--
-- Name: support_ticketchathistory_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.support_ticketchathistory ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.support_ticketchathistory_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: users_user; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.users_user (
    id bigint NOT NULL,
    cognito_sub character varying(100) NOT NULL,
    first_name character varying(50) NOT NULL,
    last_name character varying(50) NOT NULL,
    email character varying(128) NOT NULL,
    organization_id bigint,
    role_id bigint NOT NULL,
    type_id bigint NOT NULL
);


ALTER TABLE public.users_user OWNER TO "user";

--
-- Name: users_user_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.users_user ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.users_user_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: users_userrole; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.users_userrole (
    id bigint NOT NULL,
    code character varying(50) NOT NULL,
    name character varying(50) NOT NULL,
    description character varying(256) NOT NULL
);


ALTER TABLE public.users_userrole OWNER TO "user";

--
-- Name: users_userrole_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.users_userrole ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.users_userrole_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: users_usertype; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.users_usertype (
    id bigint NOT NULL,
    code character varying(50) NOT NULL,
    name character varying(50) NOT NULL,
    description character varying(256) NOT NULL
);


ALTER TABLE public.users_usertype OWNER TO "user";

--
-- Name: users_usertype_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.users_usertype ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.users_usertype_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Data for Name: auth_group; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.auth_group (id, name) FROM stdin;
\.


--
-- Data for Name: auth_group_permissions; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.auth_group_permissions (id, group_id, permission_id) FROM stdin;
\.


--
-- Data for Name: auth_permission; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.auth_permission (id, name, content_type_id, codename) FROM stdin;
1	Can add log entry	1	add_logentry
2	Can change log entry	1	change_logentry
3	Can delete log entry	1	delete_logentry
4	Can view log entry	1	view_logentry
5	Can add permission	2	add_permission
6	Can change permission	2	change_permission
7	Can delete permission	2	delete_permission
8	Can view permission	2	view_permission
9	Can add group	3	add_group
10	Can change group	3	change_group
11	Can delete group	3	delete_group
12	Can view group	3	view_group
13	Can add user	4	add_user
14	Can change user	4	change_user
15	Can delete user	4	delete_user
16	Can view user	4	view_user
17	Can add content type	5	add_contenttype
18	Can change content type	5	change_contenttype
19	Can delete content type	5	delete_contenttype
20	Can view content type	5	view_contenttype
21	Can add session	6	add_session
22	Can change session	6	change_session
23	Can delete session	6	delete_session
24	Can view session	6	view_session
25	Can add disbursement	7	add_disbursement
26	Can change disbursement	7	change_disbursement
27	Can delete disbursement	7	delete_disbursement
28	Can view disbursement	7	view_disbursement
29	Can add expense	8	add_expense
30	Can change expense	8	change_expense
31	Can delete expense	8	delete_expense
32	Can view expense	8	view_expense
33	Can add grant	9	add_grant
34	Can change grant	9	change_grant
35	Can delete grant	9	delete_grant
36	Can view grant	9	view_grant
37	Can add grant attachment	10	add_grantattachment
38	Can change grant attachment	10	change_grantattachment
39	Can delete grant attachment	10	delete_grantattachment
40	Can view grant attachment	10	view_grantattachment
41	Can add organization grant history	11	add_organizationgranthistory
42	Can change organization grant history	11	change_organizationgranthistory
43	Can delete organization grant history	11	delete_organizationgranthistory
44	Can view organization grant history	11	view_organizationgranthistory
45	Can add questioner	12	add_questioner
46	Can change questioner	12	change_questioner
47	Can delete questioner	12	delete_questioner
48	Can view questioner	12	view_questioner
49	Can add question	13	add_question
50	Can change question	13	change_question
51	Can delete question	13	delete_question
52	Can view question	13	view_question
53	Can add milestone	14	add_milestone
54	Can change milestone	14	change_milestone
55	Can delete milestone	14	delete_milestone
56	Can view milestone	14	view_milestone
57	Can add milestones attachment	15	add_milestonesattachment
58	Can change milestones attachment	15	change_milestonesattachment
59	Can delete milestones attachment	15	delete_milestonesattachment
60	Can view milestones attachment	15	view_milestonesattachment
61	Can add address	16	add_address
62	Can change address	16	change_address
63	Can delete address	16	delete_address
64	Can view address	16	view_address
65	Can add funds received from funding entity	17	add_fundsreceivedfromfundingentity
66	Can change funds received from funding entity	17	change_fundsreceivedfromfundingentity
67	Can delete funds received from funding entity	17	delete_fundsreceivedfromfundingentity
68	Can view funds received from funding entity	17	view_fundsreceivedfromfundingentity
69	Can add organization	18	add_organization
70	Can change organization	18	change_organization
71	Can delete organization	18	delete_organization
72	Can view organization	18	view_organization
73	Can add organization attachment	19	add_organizationattachment
74	Can change organization attachment	19	change_organizationattachment
75	Can delete organization attachment	19	delete_organizationattachment
76	Can view organization attachment	19	view_organizationattachment
77	Can add organization attachment status	20	add_organizationattachmentstatus
78	Can change organization attachment status	20	change_organizationattachmentstatus
79	Can delete organization attachment status	20	delete_organizationattachmentstatus
80	Can view organization attachment status	20	view_organizationattachmentstatus
81	Can add organization attachment type	21	add_organizationattachmenttype
82	Can change organization attachment type	21	change_organizationattachmenttype
83	Can delete organization attachment type	21	delete_organizationattachmenttype
84	Can view organization attachment type	21	view_organizationattachmenttype
85	Can add organization function type	22	add_organizationfunctiontype
86	Can change organization function type	22	change_organizationfunctiontype
87	Can delete organization function type	22	delete_organizationfunctiontype
88	Can view organization function type	22	view_organizationfunctiontype
89	Can add organization legal type	23	add_organizationlegaltype
90	Can change organization legal type	23	change_organizationlegaltype
91	Can delete organization legal type	23	delete_organizationlegaltype
92	Can view organization legal type	23	view_organizationlegaltype
93	Can add organization kmp	24	add_organizationkmp
94	Can change organization kmp	24	change_organizationkmp
95	Can delete organization kmp	24	delete_organizationkmp
96	Can view organization kmp	24	view_organizationkmp
97	Can add organization attachment history	25	add_organizationattachmenthistory
98	Can change organization attachment history	25	change_organizationattachmenthistory
99	Can delete organization attachment history	25	delete_organizationattachmenthistory
100	Can view organization attachment history	25	view_organizationattachmenthistory
101	Can add ticket	26	add_ticket
102	Can change ticket	26	change_ticket
103	Can delete ticket	26	delete_ticket
104	Can view ticket	26	view_ticket
105	Can add ticket chat history	27	add_ticketchathistory
106	Can change ticket chat history	27	change_ticketchathistory
107	Can delete ticket chat history	27	delete_ticketchathistory
108	Can view ticket chat history	27	view_ticketchathistory
109	Can add historical ticket	28	add_historicalticket
110	Can change historical ticket	28	change_historicalticket
111	Can delete historical ticket	28	delete_historicalticket
112	Can view historical ticket	28	view_historicalticket
113	Can add user role	29	add_userrole
114	Can change user role	29	change_userrole
115	Can delete user role	29	delete_userrole
116	Can view user role	29	view_userrole
117	Can add user type	30	add_usertype
118	Can change user type	30	change_usertype
119	Can delete user type	30	delete_usertype
120	Can view user type	30	view_usertype
121	Can add user	31	add_user
122	Can change user	31	change_user
123	Can delete user	31	delete_user
124	Can view user	31	view_user
125	Can add grant maker funds	32	add_grantmakerfunds
126	Can change grant maker funds	32	change_grantmakerfunds
127	Can delete grant maker funds	32	delete_grantmakerfunds
128	Can view grant maker funds	32	view_grantmakerfunds
129	Can add funding allocation	33	add_fundingallocation
130	Can change funding allocation	33	change_fundingallocation
131	Can delete funding allocation	33	delete_fundingallocation
132	Can view funding allocation	33	view_fundingallocation
133	Can add funding entity	34	add_fundingentity
134	Can change funding entity	34	change_fundingentity
135	Can delete funding entity	34	delete_fundingentity
136	Can view funding entity	34	view_fundingentity
137	Can add funding record	35	add_fundingrecord
138	Can change funding record	35	change_fundingrecord
139	Can delete funding record	35	delete_fundingrecord
140	Can view funding record	35	view_fundingrecord
141	Can add narrative report	36	add_narrativereport
142	Can change narrative report	36	change_narrativereport
143	Can delete narrative report	36	delete_narrativereport
144	Can view narrative report	36	view_narrativereport
145	Can add reports gallery	37	add_reportsgallery
146	Can change reports gallery	37	change_reportsgallery
147	Can delete reports gallery	37	delete_reportsgallery
148	Can view reports gallery	37	view_reportsgallery
149	Can add narrative report question	38	add_narrativereportquestion
150	Can change narrative report question	38	change_narrativereportquestion
151	Can delete narrative report question	38	delete_narrativereportquestion
152	Can view narrative report question	38	view_narrativereportquestion
153	Can add resource	39	add_resource
154	Can change resource	39	change_resource
155	Can delete resource	39	delete_resource
156	Can view resource	39	view_resource
157	Can add grant attachment type	40	add_grantattachmenttype
158	Can change grant attachment type	40	change_grantattachmenttype
159	Can delete grant attachment type	40	delete_grantattachmenttype
160	Can view grant attachment type	40	view_grantattachmenttype
161	Can add impact	41	add_impact
162	Can change impact	41	change_impact
163	Can delete impact	41	delete_impact
164	Can view impact	41	view_impact
165	Can add outcome	42	add_outcome
166	Can change outcome	42	change_outcome
167	Can delete outcome	42	delete_outcome
168	Can view outcome	42	view_outcome
169	Can add output	43	add_output
170	Can change output	43	change_output
171	Can delete output	43	delete_output
172	Can view output	43	view_output
173	Can add activity	44	add_activity
174	Can change activity	44	change_activity
175	Can delete activity	44	delete_activity
176	Can view activity	44	view_activity
\.


--
-- Data for Name: auth_user; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.auth_user (id, password, last_login, is_superuser, username, first_name, last_name, email, is_staff, is_active, date_joined) FROM stdin;
1	pbkdf2_sha256$600000$bYJwlvls7eBlBZxquvZB9q$TJWelRYxtyDKudxzEegYzS4K4zzhalvAkOQPOr1TcZ8=	2025-06-01 20:34:27.703413+00	t	root				t	t	2025-04-16 10:14:31.468232+00
\.


--
-- Data for Name: auth_user_groups; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.auth_user_groups (id, user_id, group_id) FROM stdin;
\.


--
-- Data for Name: auth_user_user_permissions; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.auth_user_user_permissions (id, user_id, permission_id) FROM stdin;
\.


--
-- Data for Name: django_admin_log; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.django_admin_log (id, action_time, object_id, object_repr, action_flag, change_message, content_type_id, user_id) FROM stdin;
1	2025-04-16 10:17:11.715833+00	1	31f39d9a-d0e1-70b1-922d-92c4897327ff - ADMIN - Admin	1	[{"added": {}}]	27	1
2	2025-04-16 10:18:22.112785+00	2	01f3dd2a-8051-70d8-e478-d9c899f47eba - ADMIN - Admin	1	[{"added": {}}]	27	1
3	2025-04-22 06:14:22.069892+00	2	GRANTEE - Grantee	1	[{"added": {}}]	26	1
4	2025-04-22 06:14:35.745906+00	3	GRANT_MAKER - Grant Maker	1	[{"added": {}}]	26	1
5	2025-04-22 06:14:43.667363+00	1	ADMIN - Admin	3		26	1
6	2025-04-22 06:20:21.000015+00	2	GRANT_MAKER - Grant Maker	3		25	1
7	2025-04-22 06:20:26.254503+00	1	GRANTEE - Grantee	3		25	1
8	2025-04-22 06:20:47.501167+00	4	31f39d9a-d0e1-70b1-922d-92c4897327ff - GRANTEE - Grantee	2	[{"changed": {"fields": ["Organization"]}}]	27	1
9	2025-04-22 06:20:54.38144+00	6	01f3dd2a-8051-70d8-e478-d9c899f47eba - GRANT_MAKER - Grant Maker	2	[{"changed": {"fields": ["Organization"]}}]	27	1
10	2025-04-30 17:33:28.800812+00	5	Title (open)	1	[{"added": {}}]	25	1
11	2025-05-28 09:33:34.079534+00	1	Address object (1)	1	[{"added": {}}]	16	1
12	2025-05-28 09:33:50.137583+00	1	Tech Info Foundationnn - GRANTEE_ORGANIZATION - Grantee Organization	2	[{"changed": {"fields": ["Address"]}}]	18	1
\.


--
-- Data for Name: django_content_type; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.django_content_type (id, app_label, model) FROM stdin;
1	admin	logentry
2	auth	permission
3	auth	group
4	auth	user
5	contenttypes	contenttype
6	sessions	session
7	funding	disbursement
8	funding	expense
9	grants	grant
10	grants	grantattachment
11	grants	organizationgranthistory
12	milestones	questioner
13	milestones	question
14	milestones	milestone
15	milestones	milestonesattachment
16	profiles	address
17	profiles	fundsreceivedfromfundingentity
18	profiles	organization
19	profiles	organizationattachment
20	profiles	organizationattachmentstatus
21	profiles	organizationattachmenttype
22	profiles	organizationfunctiontype
23	profiles	organizationlegaltype
24	profiles	organizationkmp
25	profiles	organizationattachmenthistory
26	support	ticket
27	support	ticketchathistory
28	support	historicalticket
29	users	userrole
30	users	usertype
31	users	user
32	profiles	grantmakerfunds
33	funding	fundingallocation
34	funding	fundingentity
35	funding	fundingrecord
36	reports	narrativereport
37	reports	reportsgallery
38	reports	narrativereportquestion
39	resources	resource
40	grants	grantattachmenttype
41	reports	impact
42	reports	outcome
43	reports	output
44	reports	activity
\.


--
-- Data for Name: django_migrations; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.django_migrations (id, app, name, applied) FROM stdin;
1	contenttypes	0001_initial	2025-05-14 05:59:55.196115+00
2	auth	0001_initial	2025-05-14 05:59:55.208925+00
3	admin	0001_initial	2025-05-14 05:59:55.213497+00
4	admin	0002_logentry_remove_auto_add	2025-05-14 05:59:55.21591+00
5	admin	0003_logentry_add_action_flag_choices	2025-05-14 05:59:55.21866+00
6	contenttypes	0002_remove_content_type_name	2025-05-14 05:59:55.225128+00
7	auth	0002_alter_permission_name_max_length	2025-05-14 05:59:55.227664+00
8	auth	0003_alter_user_email_max_length	2025-05-14 05:59:55.230391+00
9	auth	0004_alter_user_username_opts	2025-05-14 05:59:55.232597+00
10	auth	0005_alter_user_last_login_null	2025-05-14 05:59:55.234834+00
11	auth	0006_require_contenttypes_0002	2025-05-14 05:59:55.235514+00
12	auth	0007_alter_validators_add_error_messages	2025-05-14 05:59:55.237706+00
13	auth	0008_alter_user_username_max_length	2025-05-14 05:59:55.242363+00
14	auth	0009_alter_user_last_name_max_length	2025-05-14 05:59:55.244857+00
15	auth	0010_alter_group_name_max_length	2025-05-14 05:59:55.247905+00
16	auth	0011_update_proxy_permissions	2025-05-14 05:59:55.251929+00
17	auth	0012_alter_user_first_name_max_length	2025-05-14 05:59:55.254147+00
18	grants	0001_initial	2025-05-14 05:59:55.258568+00
21	profiles	0001_initial	2025-05-14 06:06:15.962379+00
22	grants	0002_initial	2025-05-14 06:06:15.974553+00
23	users	0001_initial	2025-05-14 06:06:15.983857+00
25	profiles	0003_alter_organization_address_and_more	2025-05-14 06:06:16.041872+00
26	sessions	0001_initial	2025-05-14 06:06:16.045919+00
27	support	0001_initial	2025-05-14 06:06:16.069915+00
33	profiles	0004_grantmakerfunds_and_more	2025-05-14 11:50:29.300694+00
34	profiles	0005_organization_created_by_grant_maker	2025-05-15 06:12:52.424908+00
36	profiles	0002_organization_point_of_contact_name	2025-05-22 08:03:13.475902+00
37	funding	0003_alter_expense_frequency_alter_expense_status	2025-05-26 14:41:10.170249+00
38	funding	0004_alter_expense_frequency_alter_expense_units	2025-05-26 14:43:48.610533+00
39	funding	0005_alter_expense_frequency_alter_expense_units	2025-05-26 15:21:02.102317+00
41	profiles	0004_organization_registered_year	2025-05-29 09:36:45.568568+00
43	funding	0001_initial	2025-06-01 12:19:55.952823+00
44	funding	0002_initial	2025-06-01 12:19:56.007833+00
46	profiles	0002_initial	2025-06-01 20:17:06.038992+00
47	profiles	0003_remove_organization_mission_vision_and_more	2025-06-01 20:17:12.153292+00
48	funding	0003_remove_fundingallocation_funding_fun_funding_ef246e_idx_and_more	2025-06-02 08:28:28.850451+00
49	funding	0004_remove_fundingallocation_funding_fun_grant_i_330cac_idx_and_more	2025-06-02 08:53:49.683377+00
50	milestones	0001_initial	2025-06-03 08:00:01.940097+00
51	reports	0001_initial	2025-06-03 08:00:01.975144+00
52	resources	0001_initial	2025-06-03 08:00:01.977499+00
53	grants	0003_grantattachmenttype_grantattachment_uploaded_by	2025-06-22 17:57:03.105528+00
54	grants	0004_grantattachment_attachment_type_and_more	2025-06-22 17:57:03.118638+00
\.


--
-- Data for Name: django_session; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.django_session (session_key, session_data, expire_date) FROM stdin;
zety7streic0vjfm2wh1lkpm6knsi46r	.eJxVjMsOwiAQRf-FtSHMIAN16b7fQJgBpGrapI-V8d-1SRe6veec-1IxbWuL21LmOGR1UaBOvxsneZRxB_mextukZRrXeWC9K_qgi-6nXJ7Xw_07aGlp3xqNWKnsqbOJiC0TUiX2IYBz5owA2XoyHSBSCR0DF49ZxAFBFSzq_QG_XDcL:1u4zmv:B76XDW0cPC7sRieU7EQXXxN8T9mG9XAy_PPyKyNEXgw	2025-04-30 10:14:37.176765+00
yv6v0wh9eav2snxzio52krf4jzzjuuan	.eJxVjMsOwiAQRf-FtSHMIAN16b7fQJgBpGrapI-V8d-1SRe6veec-1IxbWuL21LmOGR1UaBOvxsneZRxB_mextukZRrXeWC9K_qgi-6nXJ7Xw_07aGlp3xqNWKnsqbOJiC0TUiX2IYBz5owA2XoyHSBSCR0DF49ZxAFBFSzq_QG_XDcL:1uABH6:DbyfiUaGh0sve0cr52n1vhkhOkVrRVUIyI_F91M8btk	2025-05-14 17:31:12.716915+00
q0ru895qugas2zqurhr9ci8ouu9q3l5j	.eJxVjMsOwiAQRf-FtSHMIAN16b7fQJgBpGrapI-V8d-1SRe6veec-1IxbWuL21LmOGR1UaBOvxsneZRxB_mextukZRrXeWC9K_qgi-6nXJ7Xw_07aGlp3xqNWKnsqbOJiC0TUiX2IYBz5owA2XoyHSBSCR0DF49ZxAFBFSzq_QG_XDcL:1uKD9V:yk8d_q0n6JFg7R9wYG6utqHKCjppa4Tsew5SzXPA6UI	2025-06-11 09:32:49.836454+00
q3b2id2se9miin42e39x85zqecdu195i	.eJxVjMsOwiAQRf-FtSHMIAN16b7fQJgBpGrapI-V8d-1SRe6veec-1IxbWuL21LmOGR1UaBOvxsneZRxB_mextukZRrXeWC9K_qgi-6nXJ7Xw_07aGlp3xqNWKnsqbOJiC0TUiX2IYBz5owA2XoyHSBSCR0DF49ZxAFBFSzq_QG_XDcL:1uLpNz:6_zXNv01zCK87f9qyjscAhJ2B0yDcH7GlvTdixib7-g	2025-06-15 20:34:27.705726+00
\.


--
-- Data for Name: funding_disbursement; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.funding_disbursement (id, scheduled_payment_date, scheduled_amount, received_amount, pending_amount, payment_received_date, grant_id) FROM stdin;
\.


--
-- Data for Name: funding_expense; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.funding_expense (id, particulars, main_header, sub_header, units, frequency, cost_per_unit, activity_description, budget_q1, budget_q2, budget_q3, budget_q4, actual_q1, actual_q2, actual_q3, actual_q4, total_budget, total_grant_budget, total_actual, remarks, expense_date, source_type, status, is_frozen, metadata, file1, file2, file3, file4, grant_id, rejection_notes) FROM stdin;
1	Office Supplies			1.00	1.00	100.00		20.00	20.00	20.00	40.00	20.00	0.00	0.00	0.00	100.00	100.00	20.00		2025-06-03	excel	approved	f	{"edit_history": []}					1	\N
2	Office Supplies			1.00	1.00	180.00		100.00	20.00	20.00	40.00	100.00	0.00	0.00	0.00	180.00	180.00	100.00		2025-06-03	excel	approved	f	{"edit_history": []}					1	\N
3	sasdf	asdfasd	asdfasd	34.00	44.00	3.00	sdfasdf	4.00	4477.00	4.00	3.00	4.00	0.00	0.00	0.00	4488.00	4488.00	4.00		2025-06-03	excel	approved	f	{"edit_history": []}					2	
4	sdfsdf	xxxx	aasdasdf	444.00	4.00	4.00	sdfasdf	2.00	7100.00	1.00	1.00	2.00	0.00	0.00	0.00	7104.00	7104.00	2.00		2025-06-03	excel	approved	f	{"edit_history": []}					2	
5	dsdf	sdfsdfxx	sdf	3.00	4.00	4.00	sdfasdfasdf	1.00	4.00	39.00	4.00	5.00	0.00	0.00	0.00	48.00	48.00	5.00	TEst	2025-06-03	excel	pending	f	{"edit_history": [{"field": "Remarks", "row_id": 3, "new_value": "T", "old_value": "", "timestamp": "2025-06-03T08:06:53.663Z"}, {"field": "Remarks", "row_id": 3, "new_value": "TE", "old_value": "T", "timestamp": "2025-06-03T08:06:53.761Z"}, {"field": "Remarks", "row_id": 3, "new_value": "TEs", "old_value": "TE", "timestamp": "2025-06-03T08:06:53.836Z"}, {"field": "Remarks", "row_id": 3, "new_value": "TEst", "old_value": "TEs", "timestamp": "2025-06-03T08:06:54.664Z"}]}					2	
6	asdfasdf	asdfasd	dasdf	44.00	44.00	4.00	asdf	0.00	7696.00	44.00	4.00	4.00	0.00	0.00	0.00	7744.00	7744.00	4.00	Test	2025-06-03	excel	pending	f	{"edit_history": [{"field": "Remarks", "row_id": 4, "new_value": "T", "old_value": "", "timestamp": "2025-06-03T08:06:56.785Z"}, {"field": "Remarks", "row_id": 4, "new_value": "Te", "old_value": "T", "timestamp": "2025-06-03T08:06:56.863Z"}, {"field": "Remarks", "row_id": 4, "new_value": "Tes", "old_value": "Te", "timestamp": "2025-06-03T08:06:56.963Z"}, {"field": "Remarks", "row_id": 4, "new_value": "Test", "old_value": "Tes", "timestamp": "2025-06-03T08:06:57.065Z"}]}					2	
\.


--
-- Data for Name: funding_fundingallocation; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.funding_fundingallocation (id, amount_allocated, notes, grant_id, funding_entity_id) FROM stdin;
10	1200000.00		1	24
\.


--
-- Data for Name: funding_fundingrecord; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.funding_fundingrecord (id, amount, date_received, notes, funding_entity_id) FROM stdin;
4	50000.00	2025-06-01	asdf	26
3	103000.00	2025-06-17	Byeee	24
5	1000000.00	2025-06-11	asdf	24
6	1000000.00	2025-06-26	asdf	24
\.


--
-- Data for Name: grants_grant; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.grants_grant (id, grant_name, start_date, end_date, grant_purpose, annual_budget, funding_sources, created_at, updated_at, grant_maker_organization_id, organization_id) FROM stdin;
1	Rural Women Livelihood Empowerment Program	2025-04-23	2025-04-23	To enhance the livelihood opportunities for rural women through skill development, micro-enterprise training, and access to market linkages.	1200000.00	CSR funds from development-focused corporates, women's welfare NGOs, and government rural schemes	2025-04-18 09:11:41.590486+00	2025-04-18 09:11:41.590501+00	2	1
2	Youth Skill Development and Entrepreneurship Initiative	2025-04-29	2025-04-29	To provide vocational training, entrepreneurial mentorship, and startup funding support to unemployed rural and semi-urban youth.	750000.00	CSR contributions from tech companies, entrepreneurship promotion agencies, and state youth welfare programs	2025-05-06 07:21:50.923859+00	2025-05-06 07:21:50.923937+00	2	1
3	Grant Program For High-Impact Interventions	2024-11-01	2025-07-01	CSR contributions from tech companies, entrepreneurship promotion agencies, and state youth welfare programs	2680955.00	Svatantra	2025-05-09 07:01:46.268927+00	2025-05-09 07:01:46.268946+00	2	6
5	Grant Program For High-Impact Interventions	2024-11-01	2025-07-01	CSR contributions from tech companies, entrepreneurship promotion agencies, and state youth welfare programs	2300500.00	Svatantra	2025-05-09 07:13:21.888118+00	2025-05-09 07:13:21.888305+00	2	7
7	Grant Program For High-Impact Interventions	2024-11-01	2025-07-01	CSR contributions from tech companies, entrepreneurship promotion agencies, and state youth welfare programs	2253198.00	Svatantra	2025-05-09 07:41:07.58736+00	2025-05-09 07:41:07.587375+00	2	8
8	Grant Program For High-Impact Interventions	2024-11-01	2025-07-01	CSR contributions from tech companies, entrepreneurship promotion agencies, and state youth welfare programs	2400000.00	Svatantra	2025-05-09 07:41:52.900514+00	2025-05-09 07:41:52.900528+00	2	9
9	Grant Program For High-Impact Interventions	2024-11-01	2025-07-01	CSR contributions from tech companies, entrepreneurship promotion agencies, and state youth welfare programs	2890000.00	Svatantra	2025-05-09 07:42:11.686821+00	2025-05-09 07:42:11.686835+00	2	10
\.


--
-- Data for Name: grants_grantattachment; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.grants_grantattachment (id, s3_key, filename, file_type, uploaded_at, description, grant_id, uploaded_by_id, attachment_type_id) FROM stdin;
2	grant-attachments/1/1750617709907-ME_Work_Plan.pdf	ME_Work_Plan.pdf	\N	2025-06-22 18:41:49.993106+00	\N	1	4	34
4	uploads/28008d96-b7c2-413a-8a92-94b616d2c5f2_ME_Work_Plan.pdf	ME_Work_Plan.pdf	\N	2025-06-22 18:53:09.289311+00	\N	1	4	36
3	uploads/ac61ea17-df4c-41d2-b0af-46e281570d45_ME_Work_Plan.pdf	ME_Work_Plan.pdf		2025-06-22 18:51:06.559111+00		1	4	35
5	uploads/e13c1c09-1293-4a23-a082-4cb3561cf5ab_MOU Ashadeep-SV CSR Agreement-full agreement.pdf	MOU Ashadeep-SV CSR Agreement-full agreement.pdf		2025-06-22 19:03:02.086143+00		1	4	37
1	uploads/261ff36d-5e00-49bf-a46d-d0a6e714f4d8_15_Minute_Yoga_Fix_eBook.pdf	15_Minute_Yoga_Fix_eBook.pdf		2025-06-22 18:14:17.339116+00		1	4	1
\.


--
-- Data for Name: grants_grantattachmenttype; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.grants_grantattachmenttype (id, name, code, description) FROM stdin;
1	MOU X CSR	MOU_X_CSR	
34	M&E Framework	M_AND_E_FRAMEWORK	
35	Program Proposal	PROGRAM_PROPOSAL	
36	Program Budgets	PROGRAM_BUDGET	
37	Theory Of Change	THEORY_OF_CHANGE	
38	Program Organogram	PROGRAM_ORGANOGRAM	
39	Gantt Chart	GANTT_CHART	
\.


--
-- Data for Name: grants_organizationgranthistory; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.grants_organizationgranthistory (id, grant_name, grant_purpose, start_date, end_date, budget, status, created_at, updated_at, organization_id) FROM stdin;
7	Hello world	123	2025-06-19	2025-06-27	1000.00	PENDING	2025-06-01 07:15:02.130836+00	2025-06-01 07:15:02.131202+00	1
11	hello world	1	2025-06-01	2025-06-11	1.00	PENDING	2025-06-01 07:21:08.679024+00	2025-06-01 07:21:08.679051+00	1
12	Hello	Bye	2025-06-01	2025-06-01	1.00	PENDING	2025-06-01 07:22:45.40516+00	2025-06-01 07:22:45.40519+00	1
13	a	b	2025-06-01	2025-06-01	1.00	PENDING	2025-06-01 07:24:09.587948+00	2025-06-01 07:24:09.587982+00	1
14	a	b	2025-06-01	2025-06-01	10.00	PENDING	2025-06-01 07:25:15.844419+00	2025-06-01 07:25:15.844453+00	1
\.


--
-- Data for Name: milestones_milestone; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.milestones_milestone (id, description, activity, target_group, target_q1, target_q2, target_q3, target_q4, achieved_q1, achieved_q2, achieved_q3, achieved_q4, cost_per_unit, total_planned_budget, balance, created_at, updated_at, grant_id) FROM stdin;
\.


--
-- Data for Name: milestones_milestonesattachment; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.milestones_milestonesattachment (id, attachment_type, file_path, uploaded_at, milestone_id) FROM stdin;
\.


--
-- Data for Name: milestones_question; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.milestones_question (id, title, created_at, updated_at, questioner_id) FROM stdin;
\.


--
-- Data for Name: profiles_address; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_address (id, address_line_1, address_line_2, locality, city, state, postal_code, country) FROM stdin;
1	E19, Hill View Residency	Urit Nagar, Warje	Warjes	Pune	Maharashtra	411052	India
2	XYZ	ABC	Delhi	Pune	Maharashtra	411052	India
\.


--
-- Data for Name: profiles_grantmakerfunds; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_grantmakerfunds (id, amount, reference_number, currency, remarks, date_received, payment_mode, created_at, added_by_id, funder_id, recipient_id) FROM stdin;
1	50000.00	TXN123456789	INR	Received Q1 funding for community outreach program.	2024-04-15 10:00:00+00	NEFT	2025-05-14 11:50:37.713588+00	6	3	2
\.


--
-- Data for Name: profiles_organization; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_organization (id, organization_name, pan_number, phone_number, email_address, website_url, number_of_team_members, background_history, csr_registration_number, tax_registration_number, tax_registration_number_under_12_a, fcra_registration_number, trust_registration_number, darpan_id, created_at, updated_at, address_id, organization_function_type_id, organization_legal_type_id, created_by_grant_maker_id, point_of_contact_name, budget, logo_key, mission, vision, registered_year, grant_maker_organization_id) FROM stdin;
2	Global Impact Grants	\\x80000000006800bb3ffff2931b6ae18660f8164bf420b06f3efcd2237484e6eef5bc16976951479488f2b7e17f8d08bfd94a8a07e3e3d5e014e54cd0189464691762ffec9d72b8d12f63182e3b5e334680e3ac3758b2706c11	+************	<EMAIL>	https://www.globalimpact.org	40	Established in 2010 to support high-impact NGOs across South Asia.	CSR99887766	TRUST9876543210	12AGLO456789	FCRA998877	TRUST55667788	DARPAN998877	2025-04-16 10:20:11.44287+00	2025-04-17 08:26:39.587367+00	\N	2	1	\N	\N	\N	\N	\N	\N	\N	\N
5	Hope Foundation India	\\x8000000000680f1ef6aa4eb6d0c847c1ef2b444785e226c949f650a0fa8ddc4918a40f1dbf50ff9dd817c264c9564184439e71cb8c915dd1c70d2ad0da4067397dcfc094b708c5d13319b714a7bef4bbc5b2c62cde278c5e65	+************	<EMAIL>	https://www.hopefoundation.org.in	55	Founded in 2008 by a group of social entrepreneurs aiming to bridge gaps in basic human needs across rural India.	CSR12349876	TRUST1234567890	12AHOPE456789	FCRA123456	TRUST11223344	DARPAN123456	2025-04-28 06:23:50.788975+00	2025-04-28 06:23:50.789327+00	\N	1	2	\N	\N	\N	\N	\N	\N	\N	\N
3	Global Impact Grants	\\x80000000006802165db88a1f14e60f533042ae510981dd7837d434a177c7bde83f0d54a827f91dacc6c2b7a47207b2f723e8afd4aaf7432a3f97331c8b1be48711dd0f8f13a53d7cd16c465334554631be2c732c53f8e3dfb7	+************	<EMAIL>	https://www.globalimpact.org	40	Established in 2010 to support high-impact NGOs across South Asia.	CSR99887766	TRUST9876543210	12AGLO456789	FCRA998877	TRUST55667788	DARPAN998877	2025-04-18 09:07:41.254357+00	2025-04-18 09:07:41.254373+00	\N	1	2	\N	\N	\N	\N	\N	\N	\N	\N
4	Education Access Foundation	\\x800000000068127997e2b105efacdb9557506a5c166626b415fc164db30708e0443a545f5f7019f55466561c0f59ae8ddff983d6246dcf445eb1cd5c122ea36a3bd121f53d29d1723f5bbc54f887f6212e290071492e9668de	+************	<EMAIL>	https://www.example.org	10	Founded in 2010 with a mission to make education accessible.	CSR123456	TAX123456	12A123456	FCRA123456	TRUST123456	DARPAN123456	2025-04-24 04:48:15.934466+00	2025-04-30 19:27:19.866032+00	\N	1	1	\N	\N	\N	\N	\N	\N	\N	\N
9	Kranti Foundation	\\x8000000000681dae91756bc08827a178a5254011041914ef5fd40ae2d20d8baad370876d7f8dd888c815913625967effd588bda2de14f509dfed7b16167693b897b109df5e2f613414e6b495283ec42ac0224762aae175db6d	+************	<EMAIL>	https://www.kranti-india.org/	10	Founded in 2010 with a mission to make education accessible.	CSR123456	TAX123456	12A123456	FCRA123456	TRUST123456	DARPAN123456	2025-05-09 07:28:17.391535+00	2025-05-09 07:28:17.391551+00	\N	1	1	\N	\N	\N	\N	\N	\N	\N	\N
6	Ashadeep	\\x8000000000681d9a0810bcce8c20d45fa9cf616319f49228a296962c7de4752d272d2bd958e3a505edbbbb718386b6dc4a315af52f1a839a2c8d6a58c22b2feaf92ef557924fca0d23c6fc9ce75fcc3c87bbecafe8ae72df21	+************	<EMAIL>	https://www.ashadeepindia.org/	82	Ashadeep was established in 1996 with a vision to achieve a life of better quality for persons with mental disorder and their families. Since inception, every intervention facilitated by Ashadeep has evolved keeping in view the ‘contextual needs’ of persons with mental illness and individuals with intellectual disability and associated disorders. The core area of Ashadeep consists of being a service provider in mental health interventions in the community. The various facilities of Ashadeep are community mental health program in rural locations, Special schools in urban and rural locations for children with intellectual disability, rehabilitation homes for homeless persons with mental illness, outdoor composite care units for psychiatric interventions in urban and rural locations.	CSR00033867	TAX123456	12A123456	*********	TRUST123456	AS/2009/0007549	2025-05-09 06:00:40.464399+00	2025-05-09 06:00:40.46442+00	\N	1	3	\N	\N	\N	\N	\N	\N	\N	\N
7	Slam Out Loud (Foundation of Arts for Social Change in India)	\\x8000000000681da9c5a0fe479e740d7db46b297d1f4c0969b3984e35ad78927c6c215ae124d193a3039c1349d7548355a318ebba061e96c61687496bcfc014a623afab2b28f5b5c2fe20c9b869b1b26a540a892741e2be9584	+************	<EMAIL>	https://www.slamoutloud.org	50	"Slam Out Loud (SOL) uses the transformative power of the performance and visual arts to build Creative Confidence (curiosity, analytical thinking, imagination, agency, emotional awareness, and teamwork) while improving children's mental and emotional health in under-resourced communities. We work with teachers and professional artists and create contextual curriculum resources to enable children to build the skills they need to achieve better health, impacting long-term life outcomes.\nOur vision is based on our belief that exposure to and development of arts and creative skills builds children into strong, empowered individuals, who are better equipped because of social emotional learning. We believe that exposure to arts and inculcating SEL (Social Emotional Learning) into school ecosystems will create and develop well-being practices, expressive individuals and strong empowered voices."	CSR00005784	TAX123456	12A123456	FCRA123456	TRUST123456	DARPAN123456	2025-05-09 07:07:49.361907+00	2025-05-09 07:07:49.361924+00	\N	1	1	\N	\N	\N	\N	\N	\N	\N	\N
8	Saahas	\\x8000000000681dad31d9d9981c63a776ceac542ba5e83ea09433287689064340e74bcd217c8ffc4507e986b7d43cb3f8204e8b10404dab5d66fd5344123070903f48523627188cc18d99219a941751117a2cb9b8034cc231e0	+************	<EMAIL>	https://saahas.org/	380	Saahas is a non-profit organization which was set up in 2001 a vision to enable India to become a leading Circular Economy where Nothing is Waste. Saahas focuses on programs designed around the principles of Circular Economy and SWM Rules 2016. Saahas’s work involves piloting innovative resource management programs, collaborating closely with communities, administrators, solution providers, innovators and law makers to evolve next practices for adoption of Circular Economy.	CSR00000097.	TAX123456	12A123456	FCRA123456	TRUST123456	DARPAN123456	2025-05-09 07:22:25.187912+00	2025-05-09 07:22:25.187927+00	\N	1	1	\N	\N	\N	\N	\N	\N	\N	\N
10	TYCIA	\\x8000000000681db0c50d965f6ca7024e81d0c1db22c809f962d9230b9f40318e206b53f7c88688d7b93c5acaa2b2f328f1939d28c65e0adb0b337d95a4a205576b36b0c9163e8ddd8b29091b0396452975fea9faaad5523792	+************	<EMAIL>	https://tyciafoundation.org/	10	Turn Your Concern Into Action (TYCIA) Foundation, is a not-for-profit organization that was founded in the year 2011 out of two college friends’ disquiet at seeing migrant children who were not enrolled in school playing and working in Delhi. Since its' inception, TYCIA has honed a holistic approach to change-making, always keeping special focus on communities at risk which are often ignored. From tribal farmer training programs to tailored educational solutions for incarcerated youth, TYCIA is committed to empower the most vulnerable sections of society through providing equitable and quality avenues to bridge society's gaps.	CSR123456	TAX123456	12A123456	FCRA123456	TRUST123456	DARPAN123456	2025-05-09 07:37:41.232107+00	2025-05-09 07:37:41.232122+00	\N	1	1	\N	\N	\N	\N	\N	\N	\N	\N
12	ABCD	\\x80000000006824342bcce1171922145c8f13f69479922c43498e68dcfc9686e16d754b6452a2bf6a9fbe8d4a12bdf11ddb81d9baf649e0b6c70a107cc09ade190d78e1eb253f0f5c46e56eef52b49f0dc792c93a85ecd5d9ed	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	2025-05-14 06:11:55.866284+00	2025-05-14 06:11:55.866309+00	\N	4	\N	\N	\N	\N	\N	\N	\N	\N	\N
13	ABCD	\\x80000000006824377fd7f5064ada5bb5c7ef9fd35a16b9e127268307bce4cd26d23ae4b8fae60c433ba23039113851d89424608d2de170d18c3d8b8a9d84fd65eb7fb0159ea3aabcff7cdd09509df2c5c364e99420e9706092	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	2025-05-14 06:26:07.148346+00	2025-05-14 06:26:07.14836+00	\N	4	\N	\N	\N	\N	\N	\N	\N	\N	\N
14	ABCSD	\\x8000000000682437df961d4107ee8b0544e19b2936eeba25810928ddceb4b28fc8f8ccf31810c7230c6b16758d0feebcb6b5e6506836d8902258e2662dd19090cd630e12e5ecd33a4a70af6ba77e975e96492929dbe7774d1b	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	2025-05-14 06:27:43.563174+00	2025-05-14 06:27:43.563185+00	\N	4	\N	\N	\N	\N	\N	\N	\N	\N	\N
15	Hello world	\\x800000000068243a1f73dc20803abbcab61b1823f39adb98387a8b938c176cdb7ab44b5eec44e5b0bf6bdea6e1302e4eb2b63e74cda8b343bf944fbbfd5efeb8f120b31bcc81d582c1048b278e53f57b4e514ae597803525bc	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	2025-05-14 06:37:19.400887+00	2025-05-14 06:37:19.400898+00	\N	4	\N	\N	\N	\N	\N	\N	\N	\N	\N
1	Tech Info Foundation	\\x800000000068393f921b53e954a651cd7e600d28b0233810023e4ddc33b73c1b30511bd2ac7dbafc90dc1e3287ebb18a3b3f4cdc1ea4d27c4672a07510233d6420133fb18a5a1c47607f52e376e9b323d803c316b55ab04785	+919876543211	<EMAIL>	https://www.techforchange.org	15	Education Access Foundation	CSR12345674	TAX98765432	TAX98765432	FCRA1234563	12334643	DARPAN1234566	2025-04-16 10:17:15.403649+00	2025-05-30 05:18:10.445151+00	1	1	1	\N	No name here please	100	logos/org_1_logo.png	Hello world	This is vision	2011	\N
24	Somw	\\x8000000000683cdaded655622ee9a6495afb8336f55e2aa23cdc278bf57ba96c12db86e52a6c36c6d93d3c6ca32bc7dff33d99ae3b57700325f59d8df39c82cc9ed5fa515ca6407878c1e78b138291d0390b63c882175faf56		\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	2025-06-01 21:28:45.54519+00	2025-06-01 22:57:34.409368+00	\N	4	\N	\N	\N	\N	\N	\N	\N	\N	2
26	Next one11	\\x8000000000683cdae3c7e49c0dbf435406f6b77dee4c3787d74b19c16be2e90262e17ddfa24e35e5ce94a2200d3a5b9be3454b57cefdf82d0b97f59c8ab2d572648915f5d90c949d5cb3490b79d458501d56366654a7beab44		\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	2025-06-01 22:11:02.838404+00	2025-06-01 22:57:39.807845+00	\N	4	\N	\N	\N	\N	\N	\N	\N	\N	2
27	Aditya Birla Foundation	\\x5c783431343234333434343533313332333333343436	+91-9876543210	<EMAIL>	https://www.greenfuture.org	15	Founded in 2010, the foundation has worked with over 50 rural communities on clean energy and water projects.	CSR1234567890	**********	12A123456	FCRA987654321	TRUST654321	DARPAN001122	2025-06-23 06:56:52.226672+00	2025-06-23 06:56:52.226672+00	\N	1	1	\N	\N	\N	\N	To promote sustainable development and environmental awareness across India.		\N	\N
28	Ananya Birla Foundation	\\x800000000068590219da7635da6165f0fb062bb414d1f92f4aba2b2285566b6f812f00fd54dd12363e47e9e9217e54e2cc090aa2b9ded8dfac86a1dbf03b8bf53822e56e918441c8d9262a9f4aac530f5b543ec14f7e64d44b	+************	<EMAIL>	https://www.hopefoundation.org.in	55	Founded in 2008 by a group of social entrepreneurs aiming to bridge gaps in basic human needs across rural India.	CSR12349876	TRUST1234567890	12AHOPE456789	FCRA123456	TRUST11223344	DARPAN123456	2025-06-23 07:24:47.127796+00	2025-06-23 07:28:25.210135+00	2	1	2	\N	Shubhi	\N	\N	This is mission	This is vision	\N	\N
\.


--
-- Data for Name: profiles_organizationattachment; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_organizationattachment (id, original_filename, remarks, object_key, uploaded_at, attachment_type_id, organization_id, status_id, uploaded_by_id) FROM stdin;
29	~`213*{}.pdf	\N	uploads/b3395b9d-29e7-46fa-bd32-c7fd4ec98fa6_~`213*{}.pdf	2025-05-12 16:34:39.72727+00	7	1	1	4
30	Bills | Billing and Cost Management | Global.pdf	\N	uploads/4ddc86b7-9643-4478-bd03-5cbc239f3585_Bills | Billing and Cost Management | Global.pdf	2025-05-12 16:34:53.735375+00	1	1	1	4
20	~`213*{}.pdf		uploads/c25d16b1-7a4e-4f3c-80e9-cdc39092489d_~`213*{}.pdf	2025-05-12 16:06:44.788378+00	4	1	2	4
31	Bills | Billing and Cost Management | Global.pdf	\N	uploads/e89407c5-c3b3-40b5-9402-efa95629f75c_Bills | Billing and Cost Management | Global.pdf	2025-05-13 07:22:02.259409+00	8	1	1	4
7	MOU Ashadeep-SV CSR Agreement-full agreement.pdf	\N	uploads/95bcb203-713c-49d0-966f-bcb60aa09658_~`213*{}.pdf	2025-04-28 09:18:10.514526+00	6	6	1	4
6	Ashadeep CSR Certificate.PDF	\N	uploads/caa17298-6678-4047-9410-07cc25a3b092_~`213*{}.pdf	2025-04-28 09:15:57.814084+00	5	6	1	4
9	Ashadeep 12A original_renewed.pdf	\N	uploads/d8a05ffd-c7b0-4ace-bbe5-d56c305ee7b3_~`213*{}.pdf	2025-04-28 09:51:10.989147+00	8	6	1	4
8	Ashadeep 80G Certificate.pdf	\N	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf	2025-04-28 09:51:01.563784+00	7	6	1	4
1	Bills | Billing and Cost Management | Global.pdf		uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf	2025-04-16 13:10:29.440736+00	4	6	1	\N
32	MOU Ashadeep-SV CSR Agreement-full agreement.pdf	\N	uploads/39cd74c4-6aaa-4648-9836-0cd54d430685_MOU Ashadeep-SV CSR Agreement-full agreement.pdf	2025-05-22 08:58:50.266895+00	5	1	1	4
33	15_Minute_Yoga_Fix_eBook.pdf	\N	uploads/1ee9f68c-ffa6-4f1f-8867-5add7121718d_15_Minute_Yoga_Fix_eBook.pdf	2025-05-29 06:17:16.929553+00	6	1	1	4
\.


--
-- Data for Name: profiles_organizationattachmenthistory; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_organizationattachmenthistory (id, object_key, remarks, uploaded_at, attachment_id, status_id, uploaded_by_id) FROM stdin;
1	uploads/123.pdf	\N	2025-04-25 09:18:48.311892+00	1	1	4
2	uploads/12332.pdf	\N	2025-04-25 09:18:57.350543+00	1	1	4
3	uploads/12332.pdf	\N	2025-04-28 08:40:32.990717+00	1	1	4
4	uploads/cbeea597-c600-43df-a97c-746f9dec0209_~`213*{}.pdf	\N	2025-04-28 09:02:45.547121+00	1	1	4
5	uploads/85ff5e61-7b51-4f65-87dd-6006c5c7a7e3_~`213*{}.pdf	\N	2025-04-28 09:03:34.131428+00	1	1	4
6	uploads/918b8bdb-1e3e-4f08-b38b-93e0ef70ff03_~`213*{}.pdf	\N	2025-04-28 09:03:59.854869+00	1	1	4
7	uploads/2bc5c053-640e-4c2c-aadd-a586437c5fdd_~`213*{}.pdf	\N	2025-04-28 09:04:23.417076+00	1	1	4
8	uploads/415017ed-eaad-49a8-bc87-f5d28b6a6670_~`213*{}.pdf	\N	2025-04-28 09:07:28.060934+00	1	1	4
9	uploads/0d640fed-b68a-4b0b-ba01-785cc79e2bd1_~`213*{}.pdf	\N	2025-04-28 09:07:50.868638+00	1	1	4
10	uploads/13c47aee-4fbd-47d4-8b61-66454d8c8979_~`213*{}.pdf	\N	2025-04-28 09:08:35.509153+00	1	1	4
11	uploads/2e0da0b4-5c5b-4007-9559-a65e0e08297a_~`213*{}.pdf	\N	2025-04-28 09:08:47.896146+00	1	1	4
12	uploads/12332.pdf	\N	2025-04-28 09:09:20.642661+00	1	1	4
13	uploads/caa17298-6678-4047-9410-07cc25a3b092_~`213*{}.pdf	\N	2025-04-28 09:15:57.844184+00	6	1	4
14	uploads/f2ef4404-7292-49ca-ada9-3ac07e1d5d44_~`213*{}.pdf	\N	2025-04-28 09:18:10.534895+00	7	1	4
15	uploads/95bcb203-713c-49d0-966f-bcb60aa09658_~`213*{}.pdf	\N	2025-04-28 09:49:11.241036+00	7	1	4
16	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf	\N	2025-04-28 09:51:01.593117+00	8	1	4
17	uploads/d8a05ffd-c7b0-4ace-bbe5-d56c305ee7b3_~`213*{}.pdf	\N	2025-04-28 09:51:11.013408+00	9	1	4
19	uploads/12332.pdf	Have rejected it	2025-05-09 08:46:35.37565+00	1	3	4
20	uploads/95bcb203-713c-49d0-966f-bcb60aa09658_~`213*{}.pdf	\N	2025-05-09 08:47:22.584587+00	7	2	4
21	uploads/caa17298-6678-4047-9410-07cc25a3b092_~`213*{}.pdf	\N	2025-05-09 08:52:33.685224+00	6	2	4
27	uploads/caa17298-6678-4047-9410-07cc25a3b092_~`213*{}.pdf	\N	2025-05-09 09:12:46.252344+00	6	2	8
29	uploads/caa17298-6678-4047-9410-07cc25a3b092_~`213*{}.pdf	\N	2025-05-09 09:22:57.999111+00	6	2	8
30	uploads/12332.pdf	Have rejected it	2025-05-09 09:25:25.251818+00	1	3	8
31	uploads/95bcb203-713c-49d0-966f-bcb60aa09658_~`213*{}.pdf	\N	2025-05-09 09:25:58.750864+00	7	1	8
32	uploads/12332.pdf	Have rejected it	2025-05-09 09:28:36.86932+00	1	3	8
33	uploads/caa17298-6678-4047-9410-07cc25a3b092_~`213*{}.pdf	\N	2025-05-09 09:45:16.189532+00	6	1	8
34	uploads/d8a05ffd-c7b0-4ace-bbe5-d56c305ee7b3_~`213*{}.pdf	\N	2025-05-09 09:45:30.469587+00	9	1	8
35	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf	\N	2025-05-09 09:45:37.647513+00	8	1	8
36	uploads/12332.pdf	Have rejected it	2025-05-09 10:00:29.699519+00	1	3	8
37	uploads/12332.pdf	Have rejected it	2025-05-09 10:02:24.001521+00	1	3	8
38	uploads/12332.pdf	Have rejected it	2025-05-09 10:03:14.794667+00	1	3	8
39	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:07:29.385128+00	1	1	8
40	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:07:46.158196+00	1	1	8
41	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:11:19.281751+00	1	1	8
42	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:19:00.861714+00	1	1	8
43	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:20:32.305638+00	1	1	8
44	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:22:16.434151+00	1	1	8
45	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:24:22.094571+00	1	1	8
46	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:26:14.763656+00	1	1	8
47	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:26:31.291041+00	1	1	8
48	uploads/fa1a60a4-4f6e-4e91-9ce1-60dc1feb4d9a_~`213*{}.pdf		2025-05-09 10:28:02.801953+00	1	1	8
53	abcd		2025-05-12 16:28:16.142124+00	20	1	4
54	uploads/c25d16b1-7a4e-4f3c-80e9-cdc39092489d_~`213*{}.pdf		2025-05-12 16:32:59.613271+00	20	1	4
55	uploads/86d25e0b-407b-4001-9314-20112f7a42b5_Bills | Billing and Cost Management | Global.pdf	\N	2025-05-12 16:34:39.743874+00	29	1	4
56	uploads/b3395b9d-29e7-46fa-bd32-c7fd4ec98fa6_~`213*{}.pdf	\N	2025-05-12 16:34:46.561056+00	29	1	4
57	uploads/4ddc86b7-9643-4478-bd03-5cbc239f3585_Bills | Billing and Cost Management | Global.pdf	\N	2025-05-12 16:34:53.751562+00	30	1	4
58	uploads/e89407c5-c3b3-40b5-9402-efa95629f75c_Bills | Billing and Cost Management | Global.pdf	\N	2025-05-13 07:22:02.291312+00	31	1	4
59	uploads/39cd74c4-6aaa-4648-9836-0cd54d430685_MOU Ashadeep-SV CSR Agreement-full agreement.pdf	\N	2025-05-22 08:58:50.291304+00	32	1	4
60	uploads/1ee9f68c-ffa6-4f1f-8867-5add7121718d_15_Minute_Yoga_Fix_eBook.pdf	\N	2025-05-29 06:17:16.952624+00	33	1	4
\.


--
-- Data for Name: profiles_organizationattachmentstatus; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_organizationattachmentstatus (id, code, name, description) FROM stdin;
1	PENDING	Pending Review	Waiting for verification
2	VERIFIED	Verified	Document is verified
3	REJECTED	Rejected	Rejected due to issues
\.


--
-- Data for Name: profiles_organizationattachmenttype; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_organizationattachmenttype (id, code, name, description) FROM stdin;
1	ORGANIZATION_REGISTRATION_CERTIFICATE	Organization Registration Certificate	\N
2	TAX_EXEMPTION	Tax Exemption Certificate	\N
3	FCRA_CERTIFICATE	FCRA Certificate	\N
4	PAN_CARD	PAN Card	\N
5	CSR_REGISTRATION	CSR Registration Certificate	\N
6	TRUST_REGISTRATION	Trust Registration Certificate	\N
7	TAX_80G	Tax Exemption Certificate (80G)	\N
8	TAX_12A	Tax Exemption Certificate (12A)	\N
9	INCORPORATION	Certificate of Incorporation	\N
10	CONFLICT_DECLARATION	Conflict of Interest Declaration for BOD	\N
11	THEORY_OF_CHANGE	Theory of Change	\N
12	CSR_FUNDS_DECLARATION	Self Declaration of CSR Funds	\N
13	POSH_POLICY	POSH Policy	\N
14	CHILD_PROTECTION	Child Protection Policy	\N
15	FINANCIAL_STATEMENTS	Financial Statements	\N
16	ANNUAL_REPORTS	Annual Reports	\N
\.


--
-- Data for Name: profiles_organizationfunctiontype; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_organizationfunctiontype (id, code, name, description) FROM stdin;
1	GRANTEE_ORGANIZATION	Grantee Organization	
2	GRANT_MAKER_ORGANIZATION	Grant Maker Organization	
4	FUNDING_ENTITY	Funding Entity	Funding entity
\.


--
-- Data for Name: profiles_organizationkmp; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_organizationkmp (id, name, designation, din, phone_number, email, organization_id) FROM stdin;
6	asd	123	123	asdf	<EMAIL>	1
7	asd	123	123	asdf	<EMAIL>	1
8	asd helo	123	123	asdf	<EMAIL>	1
\.


--
-- Data for Name: profiles_organizationlegaltype; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.profiles_organizationlegaltype (id, code, name, description) FROM stdin;
1	NON_PROFIT	Non-Profit	
2	TRUST	Trust	
3	SOCIETY	Society	
4	SECTION_8	Section 8	Society 8 Company
\.


--
-- Data for Name: questioners; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.questioners (id, title, description, created_at, updated_at, grant_id) FROM stdin;
\.


--
-- Data for Name: reports_narrativereport; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.reports_narrativereport (id, quarter, year, status, remarks, created_at, updated_at, grant_id, organization_id) FROM stdin;
\.


--
-- Data for Name: reports_narrativereportquestion; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.reports_narrativereportquestion (id, question, answer, report_id) FROM stdin;
\.


--
-- Data for Name: reports_reportsgallery; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.reports_reportsgallery (id, title, s3_key, file_name, file_type, uploaded_at, description, organization_id) FROM stdin;
\.


--
-- Data for Name: resources_resource; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.resources_resource (id, title, file_name, file_description, file_uploaded, created_at, uploaded_at) FROM stdin;
\.


--
-- Data for Name: support_historicalticket; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.support_historicalticket (id, category, priority, title, description, email, phone, file, status, created_at, updated_at, history_id, history_date, history_change_reason, history_type, grant_id, history_user_id, point_of_contact_name) FROM stdin;
1	billing	low	Test 1	Some description	<EMAIL>	+911234567890		open	2025-05-08 13:08:16.494898+00	2025-05-08 13:08:16.494942+00	1	2025-05-08 13:08:16.511034+00	\N	+	2	\N	\N
2	financial	medium	Test 2	Desc 2	<EMAIL>	+911234567890		open	2025-05-08 13:38:57.351626+00	2025-05-08 13:38:57.351645+00	2	2025-05-08 13:38:57.363151+00	\N	+	2	\N	\N
3	financial	high	Test 1	Test 2	<EMAIL>	+911234567890		open	2025-05-09 09:29:09.035494+00	2025-05-09 09:29:09.03551+00	3	2025-05-09 09:29:09.0434+00	\N	+	3	\N	\N
3	financial	high	Test 1	Test 2	<EMAIL>	+911234567890		under review	2025-05-09 09:29:09.035494+00	2025-05-09 09:29:09.03551+00	4	2025-05-15 09:17:57.605749+00	\N	~	3	\N	\N
1	billing	low	Test 1	Some description	<EMAIL>	+911234567890		under review	2025-05-08 13:08:16.494898+00	2025-05-08 13:08:16.494942+00	5	2025-05-15 09:18:32.615433+00	\N	~	2	\N	\N
1	billing	low	Test 1	Some description	<EMAIL>	+911234567890		closed	2025-05-08 13:08:16.494898+00	2025-05-15 09:19:22.444914+00	6	2025-05-15 09:19:22.446798+00	\N	~	2	\N	\N
1	billing	low	Test 1	Some description	<EMAIL>	+911234567890		under review	2025-05-08 13:08:16.494898+00	2025-05-15 09:19:52.518562+00	7	2025-05-15 09:19:52.521259+00	\N	~	2	\N	\N
1	billing	low	Test 1	Some description	<EMAIL>	+911234567890		closed	2025-05-08 13:08:16.494898+00	2025-05-15 09:20:21.09548+00	8	2025-05-15 09:20:21.098682+00	\N	~	2	\N	\N
1	billing	low	Test 1	Some description	<EMAIL>	+911234567890		under review	2025-05-08 13:08:16.494898+00	2025-05-15 09:20:24.501636+00	9	2025-05-15 09:20:24.502957+00	\N	~	2	\N	\N
\.


--
-- Data for Name: support_ticket; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.support_ticket (id, category, priority, title, description, email, phone, file, status, created_at, updated_at, grant_id, point_of_contact_name) FROM stdin;
2	financial	medium	Test 2	Desc 2	<EMAIL>	+911234567890		open	2025-05-08 13:38:57.351626+00	2025-05-08 13:38:57.351645+00	2	\N
3	financial	high	Test 1	Test 2	<EMAIL>	+911234567890		under review	2025-05-09 09:29:09.035494+00	2025-05-09 09:29:09.03551+00	3	\N
1	billing	low	Test 1	Some description	<EMAIL>	+911234567890		under review	2025-05-08 13:08:16.494898+00	2025-05-15 09:20:24.501636+00	2	\N
\.


--
-- Data for Name: support_ticketchathistory; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.support_ticketchathistory (id, update_text, file, updated_at, ticket_id, user_id) FROM stdin;
1	hello world		2025-05-15 09:17:57.615103+00	3	6
2	Hello world		2025-05-15 09:18:24.69838+00	1	4
3	Nooo		2025-05-15 09:18:32.617228+00	1	6
4		support-update-attachment/GRANT_2.xlsx	2025-05-15 09:18:40.455198+00	1	6
5		support-update-attachment/MOU_Ashadeep-SV_CSR_Agreement-full_agreement.pdf	2025-05-15 09:19:05.316519+00	1	6
6		support-update-attachment/GRANT_2.xlsx	2025-05-15 09:19:56.65198+00	1	4
7		support-update-attachment/Postman.app.zip	2025-05-15 09:21:10.421199+00	1	4
\.


--
-- Data for Name: users_user; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.users_user (id, cognito_sub, first_name, last_name, email, organization_id, role_id, type_id) FROM stdin;
6	01f3dd2a-8051-70d8-e478-d9c899f47eba	Dhananjay	Panage	<EMAIL>	2	3	3
4	31f39d9a-d0e1-70b1-922d-92c4897327ff	XYZ	ABC	<EMAIL>	1	3	1
7	f1a36d8a-d0d1-7080-d65f-2679ede09c85		P	<EMAIL>	4	3	1
5	b103ed5a-e031-70fe-5028-ef20effd460c	Sarvesh	Atalkar	<EMAIL>	5	3	1
8	0153bd5a-e041-7055-00d1-0872ec35216b	Ashadeep	Society	<EMAIL>	6	3	1
9	9113ad1a-00d1-707e-f82f-8496ad803e07	Nivea	S	<EMAIL>	7	3	1
10	3193fd7a-0081-7080-d023-11612d109de6	Angel	Vinod	<EMAIL>	8	3	1
11	b1537daa-3071-70aa-d185-d63078d8e9a2	Robin	K	<EMAIL>	9	3	1
12	51431d1a-f051-7031-48f5-c5abe9e1d928	Mohit	Raj	<EMAIL>	10	3	1
15	a1b3fd1a-10c1-70fb-068f-133532f1d817	Shubhi	Dwivedi	<EMAIL>	27	3	1
\.


--
-- Data for Name: users_userrole; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.users_userrole (id, code, name, description) FROM stdin;
3	ADMIN	Admin	
\.


--
-- Data for Name: users_usertype; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.users_usertype (id, code, name, description) FROM stdin;
3	GRANT_MAKER	Grant Maker	EY
1	GRANTEE	Grantee	Yes
\.


--
-- Name: auth_group_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.auth_group_id_seq', 1, false);


--
-- Name: auth_group_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.auth_group_permissions_id_seq', 1, false);


--
-- Name: auth_permission_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.auth_permission_id_seq', 176, true);


--
-- Name: auth_user_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.auth_user_groups_id_seq', 1, false);


--
-- Name: auth_user_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.auth_user_id_seq', 1, true);


--
-- Name: auth_user_user_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.auth_user_user_permissions_id_seq', 1, false);


--
-- Name: django_admin_log_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.django_admin_log_id_seq', 12, true);


--
-- Name: django_content_type_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.django_content_type_id_seq', 44, true);


--
-- Name: django_migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.django_migrations_id_seq', 54, true);


--
-- Name: funding_disbursement_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.funding_disbursement_id_seq', 1, false);


--
-- Name: funding_expense_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.funding_expense_id_seq', 6, true);


--
-- Name: funding_fundingallocation_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.funding_fundingallocation_id_seq', 10, true);


--
-- Name: funding_fundingrecord_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.funding_fundingrecord_id_seq', 6, true);


--
-- Name: grants_grant_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.grants_grant_id_seq', 9, true);


--
-- Name: grants_grantattachment_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.grants_grantattachment_id_seq', 5, true);


--
-- Name: grants_grantattachmenttype_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.grants_grantattachmenttype_id_seq', 39, true);


--
-- Name: grants_organizationgranthistory_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.grants_organizationgranthistory_id_seq', 14, true);


--
-- Name: milestones_milestone_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.milestones_milestone_id_seq', 1, false);


--
-- Name: milestones_milestonesattachment_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.milestones_milestonesattachment_id_seq', 1, false);


--
-- Name: milestones_question_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.milestones_question_id_seq', 1, false);


--
-- Name: profiles_address_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_address_id_seq', 2, true);


--
-- Name: profiles_grantmakerfunds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_grantmakerfunds_id_seq', 1, true);


--
-- Name: profiles_organization_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_organization_id_seq', 28, true);


--
-- Name: profiles_organizationattachment_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_organizationattachment_id_seq', 33, true);


--
-- Name: profiles_organizationattachmenthistory_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_organizationattachmenthistory_id_seq', 60, true);


--
-- Name: profiles_organizationattachmentstatus_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_organizationattachmentstatus_id_seq', 3, true);


--
-- Name: profiles_organizationattachmenttype_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_organizationattachmenttype_id_seq', 16, true);


--
-- Name: profiles_organizationfunctiontype_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_organizationfunctiontype_id_seq', 4, true);


--
-- Name: profiles_organizationkmp_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_organizationkmp_id_seq', 8, true);


--
-- Name: profiles_organizationlegaltype_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.profiles_organizationlegaltype_id_seq', 4, true);


--
-- Name: questioners_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.questioners_id_seq', 1, false);


--
-- Name: reports_narrativereport_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.reports_narrativereport_id_seq', 1, false);


--
-- Name: reports_narrativereportquestion_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.reports_narrativereportquestion_id_seq', 1, false);


--
-- Name: reports_reportsgallery_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.reports_reportsgallery_id_seq', 1, false);


--
-- Name: resources_resource_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.resources_resource_id_seq', 1, false);


--
-- Name: support_historicalticket_history_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.support_historicalticket_history_id_seq', 9, true);


--
-- Name: support_ticket_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.support_ticket_id_seq', 3, true);


--
-- Name: support_ticketchathistory_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.support_ticketchathistory_id_seq', 7, true);


--
-- Name: users_user_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.users_user_id_seq', 16, true);


--
-- Name: users_userrole_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.users_userrole_id_seq', 3, true);


--
-- Name: users_usertype_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.users_usertype_id_seq', 3, true);


--
-- Name: auth_group auth_group_name_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_group
    ADD CONSTRAINT auth_group_name_key UNIQUE (name);


--
-- Name: auth_group_permissions auth_group_permissions_group_id_permission_id_0cd325b0_uniq; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_group_permissions
    ADD CONSTRAINT auth_group_permissions_group_id_permission_id_0cd325b0_uniq UNIQUE (group_id, permission_id);


--
-- Name: auth_group_permissions auth_group_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_group_permissions
    ADD CONSTRAINT auth_group_permissions_pkey PRIMARY KEY (id);


--
-- Name: auth_group auth_group_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_group
    ADD CONSTRAINT auth_group_pkey PRIMARY KEY (id);


--
-- Name: auth_permission auth_permission_content_type_id_codename_01ab375a_uniq; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_permission
    ADD CONSTRAINT auth_permission_content_type_id_codename_01ab375a_uniq UNIQUE (content_type_id, codename);


--
-- Name: auth_permission auth_permission_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_permission
    ADD CONSTRAINT auth_permission_pkey PRIMARY KEY (id);


--
-- Name: auth_user_groups auth_user_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user_groups
    ADD CONSTRAINT auth_user_groups_pkey PRIMARY KEY (id);


--
-- Name: auth_user_groups auth_user_groups_user_id_group_id_94350c0c_uniq; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user_groups
    ADD CONSTRAINT auth_user_groups_user_id_group_id_94350c0c_uniq UNIQUE (user_id, group_id);


--
-- Name: auth_user auth_user_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user
    ADD CONSTRAINT auth_user_pkey PRIMARY KEY (id);


--
-- Name: auth_user_user_permissions auth_user_user_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user_user_permissions
    ADD CONSTRAINT auth_user_user_permissions_pkey PRIMARY KEY (id);


--
-- Name: auth_user_user_permissions auth_user_user_permissions_user_id_permission_id_14a6b632_uniq; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user_user_permissions
    ADD CONSTRAINT auth_user_user_permissions_user_id_permission_id_14a6b632_uniq UNIQUE (user_id, permission_id);


--
-- Name: auth_user auth_user_username_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user
    ADD CONSTRAINT auth_user_username_key UNIQUE (username);


--
-- Name: django_admin_log django_admin_log_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.django_admin_log
    ADD CONSTRAINT django_admin_log_pkey PRIMARY KEY (id);


--
-- Name: django_content_type django_content_type_app_label_model_76bd3d3b_uniq; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.django_content_type
    ADD CONSTRAINT django_content_type_app_label_model_76bd3d3b_uniq UNIQUE (app_label, model);


--
-- Name: django_content_type django_content_type_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.django_content_type
    ADD CONSTRAINT django_content_type_pkey PRIMARY KEY (id);


--
-- Name: django_migrations django_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.django_migrations
    ADD CONSTRAINT django_migrations_pkey PRIMARY KEY (id);


--
-- Name: django_session django_session_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.django_session
    ADD CONSTRAINT django_session_pkey PRIMARY KEY (session_key);


--
-- Name: funding_disbursement funding_disbursement_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_disbursement
    ADD CONSTRAINT funding_disbursement_pkey PRIMARY KEY (id);


--
-- Name: funding_expense funding_expense_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_expense
    ADD CONSTRAINT funding_expense_pkey PRIMARY KEY (id);


--
-- Name: funding_fundingallocation funding_fundingallocation_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_fundingallocation
    ADD CONSTRAINT funding_fundingallocation_pkey PRIMARY KEY (id);


--
-- Name: funding_fundingrecord funding_fundingrecord_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_fundingrecord
    ADD CONSTRAINT funding_fundingrecord_pkey PRIMARY KEY (id);


--
-- Name: grants_grant grants_grant_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grant
    ADD CONSTRAINT grants_grant_pkey PRIMARY KEY (id);


--
-- Name: grants_grantattachment grants_grantattachment_grant_id_attachment_type_00f86e7d_uniq; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grantattachment
    ADD CONSTRAINT grants_grantattachment_grant_id_attachment_type_00f86e7d_uniq UNIQUE (grant_id, attachment_type_id);


--
-- Name: grants_grantattachment grants_grantattachment_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grantattachment
    ADD CONSTRAINT grants_grantattachment_pkey PRIMARY KEY (id);


--
-- Name: grants_grantattachmenttype grants_grantattachmenttype_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grantattachmenttype
    ADD CONSTRAINT grants_grantattachmenttype_pkey PRIMARY KEY (id);


--
-- Name: grants_organizationgranthistory grants_organizationgranthistory_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_organizationgranthistory
    ADD CONSTRAINT grants_organizationgranthistory_pkey PRIMARY KEY (id);


--
-- Name: milestones_milestone milestones_milestone_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.milestones_milestone
    ADD CONSTRAINT milestones_milestone_pkey PRIMARY KEY (id);


--
-- Name: milestones_milestonesattachment milestones_milestonesattachment_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.milestones_milestonesattachment
    ADD CONSTRAINT milestones_milestonesattachment_pkey PRIMARY KEY (id);


--
-- Name: milestones_question milestones_question_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.milestones_question
    ADD CONSTRAINT milestones_question_pkey PRIMARY KEY (id);


--
-- Name: profiles_address profiles_address_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_address
    ADD CONSTRAINT profiles_address_pkey PRIMARY KEY (id);


--
-- Name: profiles_grantmakerfunds profiles_grantmakerfunds_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_grantmakerfunds
    ADD CONSTRAINT profiles_grantmakerfunds_pkey PRIMARY KEY (id);


--
-- Name: profiles_organization profiles_organization_address_id_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organization
    ADD CONSTRAINT profiles_organization_address_id_key UNIQUE (address_id);


--
-- Name: profiles_organization profiles_organization_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organization
    ADD CONSTRAINT profiles_organization_pkey PRIMARY KEY (id);


--
-- Name: profiles_organizationattachment profiles_organizationattachment_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachment
    ADD CONSTRAINT profiles_organizationattachment_pkey PRIMARY KEY (id);


--
-- Name: profiles_organizationattachmenthistory profiles_organizationattachmenthistory_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachmenthistory
    ADD CONSTRAINT profiles_organizationattachmenthistory_pkey PRIMARY KEY (id);


--
-- Name: profiles_organizationattachmentstatus profiles_organizationattachmentstatus_code_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachmentstatus
    ADD CONSTRAINT profiles_organizationattachmentstatus_code_key UNIQUE (code);


--
-- Name: profiles_organizationattachmentstatus profiles_organizationattachmentstatus_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachmentstatus
    ADD CONSTRAINT profiles_organizationattachmentstatus_pkey PRIMARY KEY (id);


--
-- Name: profiles_organizationattachmenttype profiles_organizationattachmenttype_code_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachmenttype
    ADD CONSTRAINT profiles_organizationattachmenttype_code_key UNIQUE (code);


--
-- Name: profiles_organizationattachmenttype profiles_organizationattachmenttype_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachmenttype
    ADD CONSTRAINT profiles_organizationattachmenttype_pkey PRIMARY KEY (id);


--
-- Name: profiles_organizationfunctiontype profiles_organizationfunctiontype_code_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationfunctiontype
    ADD CONSTRAINT profiles_organizationfunctiontype_code_key UNIQUE (code);


--
-- Name: profiles_organizationfunctiontype profiles_organizationfunctiontype_name_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationfunctiontype
    ADD CONSTRAINT profiles_organizationfunctiontype_name_key UNIQUE (name);


--
-- Name: profiles_organizationfunctiontype profiles_organizationfunctiontype_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationfunctiontype
    ADD CONSTRAINT profiles_organizationfunctiontype_pkey PRIMARY KEY (id);


--
-- Name: profiles_organizationkmp profiles_organizationkmp_email_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationkmp
    ADD CONSTRAINT profiles_organizationkmp_email_key UNIQUE (email);


--
-- Name: profiles_organizationkmp profiles_organizationkmp_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationkmp
    ADD CONSTRAINT profiles_organizationkmp_pkey PRIMARY KEY (id);


--
-- Name: profiles_organizationlegaltype profiles_organizationlegaltype_code_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationlegaltype
    ADD CONSTRAINT profiles_organizationlegaltype_code_key UNIQUE (code);


--
-- Name: profiles_organizationlegaltype profiles_organizationlegaltype_name_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationlegaltype
    ADD CONSTRAINT profiles_organizationlegaltype_name_key UNIQUE (name);


--
-- Name: profiles_organizationlegaltype profiles_organizationlegaltype_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationlegaltype
    ADD CONSTRAINT profiles_organizationlegaltype_pkey PRIMARY KEY (id);


--
-- Name: questioners questioners_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.questioners
    ADD CONSTRAINT questioners_pkey PRIMARY KEY (id);


--
-- Name: reports_narrativereport reports_narrativereport_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.reports_narrativereport
    ADD CONSTRAINT reports_narrativereport_pkey PRIMARY KEY (id);


--
-- Name: reports_narrativereportquestion reports_narrativereportquestion_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.reports_narrativereportquestion
    ADD CONSTRAINT reports_narrativereportquestion_pkey PRIMARY KEY (id);


--
-- Name: reports_reportsgallery reports_reportsgallery_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.reports_reportsgallery
    ADD CONSTRAINT reports_reportsgallery_pkey PRIMARY KEY (id);


--
-- Name: resources_resource resources_resource_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.resources_resource
    ADD CONSTRAINT resources_resource_pkey PRIMARY KEY (id);


--
-- Name: support_historicalticket support_historicalticket_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.support_historicalticket
    ADD CONSTRAINT support_historicalticket_pkey PRIMARY KEY (history_id);


--
-- Name: support_ticket support_ticket_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.support_ticket
    ADD CONSTRAINT support_ticket_pkey PRIMARY KEY (id);


--
-- Name: support_ticketchathistory support_ticketchathistory_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.support_ticketchathistory
    ADD CONSTRAINT support_ticketchathistory_pkey PRIMARY KEY (id);


--
-- Name: profiles_organizationattachment unique_attachment_per_org_per_attachment_type; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachment
    ADD CONSTRAINT unique_attachment_per_org_per_attachment_type UNIQUE (organization_id, attachment_type_id);


--
-- Name: reports_narrativereport unique_report_per_org_grant_quarter; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.reports_narrativereport
    ADD CONSTRAINT unique_report_per_org_grant_quarter UNIQUE (organization_id, grant_id, year, quarter);


--
-- Name: users_user users_user_cognito_sub_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_user
    ADD CONSTRAINT users_user_cognito_sub_key UNIQUE (cognito_sub);


--
-- Name: users_user users_user_email_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_user
    ADD CONSTRAINT users_user_email_key UNIQUE (email);


--
-- Name: users_user users_user_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_user
    ADD CONSTRAINT users_user_pkey PRIMARY KEY (id);


--
-- Name: users_userrole users_userrole_code_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_userrole
    ADD CONSTRAINT users_userrole_code_key UNIQUE (code);


--
-- Name: users_userrole users_userrole_name_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_userrole
    ADD CONSTRAINT users_userrole_name_key UNIQUE (name);


--
-- Name: users_userrole users_userrole_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_userrole
    ADD CONSTRAINT users_userrole_pkey PRIMARY KEY (id);


--
-- Name: users_usertype users_usertype_code_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_usertype
    ADD CONSTRAINT users_usertype_code_key UNIQUE (code);


--
-- Name: users_usertype users_usertype_name_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_usertype
    ADD CONSTRAINT users_usertype_name_key UNIQUE (name);


--
-- Name: users_usertype users_usertype_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_usertype
    ADD CONSTRAINT users_usertype_pkey PRIMARY KEY (id);


--
-- Name: auth_group_name_a6ea08ec_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_group_name_a6ea08ec_like ON public.auth_group USING btree (name varchar_pattern_ops);


--
-- Name: auth_group_permissions_group_id_b120cbf9; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_group_permissions_group_id_b120cbf9 ON public.auth_group_permissions USING btree (group_id);


--
-- Name: auth_group_permissions_permission_id_84c5c92e; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_group_permissions_permission_id_84c5c92e ON public.auth_group_permissions USING btree (permission_id);


--
-- Name: auth_permission_content_type_id_2f476e4b; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_permission_content_type_id_2f476e4b ON public.auth_permission USING btree (content_type_id);


--
-- Name: auth_user_groups_group_id_97559544; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_user_groups_group_id_97559544 ON public.auth_user_groups USING btree (group_id);


--
-- Name: auth_user_groups_user_id_6a12ed8b; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_user_groups_user_id_6a12ed8b ON public.auth_user_groups USING btree (user_id);


--
-- Name: auth_user_user_permissions_permission_id_1fbb5f2c; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_user_user_permissions_permission_id_1fbb5f2c ON public.auth_user_user_permissions USING btree (permission_id);


--
-- Name: auth_user_user_permissions_user_id_a95ead1b; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_user_user_permissions_user_id_a95ead1b ON public.auth_user_user_permissions USING btree (user_id);


--
-- Name: auth_user_username_6821ab7c_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX auth_user_username_6821ab7c_like ON public.auth_user USING btree (username varchar_pattern_ops);


--
-- Name: django_admin_log_content_type_id_c4bce8eb; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX django_admin_log_content_type_id_c4bce8eb ON public.django_admin_log USING btree (content_type_id);


--
-- Name: django_admin_log_user_id_c564eba6; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX django_admin_log_user_id_c564eba6 ON public.django_admin_log USING btree (user_id);


--
-- Name: django_session_expire_date_a5c62663; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX django_session_expire_date_a5c62663 ON public.django_session USING btree (expire_date);


--
-- Name: django_session_session_key_c0390e0f_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX django_session_session_key_c0390e0f_like ON public.django_session USING btree (session_key varchar_pattern_ops);


--
-- Name: funding_dis_grant_i_4287c9_idx; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_dis_grant_i_4287c9_idx ON public.funding_disbursement USING btree (grant_id);


--
-- Name: funding_dis_payment_7edb92_idx; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_dis_payment_7edb92_idx ON public.funding_disbursement USING btree (payment_received_date);


--
-- Name: funding_dis_schedul_012f7f_idx; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_dis_schedul_012f7f_idx ON public.funding_disbursement USING btree (scheduled_payment_date);


--
-- Name: funding_disbursement_grant_id_57cbfd2c; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_disbursement_grant_id_57cbfd2c ON public.funding_disbursement USING btree (grant_id);


--
-- Name: funding_expense_grant_id_2adef1b7; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_expense_grant_id_2adef1b7 ON public.funding_expense USING btree (grant_id);


--
-- Name: funding_fun_funding_4055a3_idx; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_fun_funding_4055a3_idx ON public.funding_fundingrecord USING btree (funding_entity_id, date_received);


--
-- Name: funding_fun_grant_i_e3db3b_idx; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_fun_grant_i_e3db3b_idx ON public.funding_fundingallocation USING btree (grant_id, funding_entity_id);


--
-- Name: funding_fundingallocation_funding_entity_id_6edd46ae; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_fundingallocation_funding_entity_id_6edd46ae ON public.funding_fundingallocation USING btree (funding_entity_id);


--
-- Name: funding_fundingallocation_grant_id_2c5530fd; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_fundingallocation_grant_id_2c5530fd ON public.funding_fundingallocation USING btree (grant_id);


--
-- Name: funding_fundingrecord_funding_entity_id_eb322df8; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX funding_fundingrecord_funding_entity_id_eb322df8 ON public.funding_fundingrecord USING btree (funding_entity_id);


--
-- Name: grants_grant_grant_maker_organization_id_7885a99a; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX grants_grant_grant_maker_organization_id_7885a99a ON public.grants_grant USING btree (grant_maker_organization_id);


--
-- Name: grants_grant_organization_id_ec19d162; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX grants_grant_organization_id_ec19d162 ON public.grants_grant USING btree (organization_id);


--
-- Name: grants_grantattachment_attachment_type_id_234fedc1; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX grants_grantattachment_attachment_type_id_234fedc1 ON public.grants_grantattachment USING btree (attachment_type_id);


--
-- Name: grants_grantattachment_grant_id_1d2112d4; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX grants_grantattachment_grant_id_1d2112d4 ON public.grants_grantattachment USING btree (grant_id);


--
-- Name: grants_grantattachment_uploaded_by_id_f9991b2a; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX grants_grantattachment_uploaded_by_id_f9991b2a ON public.grants_grantattachment USING btree (uploaded_by_id);


--
-- Name: grants_organizationgranthistory_organization_id_00662b21; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX grants_organizationgranthistory_organization_id_00662b21 ON public.grants_organizationgranthistory USING btree (organization_id);


--
-- Name: milestones_milestone_grant_id_53cf76ac; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX milestones_milestone_grant_id_53cf76ac ON public.milestones_milestone USING btree (grant_id);


--
-- Name: milestones_milestonesattachment_milestone_id_36ec8c16; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX milestones_milestonesattachment_milestone_id_36ec8c16 ON public.milestones_milestonesattachment USING btree (milestone_id);


--
-- Name: milestones_question_questioner_id_53f3b8a7; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX milestones_question_questioner_id_53f3b8a7 ON public.milestones_question USING btree (questioner_id);


--
-- Name: profiles_grantmakerfunds_added_by_id_942f1c47; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_grantmakerfunds_added_by_id_942f1c47 ON public.profiles_grantmakerfunds USING btree (added_by_id);


--
-- Name: profiles_grantmakerfunds_funder_id_477ce08a; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_grantmakerfunds_funder_id_477ce08a ON public.profiles_grantmakerfunds USING btree (funder_id);


--
-- Name: profiles_grantmakerfunds_recipient_id_e6f6d929; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_grantmakerfunds_recipient_id_e6f6d929 ON public.profiles_grantmakerfunds USING btree (recipient_id);


--
-- Name: profiles_organization_created_by_grant_maker_id_4c6f18a7; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organization_created_by_grant_maker_id_4c6f18a7 ON public.profiles_organization USING btree (created_by_grant_maker_id);


--
-- Name: profiles_organization_grant_maker_organization_id_530280c5; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organization_grant_maker_organization_id_530280c5 ON public.profiles_organization USING btree (grant_maker_organization_id);


--
-- Name: profiles_organization_organization_function_type_id_40a09ad6; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organization_organization_function_type_id_40a09ad6 ON public.profiles_organization USING btree (organization_function_type_id);


--
-- Name: profiles_organization_organization_legal_type_id_20710ab1; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organization_organization_legal_type_id_20710ab1 ON public.profiles_organization USING btree (organization_legal_type_id);


--
-- Name: profiles_organizationattachment_attachment_type_id_f9d8bd54; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachment_attachment_type_id_f9d8bd54 ON public.profiles_organizationattachment USING btree (attachment_type_id);


--
-- Name: profiles_organizationattachment_organization_id_de622725; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachment_organization_id_de622725 ON public.profiles_organizationattachment USING btree (organization_id);


--
-- Name: profiles_organizationattachment_status_id_9c9377f2; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachment_status_id_9c9377f2 ON public.profiles_organizationattachment USING btree (status_id);


--
-- Name: profiles_organizationattachment_uploaded_by_id_202a9ded; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachment_uploaded_by_id_202a9ded ON public.profiles_organizationattachment USING btree (uploaded_by_id);


--
-- Name: profiles_organizationattachmenthistory_attachment_id_35e03c98; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachmenthistory_attachment_id_35e03c98 ON public.profiles_organizationattachmenthistory USING btree (attachment_id);


--
-- Name: profiles_organizationattachmenthistory_status_id_03b21cb5; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachmenthistory_status_id_03b21cb5 ON public.profiles_organizationattachmenthistory USING btree (status_id);


--
-- Name: profiles_organizationattachmenthistory_uploaded_by_id_05e369b9; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachmenthistory_uploaded_by_id_05e369b9 ON public.profiles_organizationattachmenthistory USING btree (uploaded_by_id);


--
-- Name: profiles_organizationattachmentstatus_code_fdb74507_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachmentstatus_code_fdb74507_like ON public.profiles_organizationattachmentstatus USING btree (code varchar_pattern_ops);


--
-- Name: profiles_organizationattachmenttype_code_13d8214d_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationattachmenttype_code_13d8214d_like ON public.profiles_organizationattachmenttype USING btree (code varchar_pattern_ops);


--
-- Name: profiles_organizationfunctiontype_code_5238da2d_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationfunctiontype_code_5238da2d_like ON public.profiles_organizationfunctiontype USING btree (code varchar_pattern_ops);


--
-- Name: profiles_organizationfunctiontype_name_a99d8015_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationfunctiontype_name_a99d8015_like ON public.profiles_organizationfunctiontype USING btree (name varchar_pattern_ops);


--
-- Name: profiles_organizationkmp_email_e2ba9a5b_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationkmp_email_e2ba9a5b_like ON public.profiles_organizationkmp USING btree (email varchar_pattern_ops);


--
-- Name: profiles_organizationkmp_organization_id_0c6ed126; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationkmp_organization_id_0c6ed126 ON public.profiles_organizationkmp USING btree (organization_id);


--
-- Name: profiles_organizationlegaltype_code_c6b6df75_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationlegaltype_code_c6b6df75_like ON public.profiles_organizationlegaltype USING btree (code varchar_pattern_ops);


--
-- Name: profiles_organizationlegaltype_name_3c286e18_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX profiles_organizationlegaltype_name_3c286e18_like ON public.profiles_organizationlegaltype USING btree (name varchar_pattern_ops);


--
-- Name: questioners_grant_id_8a5de9b0; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX questioners_grant_id_8a5de9b0 ON public.questioners USING btree (grant_id);


--
-- Name: reports_narrativereport_grant_id_2da6e832; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX reports_narrativereport_grant_id_2da6e832 ON public.reports_narrativereport USING btree (grant_id);


--
-- Name: reports_narrativereport_organization_id_c81a2523; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX reports_narrativereport_organization_id_c81a2523 ON public.reports_narrativereport USING btree (organization_id);


--
-- Name: reports_narrativereportquestion_report_id_4d9e7c1b; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX reports_narrativereportquestion_report_id_4d9e7c1b ON public.reports_narrativereportquestion USING btree (report_id);


--
-- Name: reports_reportsgallery_organization_id_44033719; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX reports_reportsgallery_organization_id_44033719 ON public.reports_reportsgallery USING btree (organization_id);


--
-- Name: support_historicalticket_grant_id_33564ee4; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX support_historicalticket_grant_id_33564ee4 ON public.support_historicalticket USING btree (grant_id);


--
-- Name: support_historicalticket_history_date_3ea6a585; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX support_historicalticket_history_date_3ea6a585 ON public.support_historicalticket USING btree (history_date);


--
-- Name: support_historicalticket_history_user_id_6bdc4f23; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX support_historicalticket_history_user_id_6bdc4f23 ON public.support_historicalticket USING btree (history_user_id);


--
-- Name: support_historicalticket_id_63acd26c; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX support_historicalticket_id_63acd26c ON public.support_historicalticket USING btree (id);


--
-- Name: support_ticket_grant_id_c80cb603; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX support_ticket_grant_id_c80cb603 ON public.support_ticket USING btree (grant_id);


--
-- Name: support_ticketchathistory_ticket_id_e300ed70; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX support_ticketchathistory_ticket_id_e300ed70 ON public.support_ticketchathistory USING btree (ticket_id);


--
-- Name: support_ticketchathistory_user_id_58805ebf; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX support_ticketchathistory_user_id_58805ebf ON public.support_ticketchathistory USING btree (user_id);


--
-- Name: users_user_cognito_sub_e073dadc_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_user_cognito_sub_e073dadc_like ON public.users_user USING btree (cognito_sub varchar_pattern_ops);


--
-- Name: users_user_email_243f6e77_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_user_email_243f6e77_like ON public.users_user USING btree (email varchar_pattern_ops);


--
-- Name: users_user_organization_id_70643736; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_user_organization_id_70643736 ON public.users_user USING btree (organization_id);


--
-- Name: users_user_role_id_854f2687; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_user_role_id_854f2687 ON public.users_user USING btree (role_id);


--
-- Name: users_user_type_id_2647b62a; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_user_type_id_2647b62a ON public.users_user USING btree (type_id);


--
-- Name: users_userrole_code_d301de57_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_userrole_code_d301de57_like ON public.users_userrole USING btree (code varchar_pattern_ops);


--
-- Name: users_userrole_name_3632e99d_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_userrole_name_3632e99d_like ON public.users_userrole USING btree (name varchar_pattern_ops);


--
-- Name: users_usertype_code_ceab8f11_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_usertype_code_ceab8f11_like ON public.users_usertype USING btree (code varchar_pattern_ops);


--
-- Name: users_usertype_name_2aedbeef_like; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX users_usertype_name_2aedbeef_like ON public.users_usertype USING btree (name varchar_pattern_ops);


--
-- Name: auth_group_permissions auth_group_permissio_permission_id_84c5c92e_fk_auth_perm; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_group_permissions
    ADD CONSTRAINT auth_group_permissio_permission_id_84c5c92e_fk_auth_perm FOREIGN KEY (permission_id) REFERENCES public.auth_permission(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: auth_group_permissions auth_group_permissions_group_id_b120cbf9_fk_auth_group_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_group_permissions
    ADD CONSTRAINT auth_group_permissions_group_id_b120cbf9_fk_auth_group_id FOREIGN KEY (group_id) REFERENCES public.auth_group(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: auth_permission auth_permission_content_type_id_2f476e4b_fk_django_co; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_permission
    ADD CONSTRAINT auth_permission_content_type_id_2f476e4b_fk_django_co FOREIGN KEY (content_type_id) REFERENCES public.django_content_type(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: auth_user_groups auth_user_groups_group_id_97559544_fk_auth_group_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user_groups
    ADD CONSTRAINT auth_user_groups_group_id_97559544_fk_auth_group_id FOREIGN KEY (group_id) REFERENCES public.auth_group(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: auth_user_groups auth_user_groups_user_id_6a12ed8b_fk_auth_user_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user_groups
    ADD CONSTRAINT auth_user_groups_user_id_6a12ed8b_fk_auth_user_id FOREIGN KEY (user_id) REFERENCES public.auth_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: auth_user_user_permissions auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user_user_permissions
    ADD CONSTRAINT auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm FOREIGN KEY (permission_id) REFERENCES public.auth_permission(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: auth_user_user_permissions auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_user_user_permissions
    ADD CONSTRAINT auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id FOREIGN KEY (user_id) REFERENCES public.auth_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: django_admin_log django_admin_log_content_type_id_c4bce8eb_fk_django_co; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.django_admin_log
    ADD CONSTRAINT django_admin_log_content_type_id_c4bce8eb_fk_django_co FOREIGN KEY (content_type_id) REFERENCES public.django_content_type(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: django_admin_log django_admin_log_user_id_c564eba6_fk_auth_user_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.django_admin_log
    ADD CONSTRAINT django_admin_log_user_id_c564eba6_fk_auth_user_id FOREIGN KEY (user_id) REFERENCES public.auth_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: funding_disbursement funding_disbursement_grant_id_57cbfd2c_fk_grants_grant_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_disbursement
    ADD CONSTRAINT funding_disbursement_grant_id_57cbfd2c_fk_grants_grant_id FOREIGN KEY (grant_id) REFERENCES public.grants_grant(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: funding_expense funding_expense_grant_id_2adef1b7_fk_grants_grant_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_expense
    ADD CONSTRAINT funding_expense_grant_id_2adef1b7_fk_grants_grant_id FOREIGN KEY (grant_id) REFERENCES public.grants_grant(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: funding_fundingallocation funding_fundingalloc_funding_entity_id_6edd46ae_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_fundingallocation
    ADD CONSTRAINT funding_fundingalloc_funding_entity_id_6edd46ae_fk_profiles_ FOREIGN KEY (funding_entity_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: funding_fundingallocation funding_fundingallocation_grant_id_2c5530fd_fk_grants_grant_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_fundingallocation
    ADD CONSTRAINT funding_fundingallocation_grant_id_2c5530fd_fk_grants_grant_id FOREIGN KEY (grant_id) REFERENCES public.grants_grant(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: funding_fundingrecord funding_fundingrecor_funding_entity_id_eb322df8_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.funding_fundingrecord
    ADD CONSTRAINT funding_fundingrecor_funding_entity_id_eb322df8_fk_profiles_ FOREIGN KEY (funding_entity_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: grants_grant grants_grant_grant_maker_organiza_7885a99a_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grant
    ADD CONSTRAINT grants_grant_grant_maker_organiza_7885a99a_fk_profiles_ FOREIGN KEY (grant_maker_organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: grants_grant grants_grant_organization_id_ec19d162_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grant
    ADD CONSTRAINT grants_grant_organization_id_ec19d162_fk_profiles_ FOREIGN KEY (organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: grants_grantattachment grants_grantattachme_attachment_type_id_234fedc1_fk_grants_gr; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grantattachment
    ADD CONSTRAINT grants_grantattachme_attachment_type_id_234fedc1_fk_grants_gr FOREIGN KEY (attachment_type_id) REFERENCES public.grants_grantattachmenttype(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: grants_grantattachment grants_grantattachment_grant_id_1d2112d4_fk_grants_grant_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grantattachment
    ADD CONSTRAINT grants_grantattachment_grant_id_1d2112d4_fk_grants_grant_id FOREIGN KEY (grant_id) REFERENCES public.grants_grant(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: grants_grantattachment grants_grantattachment_uploaded_by_id_f9991b2a_fk_users_user_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_grantattachment
    ADD CONSTRAINT grants_grantattachment_uploaded_by_id_f9991b2a_fk_users_user_id FOREIGN KEY (uploaded_by_id) REFERENCES public.users_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: grants_organizationgranthistory grants_organizationg_organization_id_00662b21_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.grants_organizationgranthistory
    ADD CONSTRAINT grants_organizationg_organization_id_00662b21_fk_profiles_ FOREIGN KEY (organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: milestones_milestone milestones_milestone_grant_id_53cf76ac_fk_grants_grant_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.milestones_milestone
    ADD CONSTRAINT milestones_milestone_grant_id_53cf76ac_fk_grants_grant_id FOREIGN KEY (grant_id) REFERENCES public.grants_grant(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: milestones_milestonesattachment milestones_milestone_milestone_id_36ec8c16_fk_milestone; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.milestones_milestonesattachment
    ADD CONSTRAINT milestones_milestone_milestone_id_36ec8c16_fk_milestone FOREIGN KEY (milestone_id) REFERENCES public.milestones_milestone(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: milestones_question milestones_question_questioner_id_53f3b8a7_fk_questioners_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.milestones_question
    ADD CONSTRAINT milestones_question_questioner_id_53f3b8a7_fk_questioners_id FOREIGN KEY (questioner_id) REFERENCES public.questioners(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_grantmakerfunds profiles_grantmakerf_funder_id_477ce08a_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_grantmakerfunds
    ADD CONSTRAINT profiles_grantmakerf_funder_id_477ce08a_fk_profiles_ FOREIGN KEY (funder_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_grantmakerfunds profiles_grantmakerf_recipient_id_e6f6d929_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_grantmakerfunds
    ADD CONSTRAINT profiles_grantmakerf_recipient_id_e6f6d929_fk_profiles_ FOREIGN KEY (recipient_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_grantmakerfunds profiles_grantmakerfunds_added_by_id_942f1c47_fk_users_user_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_grantmakerfunds
    ADD CONSTRAINT profiles_grantmakerfunds_added_by_id_942f1c47_fk_users_user_id FOREIGN KEY (added_by_id) REFERENCES public.users_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organization profiles_organizatio_address_id_23a036d0_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organization
    ADD CONSTRAINT profiles_organizatio_address_id_23a036d0_fk_profiles_ FOREIGN KEY (address_id) REFERENCES public.profiles_address(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organizationattachmenthistory profiles_organizatio_attachment_id_35e03c98_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachmenthistory
    ADD CONSTRAINT profiles_organizatio_attachment_id_35e03c98_fk_profiles_ FOREIGN KEY (attachment_id) REFERENCES public.profiles_organizationattachment(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organizationattachment profiles_organizatio_attachment_type_id_f9d8bd54_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachment
    ADD CONSTRAINT profiles_organizatio_attachment_type_id_f9d8bd54_fk_profiles_ FOREIGN KEY (attachment_type_id) REFERENCES public.profiles_organizationattachmenttype(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organization profiles_organizatio_created_by_grant_mak_4c6f18a7_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organization
    ADD CONSTRAINT profiles_organizatio_created_by_grant_mak_4c6f18a7_fk_profiles_ FOREIGN KEY (created_by_grant_maker_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organization profiles_organizatio_grant_maker_organiza_530280c5_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organization
    ADD CONSTRAINT profiles_organizatio_grant_maker_organiza_530280c5_fk_profiles_ FOREIGN KEY (grant_maker_organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organization profiles_organizatio_organization_functio_40a09ad6_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organization
    ADD CONSTRAINT profiles_organizatio_organization_functio_40a09ad6_fk_profiles_ FOREIGN KEY (organization_function_type_id) REFERENCES public.profiles_organizationfunctiontype(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organizationkmp profiles_organizatio_organization_id_0c6ed126_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationkmp
    ADD CONSTRAINT profiles_organizatio_organization_id_0c6ed126_fk_profiles_ FOREIGN KEY (organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organizationattachment profiles_organizatio_organization_id_de622725_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachment
    ADD CONSTRAINT profiles_organizatio_organization_id_de622725_fk_profiles_ FOREIGN KEY (organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organization profiles_organizatio_organization_legal_t_20710ab1_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organization
    ADD CONSTRAINT profiles_organizatio_organization_legal_t_20710ab1_fk_profiles_ FOREIGN KEY (organization_legal_type_id) REFERENCES public.profiles_organizationlegaltype(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organizationattachmenthistory profiles_organizatio_status_id_03b21cb5_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachmenthistory
    ADD CONSTRAINT profiles_organizatio_status_id_03b21cb5_fk_profiles_ FOREIGN KEY (status_id) REFERENCES public.profiles_organizationattachmentstatus(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organizationattachment profiles_organizatio_status_id_9c9377f2_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachment
    ADD CONSTRAINT profiles_organizatio_status_id_9c9377f2_fk_profiles_ FOREIGN KEY (status_id) REFERENCES public.profiles_organizationattachmentstatus(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organizationattachmenthistory profiles_organizatio_uploaded_by_id_05e369b9_fk_users_use; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachmenthistory
    ADD CONSTRAINT profiles_organizatio_uploaded_by_id_05e369b9_fk_users_use FOREIGN KEY (uploaded_by_id) REFERENCES public.users_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: profiles_organizationattachment profiles_organizatio_uploaded_by_id_202a9ded_fk_users_use; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.profiles_organizationattachment
    ADD CONSTRAINT profiles_organizatio_uploaded_by_id_202a9ded_fk_users_use FOREIGN KEY (uploaded_by_id) REFERENCES public.users_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: questioners questioners_grant_id_8a5de9b0_fk_grants_grant_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.questioners
    ADD CONSTRAINT questioners_grant_id_8a5de9b0_fk_grants_grant_id FOREIGN KEY (grant_id) REFERENCES public.grants_grant(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: reports_narrativereport reports_narrativerep_organization_id_c81a2523_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.reports_narrativereport
    ADD CONSTRAINT reports_narrativerep_organization_id_c81a2523_fk_profiles_ FOREIGN KEY (organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: reports_narrativereportquestion reports_narrativerep_report_id_4d9e7c1b_fk_reports_n; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.reports_narrativereportquestion
    ADD CONSTRAINT reports_narrativerep_report_id_4d9e7c1b_fk_reports_n FOREIGN KEY (report_id) REFERENCES public.reports_narrativereport(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: reports_narrativereport reports_narrativereport_grant_id_2da6e832_fk_grants_grant_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.reports_narrativereport
    ADD CONSTRAINT reports_narrativereport_grant_id_2da6e832_fk_grants_grant_id FOREIGN KEY (grant_id) REFERENCES public.grants_grant(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: reports_reportsgallery reports_reportsgalle_organization_id_44033719_fk_profiles_; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.reports_reportsgallery
    ADD CONSTRAINT reports_reportsgalle_organization_id_44033719_fk_profiles_ FOREIGN KEY (organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: support_historicalticket support_historicalti_history_user_id_6bdc4f23_fk_auth_user; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.support_historicalticket
    ADD CONSTRAINT support_historicalti_history_user_id_6bdc4f23_fk_auth_user FOREIGN KEY (history_user_id) REFERENCES public.auth_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: support_ticket support_ticket_grant_id_c80cb603_fk_grants_grant_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.support_ticket
    ADD CONSTRAINT support_ticket_grant_id_c80cb603_fk_grants_grant_id FOREIGN KEY (grant_id) REFERENCES public.grants_grant(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: support_ticketchathistory support_ticketchathi_ticket_id_e300ed70_fk_support_t; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.support_ticketchathistory
    ADD CONSTRAINT support_ticketchathi_ticket_id_e300ed70_fk_support_t FOREIGN KEY (ticket_id) REFERENCES public.support_ticket(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: support_ticketchathistory support_ticketchathistory_user_id_58805ebf_fk_users_user_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.support_ticketchathistory
    ADD CONSTRAINT support_ticketchathistory_user_id_58805ebf_fk_users_user_id FOREIGN KEY (user_id) REFERENCES public.users_user(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: users_user users_user_organization_id_70643736_fk_profiles_organization_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_user
    ADD CONSTRAINT users_user_organization_id_70643736_fk_profiles_organization_id FOREIGN KEY (organization_id) REFERENCES public.profiles_organization(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: users_user users_user_role_id_854f2687_fk_users_userrole_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_user
    ADD CONSTRAINT users_user_role_id_854f2687_fk_users_userrole_id FOREIGN KEY (role_id) REFERENCES public.users_userrole(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: users_user users_user_type_id_2647b62a_fk_users_usertype_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users_user
    ADD CONSTRAINT users_user_type_id_2647b62a_fk_users_usertype_id FOREIGN KEY (type_id) REFERENCES public.users_usertype(id) DEFERRABLE INITIALLY DEFERRED;


--
-- PostgreSQL database dump complete
--

