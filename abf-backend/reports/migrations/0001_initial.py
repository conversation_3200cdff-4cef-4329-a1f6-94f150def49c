# Generated by Django 4.2.20 on 2025-06-25 11:26

import common.mixins.s3
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('profiles', '__first__'),
        ('grants', '__first__'),
    ]

    operations = [
        migrations.CreateModel(
            name='Impact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('impact_statement', models.TextField()),
                ('needs_assessment', models.TextField(blank=True, null=True)),
                ('pre_intervention_assessment', models.TextField(blank=True, null=True)),
                ('post_intervention_assessment', models.TextField(blank=True, null=True)),
                ('tools_used', models.TextField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, help_text='General remarks for the report (optional)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('grant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='impact_report_for_grant', to='grants.grant')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='impact_reports_of_org', to='profiles.organization')),
            ],
        ),
        migrations.CreateModel(
            name='NarrativeReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quarter', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(4)])),
                ('year', models.IntegerField()),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')], default='PENDING', max_length=20)),
                ('remarks', models.TextField(blank=True, help_text='Remarks for reviewers or admins (optional)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('grant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='narrative_report_for_grant', to='grants.grant')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='narrative_reports_of_org', to='profiles.organization')),
            ],
        ),
        migrations.CreateModel(
            name='Outcome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('outcome_statement', models.TextField()),
                ('pre_intervention_assessment', models.TextField(blank=True, null=True)),
                ('post_intervention_assessment', models.TextField(blank=True, null=True)),
                ('tools_used', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('impact', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outcomes', to='reports.impact')),
            ],
        ),
        migrations.CreateModel(
            name='ReportsGallery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('s3_key', models.CharField(max_length=512)),
                ('file_name', models.CharField(max_length=255)),
                ('file_type', models.CharField(blank=True, max_length=255, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports_gallery_of_org', to='profiles.organization')),
            ],
            bases=(models.Model, common.mixins.s3.S3PresignedURLMixin),
        ),
        migrations.CreateModel(
            name='Output',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('unit', models.CharField(max_length=100)),
                ('q1_plan', models.IntegerField(blank=True, null=True)),
                ('q1_actual', models.IntegerField(blank=True, null=True)),
                ('q1_remark', models.TextField(blank=True, null=True)),
                ('q2_plan', models.IntegerField(blank=True, null=True)),
                ('q2_actual', models.IntegerField(blank=True, null=True)),
                ('q2_remark', models.TextField(blank=True, null=True)),
                ('q3_plan', models.IntegerField(blank=True, null=True)),
                ('q3_actual', models.IntegerField(blank=True, null=True)),
                ('q3_remark', models.TextField(blank=True, null=True)),
                ('q4_plan', models.IntegerField(blank=True, null=True)),
                ('q4_actual', models.IntegerField(blank=True, null=True)),
                ('q4_remark', models.TextField(blank=True, null=True)),
                ('means_of_verification', models.TextField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('outcome', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outputs', to='reports.outcome')),
            ],
        ),
        migrations.CreateModel(
            name='NarrativeReportQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.TextField()),
                ('answer', models.TextField(blank=True)),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_answers', to='reports.narrativereport')),
            ],
        ),
        migrations.CreateModel(
            name='Activity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('unit', models.CharField(max_length=100)),
                ('q1_plan', models.IntegerField(blank=True, null=True)),
                ('q1_actual', models.IntegerField(blank=True, null=True)),
                ('q1_remark', models.TextField(blank=True, null=True)),
                ('q2_plan', models.IntegerField(blank=True, null=True)),
                ('q2_actual', models.IntegerField(blank=True, null=True)),
                ('q2_remark', models.TextField(blank=True, null=True)),
                ('q3_plan', models.IntegerField(blank=True, null=True)),
                ('q3_actual', models.IntegerField(blank=True, null=True)),
                ('q3_remark', models.TextField(blank=True, null=True)),
                ('q4_plan', models.IntegerField(blank=True, null=True)),
                ('q4_actual', models.IntegerField(blank=True, null=True)),
                ('q4_remark', models.TextField(blank=True, null=True)),
                ('means_of_verification', models.TextField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('output', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='reports.output')),
            ],
        ),
        migrations.AddConstraint(
            model_name='narrativereport',
            constraint=models.UniqueConstraint(fields=('organization', 'grant', 'year', 'quarter'), name='unique_report_per_org_grant_quarter'),
        ),
    ]
