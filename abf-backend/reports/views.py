from django.db import transaction
from rest_framework import mixins, permissions, status, viewsets
from rest_framework.exceptions import PermissionDenied, ValidationError
from rest_framework.response import Response

from common.decorators import handle_org_exceptions
from common.mixins.org_aware import OrgAwareMixin
from grants.models import Grant
from profiles.models import Organization
from reports.models import (Activity, Impact, NarrativeReport,
                            NarrativeReportQuestion, Outcome, Output,
                            ReportsGallery)
from reports.permissions import CanGranteeEditOwnUpload
from reports.serializers import (ActivitySerializer, ImpactSerializer,
                                 NarrativeReportSerializer, OutcomeSerializer,
                                 OutputSerializer, ReportsGallerySerializer)
from users.permissions import IsGrantee, IsGrantMaker


class GranteeNarrativeReportViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    permission_classes = [IsGrantee]
    serializer_class = NarrativeReportSerializer

    @handle_org_exceptions
    def get_queryset(self):
        user_org = self.get_user_org(self.request)
        queryset = NarrativeReport.objects.filter(organization=user_org)

        # Apply filters based on query parameters
        filters = {
            'year': self.request.query_params.get("year"),
            'quarter': self.request.query_params.get("quarter"),
            'grant_id': self.request.query_params.get("grant"),
            'status': self.request.query_params.get("status")
        }
        
        for field, value in filters.items():
            if value:
                queryset = queryset.filter(**{field: value})

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_create(self, serializer):
        user_org = self.get_user_org(self.request)
        grant_id = self.request.data.get("grant_id")
        questions_data = self.request.data.get("questions", [])

        try:
            grant = Grant.objects.get(pk=grant_id)
        except Grant.DoesNotExist:
            raise ValidationError("Grant not found.")

        serializer.save(organization=user_org, grant=grant)

        # Create questions for the report
        report = serializer.instance
        for q in questions_data:
            question_text = q.get("question", "").strip()
            answer_text = q.get("answer", "").strip()
            if question_text:
                NarrativeReportQuestion.objects.create(report=report, question=question_text, answer=answer_text)

    @handle_org_exceptions
    def perform_update(self, serializer):
        user_org = self.get_user_org(self.request)
        report = self.get_object()

        if report.organization != user_org:
            raise PermissionDenied("You do not have permission to update this report.")

        # Validate report status for editing
        if report.status == "PENDING":
            raise ValidationError("The report is currently under review")
        elif report.status == "APPROVED":
            raise ValidationError("This report has been approved and cannot be edited.")
        elif report.status != "REJECTED":
            raise ValidationError("Only rejected reports can be edited.")

        # Handle resubmission
        is_resubmission = self.request.data.get("status") == "PENDING" and report.status == "REJECTED"
        
        # Validate restricted fields
        if 'remarks' in self.request.data:
            raise ValidationError({"remarks": "You are not allowed to modify this field."})

        # Validate questions data
        questions_data = self.request.data.get("questions")
        if questions_data is None:
            raise ValidationError({"questions": "This field is required."})
        if not isinstance(questions_data, list):
            raise ValidationError({"questions": "Must be a list."})

        # Update existing questions or create new ones
        existing_answers = {qa.id: qa for qa in report.question_answers.all()}

        for q in questions_data:
            q_id = q.get("id")
            question_text = q.get("question", "").strip()
            answer_text = q.get("answer", "").strip()

            if q_id in existing_answers:
                qa = existing_answers[q_id]
                if question_text:
                    qa.question = question_text
                if answer_text:
                    qa.answer = answer_text
                qa.save()
            elif question_text:
                NarrativeReportQuestion.objects.create(report=report, question=question_text, answer=answer_text)

        # Handle resubmission status change
        if is_resubmission:
            report.status = "PENDING"
            report.save()

        serializer.instance = report


class GrantmakerNarrativeReportViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    permission_classes = [IsGrantMaker]
    serializer_class = NarrativeReportSerializer

    @handle_org_exceptions
    def get_queryset(self):
        queryset = NarrativeReport.objects.all()

        # Apply filters based on query parameters
        filters = {
            'organization_id': self.request.query_params.get("grantee_id"),
            'year': self.request.query_params.get("year"),
            'quarter': self.request.query_params.get("quarter"),
            'grant_id': self.request.query_params.get("grant"),
            'status': self.request.query_params.get("status")
        }
        
        for field, value in filters.items():
            if value:
                queryset = queryset.filter(**{field: value})

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_update(self, serializer):
        report = self.get_object()
        new_status = self.request.data.get("status")
        remarks = self.request.data.get("remarks")

        # Validate rejection requirements
        if new_status == "REJECTED" and not remarks:
            raise ValidationError({"remarks": "Remarks are required when rejecting a report."})

        serializer.save()

        # Update questions if provided
        questions_data = self.request.data.get("questions")
        if questions_data is not None:
            if not isinstance(questions_data, list):
                raise ValidationError({"questions": "Must be a list of answers."})

            report.question_answers.all().delete()
            for q in questions_data:
                NarrativeReportQuestion.objects.create(
                    report=report,
                    question=q.get("question", "").strip(),
                    answer=q.get("answer", "").strip()
                )


class GrantmakerImpactViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    """
    Main ViewSet for impact reports
    Handles:
    - /grantmaker/quantitative/ → List all impacts (with report filtering)
    - /grantmaker/quantitative/{id}/ → Specific impact detail
    """
    permission_classes = [IsGrantMaker]
    serializer_class = ImpactSerializer

    @handle_org_exceptions
    def get_queryset(self):
        queryset = Impact.objects.all()

        filters = {
            'organization_id': self.request.query_params.get("organization_id"),
        }
        
        for field, value in filters.items():
            if value:
                queryset = queryset.filter(**{field: value})

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_create(self, serializer):
        """Create new impact report"""
        grant_id = self.request.data.get("grant_id")
        organization_id = self.request.data.get("organization_id")
        year = self.request.data.get("year")

        try:
            grant = Grant.objects.get(pk=grant_id)
            organization = Organization.objects.get(pk=organization_id)
        except Grant.DoesNotExist:
            raise ValidationError("Grant not found.")
        except Organization.DoesNotExist:
            raise ValidationError("Organization not found.")

        serializer.save(
            organization=organization,
            grant=grant,
            year=year
        )

    @handle_org_exceptions
    def perform_update(self, serializer):
        impact = self.get_object()
        serializer.save()

class GrantmakerOutcomeViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    """ViewSet for managing individual outcomes"""
    permission_classes = [IsGrantMaker]
    serializer_class = OutcomeSerializer

    OUTPUT_FIELDS = [
        "description", "unit", "q1_plan", "q1_actual", "q1_remark",
        "q2_plan", "q2_actual", "q2_remark", "q3_plan", "q3_actual", "q3_remark",
        "q4_plan", "q4_actual", "q4_remark", "means_of_verification", "remarks"
    ]
    ACTIVITY_FIELDS = OUTPUT_FIELDS

    @handle_org_exceptions
    def get_queryset(self):
        queryset = Outcome.objects.all()

        filters = {
            'impact_id': self.request.query_params.get("impact_id"),
        }
        
        for field, value in filters.items():
            if value:
                queryset = queryset.filter(**{field: value})
            
        return queryset.select_related('impact').prefetch_related('outputs__activities')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_update(self, serializer):
        outcome = self.get_object()
        outputs_data = self.request.data.get("outputs", [])

        serializer.save()

        if not isinstance(outputs_data, list):
            raise ValidationError({"outputs": "Must be a list."})

        self._sync_outputs_and_activities(outcome, outputs_data)

    def _sync_outputs_and_activities(self, outcome, outputs_data):
        existing_outputs = {o.id: o for o in outcome.outputs.all()}
        seen_output_ids = set()

        for output_data in outputs_data:
            description = output_data.get("description", "").strip()
            if not description:
                continue

            output_id = output_data.get("id")
            output = None

            if output_id and output_id in existing_outputs:
                output = existing_outputs[output_id]
                self._update_instance(output, output_data, self.OUTPUT_FIELDS)
            else:
                output = Output.objects.create(outcome=outcome, **self._filtered_fields(output_data, self.OUTPUT_FIELDS))

            seen_output_ids.add(output.id)
            self._sync_activities(output, output_data.get("activities", []))

        # Delete removed outputs
        for output_id in set(existing_outputs.keys()) - seen_output_ids:
            existing_outputs[output_id].delete()

    def _sync_activities(self, output, activities_data):
        existing_activities = {a.id: a for a in output.activities.all()}
        seen_activity_ids = set()

        for activity_data in activities_data:
            description = activity_data.get("description", "").strip()
            if not description:
                continue

            activity_id = activity_data.get("id")
            activity = None

            if activity_id and activity_id in existing_activities:
                activity = existing_activities[activity_id]
                self._update_instance(activity, activity_data, self.ACTIVITY_FIELDS)
            else:
                Activity.objects.create(output=output, **self._filtered_fields(activity_data, self.ACTIVITY_FIELDS))

            if activity_id:
                seen_activity_ids.add(activity_id)

        # Delete removed activities
        for activity_id in set(existing_activities.keys()) - seen_activity_ids:
            existing_activities[activity_id].delete()

    def _update_instance(self, instance, data, fields):
        """Update instance with filtered fields"""
        for field in fields:
            if field in data:
                setattr(instance, field, data[field])
        instance.save()

    def _filtered_fields(self, data, fields):
        """Return a dict of fields from data, with default fallbacks"""
        return {field: data.get(field, "") for field in fields}

    @handle_org_exceptions
    def perform_create(self, serializer):
        impact_id = self.request.data.get("impact_id")
        outputs_data = self.request.data.get("outputs", [])
        
        try:
            impact = Impact.objects.get(pk=impact_id)
        except Impact.DoesNotExist:
            raise ValidationError("Impact not found.")
            
        outcome = serializer.save(impact=impact)
        self._create_outputs_and_activities(outcome, outputs_data)


    def _create_outputs_and_activities(self, outcome, outputs_data):
        """Helper method to create outputs and their activities"""
        for output_data in outputs_data:
            description = output_data.get("description", "").strip()
            if not description:
                continue
                
            output = Output.objects.create(
                outcome=outcome,
                description=description,
                unit=output_data.get("unit", ""),
                q1_plan=output_data.get("q1_plan"),
                q1_actual=output_data.get("q1_actual"),
                q1_remark=output_data.get("q1_remark", ""),
                q2_plan=output_data.get("q2_plan"),
                q2_actual=output_data.get("q2_actual"),
                q2_remark=output_data.get("q2_remark", ""),
                q3_plan=output_data.get("q3_plan"),
                q3_actual=output_data.get("q3_actual"),
                q3_remark=output_data.get("q3_remark", ""),
                q4_plan=output_data.get("q4_plan"),
                q4_actual=output_data.get("q4_actual"),
                q4_remark=output_data.get("q4_remark", ""),
                means_of_verification=output_data.get("means_of_verification", ""),
                remarks=output_data.get("remarks", "")
            )
            
            # Create activities for this output
            activities_data = output_data.get("activities", [])
            for activity_data in activities_data:
                activity_description = activity_data.get("description", "").strip()
                if not activity_description:
                    continue
                    
                Activity.objects.create(
                    output=output,
                    description=activity_description,
                    unit=activity_data.get("unit", ""),
                    q1_plan=activity_data.get("q1_plan"),
                    q1_actual=activity_data.get("q1_actual"),
                    q1_remark=activity_data.get("q1_remark", ""),
                    q2_plan=activity_data.get("q2_plan"),
                    q2_actual=activity_data.get("q2_actual"),
                    q2_remark=activity_data.get("q2_remark", ""),
                    q3_plan=activity_data.get("q3_plan"),
                    q3_actual=activity_data.get("q3_actual"),
                    q3_remark=activity_data.get("q3_remark", ""),
                    q4_plan=activity_data.get("q4_plan"),
                    q4_actual=activity_data.get("q4_actual"),
                    q4_remark=activity_data.get("q4_remark", ""),
                    means_of_verification=activity_data.get("means_of_verification", ""),
                    remarks=activity_data.get("remarks", "")
                )



class GrantmakerOutputViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    permission_classes = [IsGrantMaker]
    serializer_class = OutputSerializer

    @handle_org_exceptions
    def get_queryset(self):
        queryset = Output.objects.all()
        outcome_id = self.request.query_params.get("outcome")
        if outcome_id:
            queryset = queryset.filter(outcome_id=outcome_id)
        return queryset.select_related('outcome').prefetch_related('activities')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context


class GrantmakerActivityViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    permission_classes = [IsGrantMaker]
    serializer_class = ActivitySerializer

    @handle_org_exceptions
    def get_queryset(self):
        queryset = Activity.objects.all()
        output_id = self.request.query_params.get("output")
        if output_id:
            queryset = queryset.filter(output_id=output_id)
        return queryset.select_related('output')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context


class GranteeImpactViewSet(OrgAwareMixin, viewsets.ReadOnlyModelViewSet):
    """
    Read-only ViewSet for grantee to view their impact reports
    Grantees can only view impacts but cannot edit them
    """
    permission_classes = [IsGrantee]
    serializer_class = ImpactSerializer

    @handle_org_exceptions
    def get_queryset(self):
        user_org = self.get_user_org(self.request)
        queryset = Impact.objects.filter(organization=user_org)

        filters = {
            'year': self.request.query_params.get("year"),
            'grant_id': self.request.query_params.get("grant"),
        }
        
        for field, value in filters.items():
            if value:
                queryset = queryset.filter(**{field: value})

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context


class GranteeOutcomeViewSet(OrgAwareMixin, 
                           mixins.ListModelMixin,
                           mixins.RetrieveModelMixin, 
                           mixins.UpdateModelMixin,
                           viewsets.GenericViewSet):
    """
    ViewSet for grantee to manage outcomes within their impacts
    Grantees can only read and update existing outcomes (no create/delete)
    """
    permission_classes = [IsGrantee]
    serializer_class = OutcomeSerializer

    @handle_org_exceptions
    def get_queryset(self):
        user_org = self.get_user_org(self.request)
        impact_id = self.request.query_params.get("impact_id")
        
        queryset = Outcome.objects.filter(impact__organization=user_org)
        
        if impact_id:
            queryset = queryset.filter(impact_id=impact_id)

        return queryset.select_related('impact').prefetch_related('outputs__activities')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_update(self, serializer):
        user_org = self.get_user_org(self.request)
        outcome = self.get_object()
        
        if outcome.impact.organization != user_org:
            raise PermissionDenied("You do not have permission to update this outcome.")

        outputs_data = self.request.data.get("outputs")
        
        serializer.save()
        
        if outputs_data is not None:
            if not isinstance(outputs_data, list):
                raise ValidationError({"outputs": "Must be a list."})
            
            self._update_existing_outputs_and_activities(outcome, outputs_data)

    def _update_existing_outputs_and_activities(self, outcome, outputs_data):
        """Helper method to update existing outputs and their activities by position"""
        existing_outputs = list(outcome.outputs.all().order_by('id'))
        
        for index, output_data in enumerate(outputs_data):
            if index < len(existing_outputs):
                output = existing_outputs[index]
                
                if "description" in output_data:
                    output.description = output_data["description"]
                if "unit" in output_data:
                    output.unit = output_data["unit"]
                if "q1_plan" in output_data:
                    output.q1_plan = output_data["q1_plan"]
                if "q1_actual" in output_data:
                    output.q1_actual = output_data["q1_actual"]
                if "q1_remark" in output_data:
                    output.q1_remark = output_data["q1_remark"]
                if "q2_plan" in output_data:
                    output.q2_plan = output_data["q2_plan"]
                if "q2_actual" in output_data:
                    output.q2_actual = output_data["q2_actual"]
                if "q2_remark" in output_data:
                    output.q2_remark = output_data["q2_remark"]
                if "q3_plan" in output_data:
                    output.q3_plan = output_data["q3_plan"]
                if "q3_actual" in output_data:
                    output.q3_actual = output_data["q3_actual"]
                if "q3_remark" in output_data:
                    output.q3_remark = output_data["q3_remark"]
                if "q4_plan" in output_data:
                    output.q4_plan = output_data["q4_plan"]
                if "q4_actual" in output_data:
                    output.q4_actual = output_data["q4_actual"]
                if "q4_remark" in output_data:
                    output.q4_remark = output_data["q4_remark"]
                if "means_of_verification" in output_data:
                    output.means_of_verification = output_data["means_of_verification"]
                if "remarks" in output_data:
                    output.remarks = output_data["remarks"]
                    
                output.save()
                
                activities_data = output_data.get("activities", [])
                existing_activities = list(output.activities.all().order_by('id'))
                
                for act_index, activity_data in enumerate(activities_data):
                    if act_index < len(existing_activities):
                        activity = existing_activities[act_index]
                        
                        if "description" in activity_data:
                            activity.description = activity_data["description"]
                        if "unit" in activity_data:
                            activity.unit = activity_data["unit"]
                        if "q1_plan" in activity_data:
                            activity.q1_plan = activity_data["q1_plan"]
                        if "q1_actual" in activity_data:
                            activity.q1_actual = activity_data["q1_actual"]
                        if "q1_remark" in activity_data:
                            activity.q1_remark = activity_data["q1_remark"]
                        if "q2_plan" in activity_data:
                            activity.q2_plan = activity_data["q2_plan"]
                        if "q2_actual" in activity_data:
                            activity.q2_actual = activity_data["q2_actual"]
                        if "q2_remark" in activity_data:
                            activity.q2_remark = activity_data["q2_remark"]
                        if "q3_plan" in activity_data:
                            activity.q3_plan = activity_data["q3_plan"]
                        if "q3_actual" in activity_data:
                            activity.q3_actual = activity_data["q3_actual"]
                        if "q3_remark" in activity_data:
                            activity.q3_remark = activity_data["q3_remark"]
                        if "q4_plan" in activity_data:
                            activity.q4_plan = activity_data["q4_plan"]
                        if "q4_actual" in activity_data:
                            activity.q4_actual = activity_data["q4_actual"]
                        if "q4_remark" in activity_data:
                            activity.q4_remark = activity_data["q4_remark"]
                        if "means_of_verification" in activity_data:
                            activity.means_of_verification = activity_data["means_of_verification"]
                        if "remarks" in activity_data:
                            activity.remarks = activity_data["remarks"]
                            
                        activity.save()


class GranteeOutputViewSet(OrgAwareMixin,
                          mixins.ListModelMixin,
                          mixins.RetrieveModelMixin,
                          mixins.UpdateModelMixin,
                          viewsets.GenericViewSet):
    """
    ViewSet for grantee to manage outputs within their outcomes
    Grantees can only read and update existing outputs (no create/delete)
    """
    permission_classes = [IsGrantee]
    serializer_class = OutputSerializer

    @handle_org_exceptions
    def get_queryset(self):
        user_org = self.get_user_org(self.request)
        outcome_id = self.request.query_params.get("outcome")
        
        queryset = Output.objects.filter(outcome__impact__organization=user_org)
        
        if outcome_id:
            queryset = queryset.filter(outcome_id=outcome_id)

        return queryset.select_related('outcome').prefetch_related('activities')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_update(self, serializer):
        user_org = self.get_user_org(self.request)
        output = self.get_object()
        
        if output.outcome.impact.organization != user_org:
            raise PermissionDenied("You do not have permission to update this output.")

        serializer.save()


class GranteeActivityViewSet(OrgAwareMixin,
                            mixins.ListModelMixin,
                            mixins.RetrieveModelMixin,
                            mixins.UpdateModelMixin,
                            viewsets.GenericViewSet):
    """
    ViewSet for grantee to manage activities within their outputs
    Grantees can only read and update existing activities (no create/delete)
    """
    permission_classes = [IsGrantee]
    serializer_class = ActivitySerializer

    @handle_org_exceptions
    def get_queryset(self):
        user_org = self.get_user_org(self.request)
        output_id = self.request.query_params.get("output")
        
        queryset = Activity.objects.filter(output__outcome__impact__organization=user_org)
        
        if output_id:
            queryset = queryset.filter(output_id=output_id)

        return queryset.select_related('output')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_update(self, serializer):
        user_org = self.get_user_org(self.request)
        activity = self.get_object()
        
        if activity.output.outcome.impact.organization != user_org:
            raise PermissionDenied("You do not have permission to update this activity.")

        serializer.save()

class GranteeReportsGalleryViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    serializer_class = ReportsGallerySerializer
    permission_classes = [IsGrantee, CanGranteeEditOwnUpload]

    def get_queryset(self):
        organization = self.get_user_org(self.request)
        return ReportsGallery.objects.filter(organization=organization)

    def perform_create(self, serializer):
        organization = self.get_user_org(self.request)
        serializer.save(organization=organization)

    def perform_destroy(self, instance):
        organization = self.get_user_org(self.request)
        if instance.organization != organization:
            raise PermissionDenied("You cannot delete this report.")

        instance.delete_s3_file()
        instance.delete()


class GrantmakerReportsGalleryViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    serializer_class = ReportsGallerySerializer
    permission_classes = [IsGrantMaker]

    def get_queryset(self):
        queryset = ReportsGallery.objects.all()

        # Apply filters based on query parameters
        org_id = self.request.query_params.get("organization")
        if org_id:
            try:
                Organization.objects.get(pk=org_id)  # Validate organization exists
                queryset = queryset.filter(organization_id=org_id)
            except Organization.DoesNotExist:
                raise ValidationError("Organization not found.")

        title = self.request.query_params.get("title")
        if title:
            queryset = queryset.filter(title__icontains=title)

        uploaded_after = self.request.query_params.get("uploaded_after")
        if uploaded_after:
            queryset = queryset.filter(uploaded_at__gte=uploaded_after)

        uploaded_before = self.request.query_params.get("uploaded_before")
        if uploaded_before:
            queryset = queryset.filter(uploaded_at__lte=uploaded_before)

        return queryset

    def perform_update(self, serializer):
        # Allow update of metadata only
        serializer.save()
