import logging

from django.core.validators import Max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MinValueValidator
from django.db import models

from abf_backend import settings
from common.mixins.s3 import S3PresignedURLMixin
from grants.models import Grant
from profiles.models import Organization


class NarrativeReport(models.Model):
    STATUS_CHOICES = [
        ("PENDING", "Pending"),
        ("APPROVED", "Approved"),
        ("REJECTED", "Rejected"),
    ]

    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='narrative_reports_of_org'
    )
    grant = models.ForeignKey(
        Grant,
        on_delete=models.CASCADE,
        related_name='narrative_report_for_grant'
    )
    quarter = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(4)],
    )
    year = models.IntegerField()
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="PENDING",
    )
    remarks = models.TextField(
        blank=True,
        null=True,
        help_text="Remarks for reviewers or admins (optional)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['organization', 'grant', 'year', 'quarter'],
                name='unique_report_per_org_grant_quarter'
            )
        ]

class NarrativeReportQuestion(models.Model):
    report = models.ForeignKey(
        NarrativeReport,
        on_delete=models.CASCADE,
        related_name='question_answers'
    )
    question = models.TextField()
    answer = models.TextField(blank=True)

    def __str__(self):
        return f"Q: {self.question[:30]}..."


class Impact(models.Model):
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='impact_reports_of_org'
    )
    grant = models.ForeignKey(
        Grant,
        on_delete=models.CASCADE,
        related_name='impact_report_for_grant'
    )
    year = models.IntegerField()
    impact_statement = models.TextField()
    needs_assessment = models.TextField(blank=True, null=True)
    pre_intervention_assessment = models.TextField(blank=True, null=True)
    post_intervention_assessment = models.TextField(blank=True, null=True)
    tools_used = models.TextField(blank=True, null=True)
    remarks = models.TextField(
        blank=True,
        null=True,
        help_text="General remarks for the report (optional)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Impact Report - {self.organization.name} - {self.year}"


class Outcome(models.Model):
    impact = models.ForeignKey(
        Impact,
        on_delete=models.CASCADE,
        related_name='outcomes'
    )
    outcome_statement = models.TextField()
    pre_intervention_assessment = models.TextField(blank=True, null=True)
    post_intervention_assessment = models.TextField(blank=True, null=True)
    tools_used = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Outcome: {self.outcome_statement[:50]}..."


class Output(models.Model):
    outcome = models.ForeignKey(
        Outcome,
        on_delete=models.CASCADE,
        related_name='outputs'
    )
    description = models.TextField()
    unit = models.CharField(max_length=100)
    
    # Quarter 1
    q1_plan = models.IntegerField(blank=True, null=True)
    q1_actual = models.IntegerField(blank=True, null=True)
    q1_remark = models.TextField(blank=True, null=True)
    
    # Quarter 2
    q2_plan = models.IntegerField(blank=True, null=True)
    q2_actual = models.IntegerField(blank=True, null=True)
    q2_remark = models.TextField(blank=True, null=True)
    
    # Quarter 3
    q3_plan = models.IntegerField(blank=True, null=True)
    q3_actual = models.IntegerField(blank=True, null=True)
    q3_remark = models.TextField(blank=True, null=True)
    
    # Quarter 4
    q4_plan = models.IntegerField(blank=True, null=True)
    q4_actual = models.IntegerField(blank=True, null=True)
    q4_remark = models.TextField(blank=True, null=True)
    
    means_of_verification = models.TextField(blank=True, null=True)
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Output: {self.description[:50]}..."


class Activity(models.Model):
    output = models.ForeignKey(
        Output,
        on_delete=models.CASCADE,
        related_name='activities'
    )
    description = models.TextField()
    unit = models.CharField(max_length=100)
    
    # Quarter 1
    q1_plan = models.IntegerField(blank=True, null=True)
    q1_actual = models.IntegerField(blank=True, null=True)
    q1_remark = models.TextField(blank=True, null=True)
    
    # Quarter 2
    q2_plan = models.IntegerField(blank=True, null=True)
    q2_actual = models.IntegerField(blank=True, null=True)
    q2_remark = models.TextField(blank=True, null=True)
    
    # Quarter 3
    q3_plan = models.IntegerField(blank=True, null=True)
    q3_actual = models.IntegerField(blank=True, null=True)
    q3_remark = models.TextField(blank=True, null=True)
    
    # Quarter 4
    q4_plan = models.IntegerField(blank=True, null=True)
    q4_actual = models.IntegerField(blank=True, null=True)
    q4_remark = models.TextField(blank=True, null=True)
    
    means_of_verification = models.TextField(blank=True, null=True)
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Activity: {self.description[:50]}..."


class ReportsGallery(models.Model, S3PresignedURLMixin):
    title = models.CharField(max_length=255)
    s3_key = models.CharField(max_length=512)
    file_name = models.CharField(max_length=255)
    file_type = models.CharField(max_length=255, blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    description = models.TextField(blank=True, null=True)
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='reports_gallery_of_org'
    )

    @property
    def attachment_url(self):
        return self.generate_presigned_url(s3_key=self.s3_key, method="get_object")

    def delete_s3_file(self):
        s3 = self.get_s3_client()
        try:
            s3.delete_object(Bucket=settings.S3_BUCKET_NAME, Key=self.s3_key)
        except Exception as e:
            logging.error(f"Error deleting S3 file {self.s3_key}: {e}")

    def __str__(self):
        return f"{self.file_name}"
