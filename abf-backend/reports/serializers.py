from rest_framework import serializers

from grants.models import Grant
from grants.serializers import GrantSerializer
from profiles.models import Organization
from profiles.serializers import OrganizationSerializer
from reports.models import (Activity, Impact, NarrativeReport,
                            NarrativeReportQuestion, Outcome, Output,
                            ReportsGallery)


class NarrativeReportQuestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = NarrativeReportQuestion
        fields = '__all__'


class NarrativeReportSerializer(serializers.ModelSerializer):
    grant_id = serializers.PrimaryKeyRelatedField(
        queryset=Grant.objects.all(),
        write_only=True,
        required=False  
    )
    grant = GrantSerializer(read_only=True)
    organization = OrganizationSerializer(read_only=True)
    question_answers = NarrativeReportQuestionSerializer(many=True, read_only=True)

    class Meta:
        model = NarrativeReport
        fields = [
            'id', 'organization', 'grant_id', 'grant',
            'quarter', 'year', 'status', 'remarks',
            'question_answers', 'created_at', 'updated_at',
        ]
        read_only_fields = ['organization', 'grant', 'created_at', 'updated_at'] 

    def validate(self, attrs):
        organization = self.context.get('organization')
        grant = attrs.get('grant_id', getattr(self.instance, 'grant', None)) 
        year = attrs.get('year', getattr(self.instance, 'year', None))       
        quarter = attrs.get('quarter', getattr(self.instance, 'quarter', None)) 

        if not organization:
            raise serializers.ValidationError("User is not associated with an organization.")

        if self.instance and not any(attrs.keys()):
            return attrs

        if not all([grant, year, quarter]):
            return attrs

        if self.instance:
            if (
                self.instance.grant_id == grant.id and
                self.instance.year == year and
                self.instance.quarter == quarter
            ):
                return attrs

        exists = NarrativeReport.objects.filter(
            organization=organization,
            grant=grant,
            year=year,
            quarter=quarter,
        ).exclude(pk=self.instance.pk if self.instance else None).exists() # Exclude the current instance during update check
        if exists:
            raise serializers.ValidationError(
                "A report already exists for this grant, year, and quarter."
            )

        return attrs

    def create(self, validated_data):
        grant = validated_data.pop('grant_id')
        organization = self.context.get('organization')

        validated_data.pop('organization', None)
        validated_data.pop('grant', None)

        if not organization:
            raise serializers.ValidationError("Organization context missing.")

        return NarrativeReport.objects.create(
            organization=organization,
            grant=grant,
            **validated_data
        )

    def update(self, instance, validated_data):
        instance.status = validated_data.get('status', instance.status)
        instance.remarks = validated_data.get('remarks', instance.remarks)
        instance.save()
        return instance


class ActivitySerializer(serializers.ModelSerializer):
    class Meta:
        model = Activity
        fields = [
            'id', 'description', 'unit',
            'q1_plan', 'q1_actual', 'q1_remark',
            'q2_plan', 'q2_actual', 'q2_remark',
            'q3_plan', 'q3_actual', 'q3_remark',
            'q4_plan', 'q4_actual', 'q4_remark',
            'means_of_verification', 'remarks'
        ]


class OutputSerializer(serializers.ModelSerializer):
    activities = ActivitySerializer(many=True, read_only=True)

    class Meta:
        model = Output
        fields = [
            'id', 'description', 'unit',
            'q1_plan', 'q1_actual', 'q1_remark',
            'q2_plan', 'q2_actual', 'q2_remark',
            'q3_plan', 'q3_actual', 'q3_remark',
            'q4_plan', 'q4_actual', 'q4_remark',
            'means_of_verification', 'remarks', 'activities'
        ]


class OutcomeSummarySerializer(serializers.ModelSerializer):
    class Meta:
        model = Outcome
        fields = [
            'id', 'outcome_statement' 
        ]


class ImpactSerializer(serializers.ModelSerializer):
    organization_id = serializers.PrimaryKeyRelatedField(
        queryset=Organization.objects.all(),
        write_only=True,
        required=False
    )
    grant_id = serializers.PrimaryKeyRelatedField(
        queryset=Grant.objects.all(),
        write_only=True,
        required=False
    )
    organization = OrganizationSerializer(read_only=True)
    grant = GrantSerializer(read_only=True)
    organization = OrganizationSerializer(read_only=True)
    outcomes = OutcomeSummarySerializer(many=True, read_only=True)

    class Meta:
        model = Impact
        fields = [
            'id', 'organization_id', 'organization', 'grant_id', 'grant',
            'year', 'impact_statement', 'needs_assessment',
            'pre_intervention_assessment', 'post_intervention_assessment', 'tools_used', 'remarks', 'outcomes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['organization', 'grant', 'created_at', 'updated_at']

    def validate(self, attrs):
        grant = attrs.get('grant_id', getattr(self.instance, 'grant', None)) 
        organization = attrs.get('organization_id', getattr(self.instance, 'organization', None)) 
        year = attrs.get('year', getattr(self.instance, 'year', None))       
        quarter = attrs.get('quarter', getattr(self.instance, 'quarter', None)) 

        if self.instance and not any(attrs.keys()):
            return attrs

        if not all([grant, organization, year, quarter]):
            return attrs

        if self.instance:
            if (
                self.instance.grant_id == grant.id and
                self.instance.organization_id == organization.id and
                self.instance.year == year and
                self.instance.quarter == quarter
            ):
                return attrs

        raise serializers.ValidationError(
            "Few values after validation didn't match"
        )

    def create(self, validated_data):
        grant = validated_data.pop('grant_id', None)
        organization = validated_data.pop('organization_id', None)

        validated_data.pop('organization', None)
        validated_data.pop('grant', None)

        if not organization:
            raise serializers.ValidationError("Organization context missing.")

        return Impact.objects.create(
            organization=organization,
            grant=grant,
            **validated_data
        )

    def update(self, instance, validated_data):
        grant = validated_data.pop('grant_id', None)

        validated_data.pop('grant', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance

class OutcomeSerializer(serializers.ModelSerializer):
    outputs = OutputSerializer(many=True, read_only=True)
    impact = ImpactSerializer(read_only=True)  # full nested impact

    class Meta:
        model = Outcome
        fields = [
            'id', 'outcome_statement', 'pre_intervention_assessment',
            'post_intervention_assessment', 'tools_used', 'outputs', 'impact'
        ]


class ReportsGallerySerializer(serializers.ModelSerializer):
    attachment_url = serializers.ReadOnlyField()
    organization = OrganizationSerializer(read_only=True)

    class Meta:
        model = ReportsGallery
        fields = [
            "id", "title", "s3_key", "file_name", "file_type",
            "uploaded_at", "description", "attachment_url", "organization",
        ]
        read_only_fields = ["uploaded_at", "attachment_url"]
