from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from aws_services.views import GeneratePresignedURL
from reports.views import (GranteeActivityViewSet, GranteeImpactViewSet,
                           GranteeNarrativeReportViewSet, GranteeOutcomeViewSet,
                           GranteeOutputViewSet, GranteeReportsGalleryViewSet,
                           GrantmakerActivityViewSet, GrantmakerImpactViewSet,
                           GrantmakerNarrativeReportViewSet,
                           GrantmakerOutcomeViewSet, GrantmakerOutputViewSet,
                           GrantmakerReportsGalleryViewSet)

router = DefaultRouter()

# Narrative Reports
router.register("narrative", GranteeNarrativeReportViewSet, basename="narrative-reports")
router.register("grantmaker/narrative", GrantmakerNarrativeReportViewSet, basename="grantmaker-narrative-reports")


# Quantitative Reports - Grantee side
router.register("quantitative/impacts", GranteeImpactViewSet, basename="grantee-impacts")
router.register("quantitative/outcomes", GranteeOutcomeViewSet, basename="grantee-outcomes")
router.register("quantitative/outputs", GranteeOutputViewSet, basename="grantee-outputs")
router.register("quantitative/activities", GranteeActivityViewSet, basename="grantee-activities")

# Quantitative Reports - Grantmaker side 
router.register("grantmaker/quantitative/outcomes", GrantmakerOutcomeViewSet, basename="grantmaker-outcomes")
router.register("grantmaker/quantitative/outputs", GrantmakerOutputViewSet, basename="grantmaker-outputs")
router.register("grantmaker/quantitative/activities", GrantmakerActivityViewSet, basename="grantmaker-activities")
router.register("grantmaker/quantitative/impacts", GrantmakerImpactViewSet, basename="grantmaker-impacts")

# Reports Gallery
router.register("gallery", GranteeReportsGalleryViewSet, basename="grantee-reports-gallery")
router.register("grantmaker/gallery", GrantmakerReportsGalleryViewSet, basename="grantmaker-reports-gallery")

urlpatterns = [
    path("gallery/upload/", GeneratePresignedURL.as_view(), name="reports-gallery-presign"),
    path("", include(router.urls)),
]
