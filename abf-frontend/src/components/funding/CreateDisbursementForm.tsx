"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { createDisbursement, fetchAllGrants } from "@/services/disbursement-service";
import { toast } from "sonner";
import { motion } from "framer-motion";
import {
  Plus,
  Trash2,
  FileText,
  Calendar,
  DollarSign,
  MessageSquare,
  Settings,
  Save,
  Loader2
} from "lucide-react";

interface Grant {
  id: number;
  grant_name: string;
}

interface DisbursementRow {
  tranche_number: string;
  scheduled_date: string;
  amount: string;
  remarks: string;
  status: string;
}

const statusOptions = [
  { value: "yet_to_be_disbursed", label: "Yet to be Disbursed", icon: Calendar },
  { value: "disbursed", label: "Disbursed", icon: Save },
  { value: "on_hold", label: "On Hold", icon: Settings },
];

export default function CreateDisbursementForm({ onCreated }: { onCreated: () => void }) {
  const [grantId, setGrantId] = useState("");
  const [grants, setGrants] = useState<Grant[]>([]);
  const [rows, setRows] = useState<DisbursementRow[]>([
    { tranche_number: "", scheduled_date: "", amount: "", remarks: "", status: "yet_to_be_disbursed" },
  ]);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchAllGrants().then(setGrants);
  }, []);

  const handleRowChange = (index: number, field: string, value: string) => {
    setRows(prev =>
      prev.map((row, i) => (i === index ? { ...row, [field]: value } : row))
    );
  };

  const addRow = () => {
    setRows(prev => [...prev, { tranche_number: "", scheduled_date: "", amount: "", remarks: "", status: "yet_to_be_disbursed" }]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!grantId) return toast.error("Please select a grant");

    try {
      setSubmitting(true);
      for (const row of rows) {
        await createDisbursement({
          grant: Number(grantId),
          tranche_number: Number(row.tranche_number),
          scheduled_date: row.scheduled_date,
          amount: row.amount,
          remarks: row.remarks
        });
      }
      toast.success("All disbursements added");
      setRows([{ tranche_number: "", scheduled_date: "", amount: "", remarks: "", status: "yet_to_be_disbursed" }]);
      setGrantId("");
      onCreated();
    } catch (err) {
      toast.error("Failed to submit disbursements");
      console.error(err);
    } finally {
      setSubmitting(false);
    }
  };

  const removeRow = (index: number) => {
    if (rows.length > 1) {
      setRows(prev => prev.filter((_, i) => i !== index));
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="space-y-6"
    >
      <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <FileText className="h-5 w-5 text-[#00998F]" />
            Add New Disbursement
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Grant Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <FileText className="h-4 w-4 text-teal-500" />
                Select Grant
              </Label>
              <Select value={grantId} onValueChange={setGrantId}>
                <SelectTrigger className="border-teal-200 focus:ring-teal-500">
                  <SelectValue placeholder="Choose a grant" />
                </SelectTrigger>
                <SelectContent>
                  {grants.map((grant) => (
                    <SelectItem key={grant.id} value={grant.id.toString()}>
                      {grant.grant_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Disbursement Rows */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-lg font-semibold text-gray-800">Disbursement Details</Label>
                <Button
                  type="button"
                  variant="outline"
                  onClick={addRow}
                  className="border-teal-200 text-teal-700 hover:bg-teal-50 flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Tranche
                </Button>
              </div>

              {rows.map((row, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="border border-teal-100 p-6 rounded-xl bg-gradient-to-br from-teal-50/30 to-white shadow-sm hover:shadow-md transition-shadow duration-300"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-md font-semibold text-gray-800 flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-teal-500" />
                      Tranche {index + 1}
                    </h4>
                    {rows.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeRow(index)}
                        className="border-red-200 text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <Calendar className="h-3 w-3 text-teal-500" />
                        Tranche Number
                      </Label>
                      <Input
                        type="number"
                        value={row.tranche_number}
                        onChange={e => handleRowChange(index, "tranche_number", e.target.value)}
                        placeholder="Enter tranche number"
                        className="border-teal-200 focus:ring-teal-500"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <Calendar className="h-3 w-3 text-teal-500" />
                        Scheduled Date
                      </Label>
                      <Input
                        type="date"
                        value={row.scheduled_date}
                        onChange={e => handleRowChange(index, "scheduled_date", e.target.value)}
                        className="border-teal-200 focus:ring-teal-500"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <DollarSign className="h-3 w-3 text-teal-500" />
                        Amount (₹)
                      </Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={row.amount}
                        onChange={e => handleRowChange(index, "amount", e.target.value)}
                        placeholder="Enter amount"
                        className="border-teal-200 focus:ring-teal-500"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <Settings className="h-3 w-3 text-teal-500" />
                        Status
                      </Label>
                      <Select
                        value={row.status}
                        onValueChange={(value) => handleRowChange(index, "status", value)}
                      >
                        <SelectTrigger className="border-teal-200 focus:ring-teal-500">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {statusOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                <option.icon className="h-3 w-3" />
                                {option.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="md:col-span-2 space-y-2">
                      <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <MessageSquare className="h-3 w-3 text-teal-500" />
                        Remarks
                      </Label>
                      <Textarea
                        value={row.remarks}
                        onChange={e => handleRowChange(index, "remarks", e.target.value)}
                        placeholder="Enter any additional remarks..."
                        className="border-teal-200 focus:ring-teal-500 min-h-[80px]"
                        rows={3}
                      />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-4 border-t border-gray-100">
              <Button
                type="submit"
                disabled={submitting || !grantId}
                className="bg-[#00998F] hover:bg-teal-700 text-white px-8 py-2 flex items-center gap-2 min-w-[200px]"
              >
                {submitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    Save All Disbursements
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
