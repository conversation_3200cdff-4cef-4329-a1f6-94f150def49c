"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@/components/ui/select";
import { createDisbursement, fetchAllGrants } from "@/services/disbursement-service";
import { toast } from "sonner";

export default function CreateDisbursementForm({ onCreated }: { onCreated: () => void }) {
  const [grantId, setGrantId] = useState("");
  const [grants, setGrants] = useState<any[]>([]);
  const [rows, setRows] = useState([
    { tranche_number: "", scheduled_date: "", amount: "", remarks: "", status: "yet_to_be_disbursed" },
  ]);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchAllGrants().then(setGrants);
  }, []);

  const handleRowChange = (index: number, field: string, value: string) => {
    setRows(prev =>
      prev.map((row, i) => (i === index ? { ...row, [field]: value } : row))
    );
  };

  const addRow = () => {
    setRows(prev => [...prev, { tranche_number: "", scheduled_date: "", amount: "", remarks: "", status: "yet_to_be_disbursed" }]);
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    if (!grantId) return toast.error("Please select a grant");

    try {
      setSubmitting(true);
      for (const row of rows) {
        await createDisbursement({ grant: Number(grantId), ...row });
      }
      toast.success("All disbursements added");
      setRows([{ tranche_number: "", scheduled_date: "", amount: "", remarks: "", status: "yet_to_be_disbursed" }]);
      setGrantId("");
      onCreated();
    } catch (err) {
      toast.error("Failed to submit disbursements");
      console.error(err);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Label>
        Grant
        <select value={grantId} onChange={e => setGrantId(e.target.value)} className="mt-1 w-full border rounded p-2">
          <option value="">Select a grant</option>
          {grants.map(g => (
            <option key={g.id} value={g.id}>
              {g.grant_name}
            </option>
          ))}
        </select>
      </Label>

      {rows.map((row, index) => (
        <div key={index} className="border p-4 rounded-xl bg-gray-50 space-y-4">
          <Label>
            Tranche Number
            <Input
              type="number"
              name="tranche_number"
              value={row.tranche_number}
              onChange={e => handleRowChange(index, "tranche_number", e.target.value)}
              required
            />
          </Label>

          <Label>
            Scheduled Date
            <Input
              type="date"
              name="scheduled_date"
              value={row.scheduled_date}
              onChange={e => handleRowChange(index, "scheduled_date", e.target.value)}
              required
            />
          </Label>

          <Label>
            Amount
            <Input
              type="number"
              name="amount"
              step="0.01"
              value={row.amount}
              onChange={e => handleRowChange(index, "amount", e.target.value)}
              required
            />
          </Label>

          <Label>
            Remarks
            <Textarea
              name="remarks"
              value={row.remarks}
              onChange={e => handleRowChange(index, "remarks", e.target.value)}
            />
          </Label>

          <Label>
            Status
            <select
              name="status"
              value={row.status}
              onChange={e => handleRowChange(index, "status", e.target.value)}
              className="w-full mt-1 border rounded p-2"
            >
              <option value="yet_to_be_disbursed">Yet to be Disbursed</option>
              <option value="disbursed">Disbursed</option>
              <option value="on_hold">On Hold</option>
            </select>
          </Label>
        </div>
      ))}

      <div className="flex gap-4">
        <Button type="button" variant="outline" onClick={addRow}>
          + Add Row
        </Button>
        <Button type="submit" disabled={submitting}>
          {submitting ? "Submitting..." : "Save All Disbursements"}
        </Button>
      </div>
    </form>
  );
}
