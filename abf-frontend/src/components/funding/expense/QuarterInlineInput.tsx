import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";

export default function QuarterInlineInput({
  label,
  value,
  onChange,
  readOnly = false,
  rowId,
  type,
  totalGrantBudget,
  hasError = false,
  errorMessage = '',
  validate,
}: {
  label: string;
  value: number | undefined;
  onChange: (value: number) => void;
  readOnly?: boolean;
  rowId: number;
  type: 'budget' | 'actual';
  totalGrantBudget: number;
  hasError?: boolean;
  errorMessage?: string;
  validate?: (value: number, label: string) => { isValid: boolean; message: string };
}) {
  const [inputError, setInputError] = useState(errorMessage);

  const [inputValue, setInputValue] = useState('');

  const handleChange = (val: string) => {
    // Allow empty string, numbers, and decimal points
    if (val === '' || /^\d*\.?\d*$/.test(val)) {
      setInputValue(val);
      const parsedValue = val === '' ? 0 : parseFloat(val) || 0;
      let newError = '';
      if (validate && val !== '') {
        const validation = validate(parsedValue, label);
        newError = validation.isValid ? '' : validation.message;
        setInputError(newError);

      }
      onChange(parsedValue);
    }
  };


  const displayValue = inputValue !== '' ? inputValue :
    (value !== undefined && value !== null && value !== 0 ?
      (value % 1 === 0 ? value.toString() : value.toFixed(2).replace(/\.?0+$/, '')) :
      '');

  const handleFocus = () => {
    if (value !== undefined && value !== null && value !== 0) {
      setInputValue(value % 1 === 0 ? value.toString() : value.toString());
    }
  };

  const handleBlur = () => {
    setInputValue('');
  };


  return (
    <div
      className={`group relative rounded-xl bg-white border transition-all duration-200 ${
        hasError || inputError
          ? 'border-red-300 bg-red-50/50 shadow-sm'
          : readOnly
            ? 'border-gray-200 bg-gray-50/50'
            : 'border-gray-200 hover:border-teal-300 focus-within:border-teal-400 focus-within:shadow-sm focus-within:shadow-teal-200/30'
      }`}
    >

      <div className="p-4 space-y-3">
        <label className={`block text-xs font-semibold uppercase tracking-wider ${
          hasError || inputError ? 'text-red-600' : type === 'budget' ? 'text-teal-700' : 'text-emerald-700'
        }`}>
          {label}
        </label>
        <div className="flex items-center space-x-2">
          <span className="text-gray-500 text-sm font-medium">₹</span>
          <input
            type="text"
            value={displayValue}
            onChange={(e) => handleChange(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            readOnly={readOnly}
            className={`w-20 bg-transparent text-base font-semibold focus:outline-none placeholder:text-gray-400 ${
              hasError || inputError
                ? 'text-red-600'
                : readOnly
                  ? 'text-gray-500 cursor-not-allowed'
                  : type === 'budget'
                    ? 'text-teal-700'
                    : 'text-emerald-700'
            }`}
            placeholder="0"
          />
        </div>
        {(hasError || inputError) && (
          <div className="pt-2 border-t border-red-200">
            <p className="text-red-600 text-xs font-medium flex items-start gap-2">
              <span className="w-1 h-1 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></span>
              <span>{inputError || errorMessage}</span>
            </p>
          </div>
        )}
      </div>

    </div>
  );
}

