import { Button } from "@/components/ui/button";
import { X, CheckCir<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface SuccessPopupProps {
    successPopup: string | null;
    setSuccessPopup: (message: string | null) => void;
}

export default function SuccessPopup({ successPopup, setSuccessPopup }: SuccessPopupProps) {
    if (!successPopup) return null;

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-md z-50 p-4"
                onClick={() => setSuccessPopup(null)}
            >
                <motion.div
                    initial={{ scale: 0.9, opacity: 0, y: 20 }}
                    animate={{ scale: 1, opacity: 1, y: 0 }}
                    exit={{ scale: 0.9, opacity: 0, y: 20 }}
                    transition={{ type: "spring", duration: 0.5 }}
                    className="bg-white rounded-3xl shadow-2xl w-full max-w-lg overflow-hidden"
                    onClick={(e) => e.stopPropagation()}
                >
                    {/* Header */}
                    <div className="relative bg-gradient-to-r from-green-600 to-emerald-600 p-6 text-white">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-4">
                                <div className="p-3 bg-white/20 rounded-xl shadow-lg">
                                    <CheckCircle className="w-7 h-7 text-white" />
                                </div>
                                <div>
                                    <h2 className="text-2xl font-bold tracking-tight">Success!</h2>
                                    <p className="text-green-100 text-base mt-1 font-medium">Operation completed successfully</p>
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                onClick={() => setSuccessPopup(null)}
                                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                            >
                                <X className="w-6 h-6 text-white" />
                            </Button>
                        </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                        <div className="bg-green-50 border-l-4 border-green-500 rounded-r-lg p-6 shadow-sm">
                            <div className="flex items-center gap-4">
                                <div className="flex-shrink-0">
                                    <div className="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center">
                                        <CheckCircle className="w-7 h-7" />
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <div className="text-green-800 text-lg leading-relaxed font-semibold">
                                        {successPopup}
                                    </div>
                                    <div className="text-green-700 text-sm mt-2 font-medium">
                                        Your changes have been saved and processed successfully.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="px-6 py-4 bg-green-50 border-t border-green-200">
                        <div className="flex justify-center">
                            <Button
                                onClick={() => setSuccessPopup(null)}
                                className="bg-green-600 hover:bg-green-700 text-white px-8 py-2.5 rounded-lg font-semibold transition-colors shadow-md"
                            >
                                Continue
                            </Button>
                        </div>
                    </div>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
}