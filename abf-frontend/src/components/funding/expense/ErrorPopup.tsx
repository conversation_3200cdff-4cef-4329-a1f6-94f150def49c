import { But<PERSON> } from "@/components/ui/button";
import { X, AlertTriangle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface ErrorPopupProps {
  errorPopup: string | null;
  setErrorPopup: (message: string | null) => void;
}

export default function ErrorPopup({ errorPopup, setErrorPopup }: ErrorPopupProps) {
  if (!errorPopup) return null;

  const errorLines = errorPopup.split('\n').filter(line => line.trim());

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-md z-50 p-4"
        onClick={() => setErrorPopup(null)}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="bg-white rounded-2xl shadow-xl w-full max-w-md max-h-[75vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-5 text-white bg-gradient-to-r from-red-500 to-red-600">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  <AlertTriangle className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold">
                    Validation Required
                  </h2>
                  <p className="text-red-100 text-sm mt-1">
                    Please review and fix the issues below
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                onClick={() => setErrorPopup(null)}
                className="p-1.5 hover:bg-white/20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-white" />
              </Button>
            </div>
          </div>

          <div className="p-5 space-y-3 max-h-[50vh] overflow-y-auto">
            <div className="space-y-2">
              {errorLines.map((line, index) => {
                if (line.includes('validation errors') || line.includes('Backend validation errors') || line.includes('Please fix the following validation errors:')) {
                  return null;
                }

                if (line.trim() && !line.includes('Please fix')) {
                  // Clean the message and remove row number
                  const cleanedLine = line.replace(/^Row \d+:\s*/, '');

                  return (
                    <div key={index} className="bg-red-50 border-l-3 border-red-400 rounded-r-lg p-3 shadow-sm">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0">
                          <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                        </div>
                        <div className="flex-1">
                          <p className="text-red-800 text-sm leading-relaxed font-medium">
                            {cleanedLine}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                }

                return null;
              })}
            </div>
          </div>

          <div className="px-5 py-3 bg-red-50 border-t border-red-200">
            <div className="flex justify-between items-center">
              <div className="text-red-700 text-sm font-medium">
                Please fix all errors before submitting
              </div>
              <Button
                onClick={() => setErrorPopup(null)}
                className="bg-red-500 hover:bg-red-600 text-white px-5 py-2 rounded-lg font-medium transition-colors"
              >
                Close
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}