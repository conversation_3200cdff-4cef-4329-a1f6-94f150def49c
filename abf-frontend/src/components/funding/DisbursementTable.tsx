"use client";

import React, { useState, useEffect } from "react";
import { AlertCircle, Calendar, File, Upload, RefreshCcw } from "lucide-react";
import { motion } from "framer-motion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  fetchAllDisbursements,
  uploadDisbursementFile,
} from "@/services/disbursement-service";

interface DisbursementHistory {
  id: string;
  grantName: string;
  trancheNumber?: number;
  date: string;
  amount: number;
  status: string;
  statusDisplay: string;
  remark?: string;
  requestLetterUrl: string | null;
  acknowledgementLetterUrl: string | null;
}

export default function DisbursementTable() {
  const [disbursements, setDisbursements] = useState<DisbursementHistory[]>([]);
  const [filtered, setFiltered] = useState<DisbursementHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedGrant, setSelectedGrant] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  useEffect(() => {
    (async () => {
      try {
        const data = await fetchAllDisbursements();
        const transformed = data.map((item: any) => ({
          id: item.id?.toString(),
          grantName: item.grant_name || "Default Grant",
          trancheNumber: item.tranche_number || undefined,
          date: item.scheduled_date,
          amount: parseFloat(item.amount),
          status: item.status,
          statusDisplay: item.status_display,
          remark: item.remarks || "-",
          requestLetterUrl: item.request_letter_url,
          acknowledgementLetterUrl: item.acknowledgement_letter_url,
        }));
        setDisbursements(transformed);
        setFiltered(transformed);
      } catch (err: any) {
        setError("Failed to load disbursement history: " + err.message);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  useEffect(() => {
    let results = disbursements;

    if (selectedGrant !== "all") {
      results = results.filter((d) => d.grantName === selectedGrant);
    }

    if (selectedStatus !== "all") {
      results = results.filter(
        (d) => d.statusDisplay.toLowerCase() === selectedStatus.toLowerCase()
      );
    }
    setFiltered(results);
  }, [selectedGrant, selectedStatus, disbursements]);

  const handleUpload = async (
    id: string,
    file: File,
    field: "request_letter" | "acknowledgement_letter"
  ) => {
    try {
      const updated = await uploadDisbursementFile(id, file, field);
      const updatedItem: DisbursementHistory = {
        id: updated.id.toString(),
        grantName: updated.grant_name,
        trancheNumber: updated.tranche_number,
        date: updated.scheduled_date,
        amount: parseFloat(updated.amount),
        status: updated.status,
        statusDisplay: updated.status_display,
        remark: updated.remarks || "-",
        requestLetterUrl: updated.request_letter_url,
        acknowledgementLetterUrl: updated.acknowledgement_letter_url,
      };
      setDisbursements((prev) =>
        prev.map((item) => (item.id === id ? updatedItem : item))
      );
    } catch (err: any) {
      setError(`Upload failed: ${err.message}`);
    }
  };

  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    id: string,
    field: "request_letter" | "acknowledgement_letter"
  ) => {
    const file = e.target.files?.[0];
    if (file?.type === "application/pdf") {
      handleUpload(id, file, field);
    } else {
      setError("Please upload a valid PDF file");
    }
  };

  const grants = Array.from(new Set(disbursements.map((d) => d.grantName))).sort();
  const statuses = ["Yet to be disbursed", "Disbursed", "On Hold"];

  const formatDate = (date: string) =>
    new Date(date).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "short",
      day: "2-digit",
    });

  const formatCurrency = (val: number) =>
    new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
    }).format(val);

  const badgeColor = (status: string) => {
    const s = status.toLowerCase();
    if (s.includes("disbursed")) return "bg-green-100 text-green-800 border-green-200";
    if (s.includes("yet")) return "bg-amber-100 text-amber-800 border-amber-200";
    if (s.includes("hold")) return "bg-blue-100 text-blue-800 border-blue-200";
    return "bg-gray-100 text-gray-800 border-gray-200";
  };

  if (loading)
    return (
      <div className="text-center p-6 text-gray-600 animate-pulse">
        Loading disbursement history...
      </div>
    );

  if (error)
    return (
      <div className="text-center p-6 text-red-500 flex items-center justify-center gap-2">
        <AlertCircle className="h-5 w-5" />
        {error}
      </div>
    );

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-3xl font-semibold text-gray-800">Disbursement History</h2>
        <div className="flex items-center gap-4">
          <Label className="text-sm">
            Grant:
            <select
              className="ml-2 px-2 py-1 border"
              value={selectedGrant}
              onChange={(e) => setSelectedGrant(e.target.value)}
            >
              <option value="all">All</option>
              {grants.map((g) => (
                <option key={g}>{g}</option>
              ))}
            </select>
          </Label>
          <Label className="text-sm">
            Status:
            <select
              className="ml-2 px-2 py-1 border"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              <option value="all">All</option>
              {statuses.map((s) => (
                <option key={s}>{s}</option>
              ))}
            </select>
          </Label>
        </div>
      </div>

      <div className="overflow-x-auto border rounded-lg shadow bg-white">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              {[
                "Grant",
                "Tranche",
                "Date",
                "Amount",
                "Status",
                "Remark",
                "Request Letter",
                "Acknowledgement Letter",
              ].map((head) => (
                <th
                  key={head}
                  className="px-4 py-2 text-left text-sm text-gray-600"
                >
                  {head}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filtered.map((item, idx) => (
              <motion.tr
                key={item.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: idx * 0.03 }}
                className="border-t"
              >
                <td className="px-4 py-3">
                  <div className="font-medium text-gray-800">{item.grantName}</div>
                  <div className="text-xs text-gray-500">ID: {item.id}</div>
                </td>
                <td className="px-4 py-3">{item.trancheNumber || "-"}</td>
                <td className="px-4 py-3">{formatDate(item.date)}</td>
                <td className="px-4 py-3 text-teal-700">{formatCurrency(item.amount)}</td>
                <td className="px-4 py-3">
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium border ${badgeColor(
                      item.statusDisplay
                    )}`}
                  >
                    {item.statusDisplay}
                  </span>
                </td>
                <td className="px-4 py-3">{item.remark}</td>

                {["request_letter", "acknowledgement_letter"].map((field) => {
                  const fileUrl =
                    field === "request_letter"
                      ? item.requestLetterUrl
                      : item.acknowledgementLetterUrl;
                  const labelId = `${field}-upload-${item.id}`;
                  return (
                    <td key={field} className="px-4 py-3 space-y-2">
                      {fileUrl && (
                        <a
                          href={fileUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-2 px-3 py-2 rounded border bg-green-50 text-green-700 hover:bg-green-100"
                        >
                          <File className="h-4 w-4" />
                          <span>View PDF</span>
                        </a>
                      )}
                      <Label htmlFor={labelId} className="cursor-pointer block">
                        <div className="flex items-center gap-2 px-3 py-2 border rounded bg-gray-100 hover:bg-blue-50 text-sm text-blue-700">
                          <Upload className="h-4 w-4" />
                          <span>{fileUrl ? "Replace" : "Upload PDF"}</span>
                        </div>
                        <Input
                          id={labelId}
                          type="file"
                          accept=".pdf"
                          className="hidden"
                          onChange={(e) =>
                            handleFileChange(e, item.id, field as any)
                          }
                        />
                      </Label>
                    </td>
                  );
                })}
              </motion.tr>
            ))}
            {!filtered.length && (
              <tr>
                <td colSpan={8} className="text-center p-8 text-gray-500">
                  <div className="flex flex-col items-center">
                    <Calendar className="h-12 w-12 mb-4" />
                    <p className="text-lg">No disbursement history found</p>
                    <p className="text-sm">Try adjusting filters</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
