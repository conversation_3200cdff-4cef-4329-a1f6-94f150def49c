"use client";

import React, { useEffect, useState } from "react";
import { fetchAllDisbursements, fetchAllGrants, updateDisbursementStatus } from "@/services/disbursement-service";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

const statusOptions = [
  { value: "yet_to_be_disbursed", label: "Yet to be Disbursed" },
  { value: "disbursed", label: "Disbursed" },
  { value: "on_hold", label: "On Hold" },
];

export default function DisbursementTable() {
  const [disbursements, setDisbursements] = useState([]);
  const [grants, setGrants] = useState([]);
  const [selectedGrant, setSelectedGrant] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  useEffect(() => {
    fetchAllDisbursements().then(setDisbursements);
    fetchAllGrants().then(setGrants);
  }, []);

  const handleStatusChange = async (id, newStatus) => {
    try {
      await updateDisbursementStatus(id, newStatus);
      setDisbursements(prev =>
        prev.map(item => (item.id === id ? { ...item, status: newStatus } : item))
      );
      toast.success("Status updated successfully");
    } catch (error) {
      toast.error("Failed to update status");
    }
  };

  const filtered = disbursements.filter(item => {
    const grantMatch = selectedGrant === "all" || item.grant === parseInt(selectedGrant);
    const statusMatch = selectedStatus === "all" || item.status === selectedStatus;
    return grantMatch && statusMatch;
  });
  
  const totalAmount = filtered.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0);

  {filtered.length > 0 && (
  <tr className="border-t font-semibold bg-gray-50">
    <td colSpan={3} className="px-4 py-3 text-right">Total</td>
    <td className="px-4 py-3">₹{totalAmount.toLocaleString("en-IN")}</td>
    <td colSpan={4}></td>
  </tr>
)}

  return (
    <div className="space-y-6">
      <div className="flex gap-4">
        <div>
          <label className="block mb-1 text-sm font-medium">Grant</label>
          <select
            className="border px-3 py-1 rounded"
            value={selectedGrant}
            onChange={e => setSelectedGrant(e.target.value)}
          >
            <option value="all">All</option>
            {grants.map(grant => (
              <option key={grant.id} value={grant.id}>{grant.grant_name}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block mb-1 text-sm font-medium">Status</label>
          <select
            className="border px-3 py-1 rounded"
            value={selectedStatus}
            onChange={e => setSelectedStatus(e.target.value)}
          >
            <option value="all">All</option>
            {statusOptions.map(option => (
              <option key={`status-${option.value}`} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>
      </div>

      <table className="w-full text-sm border border-gray-300">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left">Grant</th>
            <th className="px-4 py-2">Tranche</th>
            <th className="px-4 py-2">Date</th>
            <th className="px-4 py-2">Amount</th>
            <th className="px-4 py-2">Remark</th>
            <th className="px-4 py-2">Request</th>
            <th className="px-4 py-2">Ack</th>
            <th className="px-4 py-2">Edit Status</th>
          </tr>
        </thead>
        <tbody>
          {filtered.map(item => (
            <tr key={item.id} className="border-t">
              <td className="px-4 py-3">{item.grant_name}</td>
              <td className="px-4 py-3">{item.tranche_number}</td>
              <td className="px-4 py-3">{item.scheduled_date}</td>
              <td className="px-4 py-3">₹{item.amount}</td>
              <td className="px-4 py-3">{item.remarks}</td>
              <td className="px-4 py-3">{item.request_letter_url ? <a href={item.request_letter_url} target="_blank" className="text-blue-600 underline">View PDF</a> : "—"}</td>
              <td className="px-4 py-3">{item.acknowledgement_letter_url ? <a href={item.acknowledgement_letter_url} target="_blank" className="text-blue-600 underline">View PDF</a> : "—"}</td>
              <td className="px-4 py-3">
                <select
                  className="border px-2 py-1 text-sm"
                  value={item.status}
                  onChange={e => handleStatusChange(item.id, e.target.value)}
                >
                  {statusOptions.map(option => (
                    <option key={`opt-${item.id}-${option.value}`} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </td>
            </tr>
          ))}
        </tbody>
                <tfoot>
          {filtered.length > 0 && (
            <tr className="border-t font-semibold bg-gray-50">
              <td colSpan={3} className="px-4 py-3 text-right">Total</td>
              <td className="px-4 py-3">₹{totalAmount.toLocaleString("en-IN")}</td>
              <td colSpan={4}></td>
            </tr>
          )}
        </tfoot>
      </table>
    </div>
  );
}
