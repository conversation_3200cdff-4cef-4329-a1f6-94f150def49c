"use client";

import React, { useEffect, useState } from "react";
import { fetchAllDisbursements, fetchAllGrants, updateDisbursementStatus } from "@/services/disbursement-service";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { motion } from "framer-motion";
import {
  Filter,
  Download,
  FileText,
  Calendar,
  DollarSign,
  Eye,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react";

interface Disbursement {
  id: number;
  grant: number;
  grant_name: string;
  tranche_number: string;
  scheduled_date: string;
  amount: string;
  remarks: string;
  status: string;
  request_letter_url?: string;
  acknowledgement_letter_url?: string;
}

interface Grant {
  id: number;
  grant_name: string;
}

const statusOptions = [
  { value: "yet_to_be_disbursed", label: "Yet to be Disbursed", color: "bg-amber-100 text-amber-800 border-amber-200" },
  { value: "disbursed", label: "Disbursed", color: "bg-emerald-100 text-emerald-800 border-emerald-200" },
  { value: "on_hold", label: "On Hold", color: "bg-red-100 text-red-800 border-red-200" },
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case "disbursed":
      return <CheckCircle className="h-3 w-3" />;
    case "on_hold":
      return <AlertCircle className="h-3 w-3" />;
    default:
      return <Clock className="h-3 w-3" />;
  }
};

export default function DisbursementTable() {
  const [disbursements, setDisbursements] = useState<Disbursement[]>([]);
  const [grants, setGrants] = useState<Grant[]>([]);
  const [selectedGrant, setSelectedGrant] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const [disbursementsData, grantsData] = await Promise.all([
          fetchAllDisbursements(),
          fetchAllGrants()
        ]);
        setDisbursements(disbursementsData);
        setGrants(grantsData);
      } catch (error) {
        toast.error("Failed to load data");
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, []);

  const handleStatusChange = async (id: number, newStatus: string) => {
    try {
      await updateDisbursementStatus(id.toString(), newStatus);
      setDisbursements(prev =>
        prev.map(item => (item.id === id ? { ...item, status: newStatus } : item))
      );
      toast.success("Status updated successfully");
    } catch (error) {
      toast.error("Failed to update status");
    }
  };

  const filtered = disbursements.filter(item => {
    const grantMatch = selectedGrant === "all" || item.grant === parseInt(selectedGrant);
    const statusMatch = selectedStatus === "all" || item.status === selectedStatus;
    return grantMatch && statusMatch;
  });

  const totalAmount = filtered.reduce((sum, item) => sum + parseFloat(item.amount || "0"), 0);

  // Calculate summary statistics
  const summaryStats = {
    total: filtered.length,
    disbursed: filtered.filter(item => item.status === "disbursed").length,
    pending: filtered.filter(item => item.status === "yet_to_be_disbursed").length,
    onHold: filtered.filter(item => item.status === "on_hold").length,
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics Cards */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <Card className="bg-gradient-to-br from-teal-50 to-teal-100/50 border-teal-200 shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-teal-700">Total</p>
                <p className="text-2xl font-bold text-teal-900">{summaryStats.total}</p>
              </div>
              <div className="p-2 bg-teal-200 rounded-lg">
                <TrendingUp className="h-5 w-5 text-teal-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200 shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700">Disbursed</p>
                <p className="text-2xl font-bold text-emerald-900">{summaryStats.disbursed}</p>
              </div>
              <div className="p-2 bg-emerald-200 rounded-lg">
                <CheckCircle className="h-5 w-5 text-emerald-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-amber-50 to-amber-100/50 border-amber-200 shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-amber-700">Pending</p>
                <p className="text-2xl font-bold text-amber-900">{summaryStats.pending}</p>
              </div>
              <div className="p-2 bg-amber-200 rounded-lg">
                <Clock className="h-5 w-5 text-amber-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 border-red-200 shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-700">On Hold</p>
                <p className="text-2xl font-bold text-red-900">{summaryStats.onHold}</p>
              </div>
              <div className="p-2 bg-red-200 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Filters Section */}
      <motion.div
        className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.1 }}
      >
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-teal-50 rounded-md">
              <Filter className="h-4 w-4 text-teal-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800">Filter Disbursements</h3>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex items-center gap-1 border-teal-200 text-teal-700 hover:bg-teal-50">
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <FileText className="h-4 w-4 text-teal-500" />
              Grant
            </label>
            <Select value={selectedGrant} onValueChange={setSelectedGrant}>
              <SelectTrigger className="border-teal-200 focus:ring-teal-500">
                <SelectValue placeholder="Select grant" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Grants</SelectItem>
                {grants.map((grant) => (
                  <SelectItem key={grant.id} value={grant.id.toString()}>
                    {grant.grant_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Calendar className="h-4 w-4 text-teal-500" />
              Status
            </label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="border-teal-200 focus:ring-teal-500">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </motion.div>

      {/* Main Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
          <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>

          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <div className="rounded-lg border-0 shadow-sm overflow-hidden">
                <div className="max-h-[600px] overflow-y-auto">
                  <table className="w-full border-collapse bg-white min-w-[1200px]">
                    <thead className="sticky top-0 z-10">
                      <tr className="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 text-white border-b border-slate-600 shadow-lg">
                        <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Grant
                          </div>
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          <div className="flex items-center justify-center gap-2">
                            <TrendingUp className="h-4 w-4" />
                            Tranche
                          </div>
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          <div className="flex items-center justify-center gap-2">
                            <Calendar className="h-4 w-4" />
                            Date
                          </div>
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          <div className="flex items-center justify-center gap-2">
                            <DollarSign className="h-4 w-4" />
                            Amount
                          </div>
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          Remarks
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          Request
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          Acknowledgment
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-white uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-100">
                      {filtered.length === 0 ? (
                        <tr>
                          <td colSpan={8} className="px-6 py-12 text-center text-gray-500">
                            <div className="flex flex-col items-center gap-2">
                              <FileText className="h-8 w-8 text-gray-300" />
                              <p className="text-sm">No disbursements found</p>
                            </div>
                          </td>
                        </tr>
                      ) : (
                        filtered.map((item, index: number) => (
                          <motion.tr
                            key={item.id}
                            className="hover:bg-teal-50/50 transition-colors duration-200 border-b border-gray-100"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.05 }}
                          >
                            <td className="px-6 py-4 text-sm font-medium text-gray-900 border-r border-gray-100">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-teal-500"></div>
                                {item.grant_name}
                              </div>
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-700 text-center border-r border-gray-100">
                              <Badge variant="secondary" className="bg-teal-100 text-teal-800 border-teal-200">
                                Tranche {item.tranche_number}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-700 text-center border-r border-gray-100">
                              <div className="flex items-center justify-center gap-1">
                                <Calendar className="h-3 w-3 text-gray-400" />
                                {item.scheduled_date}
                              </div>
                            </td>
                            <td className="px-6 py-4 text-sm font-semibold text-gray-900 text-center border-r border-gray-100">
                              <div className="flex items-center justify-center gap-1">
                                <span className="text-teal-600">₹</span>
                                {parseFloat(item.amount || "0").toLocaleString("en-IN")}
                              </div>
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-700 border-r border-gray-100 max-w-xs">
                              <div className="truncate" title={item.remarks}>
                                {item.remarks || "—"}
                              </div>
                            </td>
                            <td className="px-6 py-4 text-sm text-center border-r border-gray-100">
                              {item.request_letter_url ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-teal-200 text-teal-700 hover:bg-teal-50"
                                  onClick={() => window.open(item.request_letter_url, '_blank')}
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </Button>
                              ) : (
                                <span className="text-gray-400">—</span>
                              )}
                            </td>
                            <td className="px-6 py-4 text-sm text-center border-r border-gray-100">
                              {item.acknowledgement_letter_url ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-teal-200 text-teal-700 hover:bg-teal-50"
                                  onClick={() => window.open(item.acknowledgement_letter_url, '_blank')}
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </Button>
                              ) : (
                                <span className="text-gray-400">—</span>
                              )}
                            </td>
                            <td className="px-6 py-4 text-sm text-center">
                              <Select
                                value={item.status}
                                onValueChange={(value) => handleStatusChange(item.id, value)}
                              >
                                <SelectTrigger className="w-full border-gray-200 focus:ring-teal-500 text-xs">
                                  <div className="flex items-center gap-2">
                                    {getStatusIcon(item.status)}
                                    <SelectValue />
                                  </div>
                                </SelectTrigger>
                                <SelectContent>
                                  {statusOptions.map(option => (
                                    <SelectItem key={option.value} value={option.value}>
                                      <div className="flex items-center gap-2">
                                        {getStatusIcon(option.value)}
                                        {option.label}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </td>
                          </motion.tr>
                        ))
                      )}
                    </tbody>
                    {filtered.length > 0 && (
                      <tfoot className="bg-gradient-to-r from-teal-50 to-emerald-50 border-t-2 border-teal-200">
                        <tr>
                          <td colSpan={3} className="px-6 py-4 text-right text-sm font-bold text-teal-800">
                            Total Amount:
                          </td>
                          <td className="px-6 py-4 text-center text-lg font-bold text-teal-900">
                            <div className="flex items-center justify-center gap-1">
                              <span className="text-teal-600">₹</span>
                              {totalAmount.toLocaleString("en-IN")}
                            </div>
                          </td>
                          <td colSpan={4} className="px-6 py-4 text-sm text-teal-700">
                            <div className="flex items-center gap-2">
                              <TrendingUp className="h-4 w-4" />
                              {filtered.length} disbursement{filtered.length !== 1 ? 's' : ''} found
                            </div>
                          </td>
                        </tr>
                      </tfoot>
                    )}
                  </table>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
