"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Layout } from "@/components/Layout";
import {
  ArrowLeft,
  Paperclip,
  Send,
  FileDown,
  MessageSquare,
  User,
  Shield,
  AlertCircle,
  Info,
  Tag,
  Folder,
  Flag,
  CalendarClock,
  Clock,
  ShieldCheck,
  XCircle
} from "lucide-react";
import SkeletonMessage from "@/components/SkeletonMessage";
import {
  getTicketById,
  getTicketUpdates,
  sendTicketUpdate,
  reopenTicket,
} from "@/services/supportTicket.service";


function Popup({ message, onClose }) {
  useEffect(() => {
    const timeout = setTimeout(onClose, 4000);
    return () => clearTimeout(timeout);
  }, [onClose]);

  return (
    <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50">
      <div className="flex items-center gap-3 bg-red-100 border border-red-300 text-red-800 px-5 py-3 rounded-xl shadow-lg animate-fade-in-down">
        <XCircle className="w-5 h-5 shrink-0" />
        <span className="text-sm font-medium">{message}</span>
      </div>
    </div>
  );
}

export default function TicketHistoryPage({ ticketId }: {ticketId: string | null}) {
  const router = useRouter();

  const [ticket, setTicket] = useState(null);
  const [updates, setUpdates] = useState([]);
  const [loadingTicket, setLoadingTicket] = useState(true);
  const [loadingUpdates, setLoadingUpdates] = useState(true);
  const [messageText, setMessageText] = useState("");
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [formError, setFormError] = useState("");
  const [popupMessage, setPopupMessage] = useState("");

  const fileInputRef = useRef(null);
  const bottomRef = useRef(null);

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase().replace(/\s+/g, "-")) {
      case "open": return "bg-gradient-to-r from-teal-50 to-teal-100 text-teal-800 border-teal-200 shadow-sm border";
      case "under-review": return "bg-gradient-to-r from-amber-50 to-orange-100 text-amber-800 border-amber-200 shadow-sm border";
      case "resolved": return "bg-gradient-to-r from-emerald-50 to-green-100 text-emerald-800 border-emerald-200 shadow-sm border";
      case "closed": return "bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 border-gray-200 shadow-sm border";
      default: return "bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 border-gray-200 shadow-sm border";
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case "low":
        return "bg-gray-100 text-gray-600";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "urgent":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };


  useEffect(() => {
    if (!ticketId) return;
    setLoadingTicket(true);
    getTicketById(ticketId).then((res) => {
      setTicket(res);
      setLoadingTicket(false);
    });
  }, [ticketId]);

  useEffect(() => {
    if (!ticket) return;
    setLoadingUpdates(true);
    getTicketUpdates(ticket.id).then((res) => {
      const formatted = res.map((u) => ({
        sender: u.created_by || "User",
        is_staff: u.is_staff || false,
        time: new Date(u.updated_at).toLocaleString(),
        text: u.update_text,
        attachments: u.attachments || [],
      }));
      setUpdates(formatted);
      setLoadingUpdates(false);
    });
  }, [ticket]);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [updates]);

  const handleSendMessage = async () => {
    if (!ticket || (!messageText.trim() && !file)) {
      setPopupMessage("Please enter a message or attach a file.");
      return;
    }
    
    if (["resolved", "closed"].includes(ticket.status?.toLowerCase())) {
      setPopupMessage("This ticket is resolved. Reopen it to continue the conversation.");
      return;
    }
  
    setFormError("");
    try {
      setLoading(true);
      const data = await sendTicketUpdate(ticket.id, messageText, file);
  
      setUpdates((prev) => [
        ...prev,
        {
          sender: "You",
          is_staff: false,
          time: new Date(data.updated_at).toLocaleString(),
          text: data.update_text,
          attachments: data.attachments || [],
        },
      ]);
  
      setMessageText("");
      setFile(null);
      if (fileInputRef.current) fileInputRef.current.value = "";
    } catch (err) {
      setPopupMessage("Failed to send update");
    } finally {
      setLoading(false);
    }
  };
  
  

  const handleExport = () => {
    if (ticket) {
      window.open(`/support-ticket/export?id=${ticket.id}`, "_blank");
    }
  };

  const handleReopen = async () => {
    try {
      const res = await reopenTicket(ticket.id);
      setTicket((prev) => ({ ...prev, status: res.status }));
      setUpdates((prev) => [
        ...prev,
        {
          sender: "System",
          is_staff: true,
          time: new Date(res.updated_at).toLocaleString(),
          text: "Ticket was reopened.",
        },
      ]);
    } catch (error) {
      setPopupMessage("Failed to reopen ticket.");
    }
  };

  return (
    <Layout title="Ticket History">
      {popupMessage && <Popup message={popupMessage} onClose={() => setPopupMessage("")} />}

      <div className="flex flex-col lg:flex-row gap-6 h-[calc(100vh-114px)] bg-white rounded-lg shadow-sm">
        <div className="flex flex-col lg:w-2/3 overflow-hidden">
          <div className="px-6 py-5 bg-gradient-to-r from-teal-50 to-teal-100 border-b border-teal-200">
            <div className="flex flex-col space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-teal-600 rounded-lg flex items-center justify-center">
                    <MessageSquare className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    {loadingTicket ? (
                      <div className="w-40 h-6 bg-teal-200 rounded animate-pulse" />
                    ) : (
                      <h1 className="text-xl font-bold text-teal-900">Ticket #{ticket?.id}</h1>
                    )}
                    <p className="text-sm text-teal-700">Support Conversation</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {!loadingTicket && (
                    <span className={`px-3 py-1 text-xs font-medium rounded-lg ${getStatusBadgeClass(ticket?.status)}`}>
                      {ticket?.status}
                    </span>
                  )}
                  <button
                    onClick={() => router.back()}
                    className="bg-gray-800 hover:bg-gray-900 text-white rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 flex items-center gap-2"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Back to Tickets
                  </button>
                </div>
              </div>
              {!loadingTicket && ticket && (
                <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Issue Title</p>
                      <p className="text-sm font-semibold text-gray-900 mt-1">{ticket.title}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Category</p>
                      <p className="text-sm font-semibold text-gray-900 mt-1">{ticket.category}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <section className="flex-1 overflow-y-auto p-6 space-y-4 bg-gradient-to-br from-gray-50 via-white to-teal-50 border-x border-gray-200">
            {loadingTicket ? (
              <SkeletonMessage />
            ) : (
              <div className="flex justify-end" style={{ animation: `fadeIn 0.4s ease-in-out` }}>
                <div className="max-w-full sm:max-w-lg p-4 border rounded-xl bg-gradient-to-br from-teal-600 to-teal-700 text-sm text-white shadow-lg">
                  <div className="font-medium mb-3 text-teal-100">
                    <div className="flex items-center gap-2 mb-1">
                      <User className="w-4 h-4" />
                      You
                    </div>
                    <div className="text-xs text-teal-200">{new Date(ticket?.created_at).toLocaleString()}</div>
                  </div>
                  <p className="whitespace-pre-wrap leading-relaxed">{ticket?.description}</p>
                  {ticket?.attachments?.map((url, i) => (
                    <a key={i} href={url} className="inline-flex items-center gap-1 text-xs text-teal-100 hover:text-white underline mt-3 p-2 bg-teal-800 rounded-lg transition-colors duration-200" target="_blank">
                      <Paperclip className="w-3 h-3" />
                      {new URL(url).pathname.split("/").pop()?.split("?")[0]}
                    </a>
                  ))}
                </div>
              </div>
            )}

            {loadingUpdates ? (
              <>
                <SkeletonMessage />
                <SkeletonMessage />
              </>
            ) : (
              updates.map((msg, i) => (
                <div key={i} className={`flex ${msg.is_staff ? "justify-start" : "justify-end"}`} style={{ animation: `fadeIn 0.4s ease-in-out ${i * 0.1}s` }}>
                  <div className={`max-w-full sm:max-w-lg p-4 border rounded-xl text-sm shadow-lg transition-all duration-300 hover:shadow-xl ${
                    msg.is_staff
                      ? "bg-gradient-to-br from-blue-50 to-white text-gray-700 border-blue-200"
                      : "bg-gradient-to-br from-teal-600 to-teal-700 text-white border-teal-600"
                  }`}>
                    <div className={`font-medium mb-3 ${msg.is_staff ? "text-blue-700" : "text-teal-100"}`}>
                      <div className="flex items-center gap-2 mb-1">
                        {msg.is_staff ? (
                          <Shield className="w-4 h-4" />
                        ) : (
                          <User className="w-4 h-4" />
                        )}
                        {msg.sender}
                      </div>
                      <div className={`text-xs ${msg.is_staff ? "text-blue-500" : "text-teal-200"}`}>{msg.time}</div>
                    </div>
                    <p className="whitespace-pre-wrap leading-relaxed mb-2">{msg.text}</p>
                    {msg.attachments?.map((url, j) => (
                      <a key={j} href={url} className={`inline-flex items-center gap-1 text-xs underline mt-2 p-2 rounded-lg transition-colors duration-200 ${
                        msg.is_staff
                          ? "text-blue-600 hover:text-blue-800 bg-blue-100 hover:bg-blue-200"
                          : "text-teal-100 hover:text-white bg-teal-800 hover:bg-teal-700"
                      }`}>
                        <Paperclip className="w-3 h-3" />
                        {new URL(url).pathname.split("/").pop()?.split("?")[0]}
                      </a>
                    ))}
                  </div>
                </div>
              ))
            )}
            <div ref={bottomRef} />
          </section>

          <footer className="p-4 border border-gray-200 bg-white rounded-b-xl shadow-sm">
            {formError && <div className="text-sm text-red-600 mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">{formError}</div>}
            {ticket && ["resolved", "closed"].includes(ticket.status?.toLowerCase()) ? (
              <div className="text-center p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <AlertCircle className="w-5 h-5 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-600 font-medium">This ticket is resolved. You can't send more messages unless you reopen it.</p>
              </div>
            ) : (
              <div className="flex items-center gap-4">
                <button
                  onClick={() => fileInputRef.current?.click()}
                  type="button"
                  className="group p-2 hover:bg-gray-100 rounded-lg transition-all duration-200"
                  title="Attach file"
                >
                  <Paperclip className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors duration-200" />
                </button>
                {file && (
                  <span className="text-xs text-gray-600 truncate max-w-[160px] bg-gray-50 px-3 py-1.5 rounded-lg border border-gray-200">
                    {file.name}
                  </span>
                )}
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={(e) => setFile(e.target.files?.[0] || null)}
                  className="hidden"
                  disabled={loading}
                />
                <textarea
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      if (!loading && (messageText.trim() || file)) {
                        handleSendMessage();
                      }
                    }
                  }}
                  placeholder="Type your message... (Press Enter to send, Shift+Enter for new line)"
                  rows={3}
                  className="flex-1 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-700 placeholder:text-gray-400 bg-gray-50 focus:outline-none focus:border-gray-400 focus:ring-2 focus:ring-gray-200 transition-all duration-300 ease-in-out resize-none hover:shadow-sm focus:shadow-md focus:bg-white"
                  disabled={loading}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={loading || !messageText.trim()}
                  type="button"
                  className="bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 disabled:bg-gray-300 disabled:text-gray-500 disabled:scale-100 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {loading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4" />
                      Send
                    </>
                  )}
                </button>
              </div>
            )}
          </footer>
        </div>

        <aside className="w-full lg:w-1/3">
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm h-fit">
            {loadingTicket ? (
              <div className="p-6 space-y-4 animate-pulse">
                <div className="w-3/4 h-5 bg-gray-200 rounded" />
                <div className="space-y-3">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="w-full h-4 bg-gray-200 rounded" />
                  ))}
                </div>
                <div className="w-full h-10 bg-gray-200 rounded mt-6" />
              </div>
            ) : (
              <>
                <div className="bg-gradient-to-r from-teal-50 to-white px-6 py-4 border-b border-gray-200 rounded-t-lg">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-teal-800 flex items-center gap-2">
                      <Info className="w-5 h-5 text-teal-600" />
                      Ticket Details
                    </h3>
                    {ticket?.status?.toLowerCase() === "resolved" && (
                      <button
                        onClick={handleReopen}
                        className="bg-teal-600 hover:bg-teal-700 text-white text-sm px-4 py-2 rounded-lg font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105"
                      >
                        Re-Open
                      </button>
                    )}
                  </div>
                </div>
                <div className="p-6">

                  <div className="space-y-4 text-sm">
                    {[
                      {
                        icon: <ShieldCheck className="w-4 h-4" />,
                        label: "Status",
                        value: (
                          <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(ticket?.status)}`}>
                            {ticket?.status}
                          </span>
                        )
                      },
                      {
                        icon: <Tag className="w-4 h-4" />,
                        label: "Title",
                        value: <span className="font-medium text-gray-800">{ticket?.title}</span>
                      },
                      {
                        icon: <Folder className="w-4 h-4" />,
                        label: "Category",
                        value: <span className="font-medium text-gray-800">{ticket?.category}</span>
                      },
                      {
                        icon: <Flag className="w-4 h-4" />,
                        label: "Priority",
                        value: (
                          <span className={`px-3 py-1 text-xs font-medium rounded-full ${getPriorityBadgeColor(ticket?.priority)}`}>
                            {ticket?.priority}
                          </span>
                        )
                      },
                      {
                        icon: <CalendarClock className="w-4 h-4" />,
                        label: "Created",
                        value: <span className="text-gray-700">{new Date(ticket?.created_at).toLocaleDateString()}</span>
                      },
                      {
                        icon: <Clock className="w-4 h-4" />,
                        label: "Updated",
                        value: <span className="text-gray-700">{new Date(ticket?.updated_at).toLocaleDateString()}</span>
                      }
                    ].map((item, i) => (
                      <div key={i} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg" style={{ animation: `fadeIn 0.4s ease-in-out ${i * 0.1}s` }}>
                        <div className="flex items-center gap-2 text-gray-600">
                          {item.icon}
                          <span className="font-medium">{item.label}</span>
                        </div>
                        <div className="text-right">
                          {item.value}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="p-6 border-t border-gray-200">
                  <button
                    onClick={handleExport}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 border border-gray-300 text-sm font-medium rounded-lg hover:bg-gray-50 transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 text-gray-700"
                  >
                    <FileDown className="w-4 h-4" />
                    Export Conversation
                  </button>
                </div>
              </>
            )}
          </div>
        </aside>
      </div>
    </Layout>
  );
}
