"use client";

import React, { useMemo } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
} from "../ui/select";
import { Loader2Icon, PencilIcon, PlusIcon } from "lucide-react";
import {
  useMutation,
  UseMutationResult,
  useQuery,
} from "@tanstack/react-query";
import { ImpactList } from "../reports/quantitative/Impacts";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { useForm, UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import apiClient from "@/lib/apiClient";
import { getGranteeProfile } from "@/services/grantmaker/grantee-service";
import { GranteeProfile } from "@/types/profile";
import {
  NarrativeReportGrantSchema,
  NarrativeReportGrantType,
  ReportGrantSelector,
} from "@/app/milestones-reports/_components/grant-selector";
import { Skeleton } from "../ui/skeleton";
import { grantSchema } from "@/types/grant";
import {
  getGrantmakerImpacts,
  GrantType,
} from "@/services/grantmaker/quantitative-service";
import { impact } from "@/data/quantitative";
import { Input } from "../ui/input";
import {
  ImpactSchema,
  ImpactType,
  ImpactTypeWithOrganization,
} from "@/types/quantitative";
import { toast } from "sonner";

export const addImpactSchema = ImpactSchema.omit({
  id: true,
}).extend({
  grant_id: z.number(),
});

export type AddImpactType = z.infer<typeof addImpactSchema>;

function getGrantsQuery(granteeId: string, form: UseFormReturn<AddImpactType>) {
  return useQuery<NarrativeReportGrantType[], Error>({
    queryKey: ["grants", granteeId],
    queryFn: async () => {
      const granteeProfile = (await getGranteeProfile(
        granteeId,
      )) as GranteeProfile;

      const filterParsedGrants = granteeProfile.grants.map((g) => ({
        id: Number(g.id),
        grant_name: g.name,
        start_date: g.startDate,
        end_date: g.endDate,
        grant_purpose: g.purpose,
        annual_budget: "",
        funding_sources: g.fundingSources ?? "",
        created_at: g.createdAt,
        updated_at: g.updatedAt,
        organization: g.organization,
        grant_maker_organization: g.grantMakerOrganization,
      }));

      const parsedGrant = z
        .array(NarrativeReportGrantSchema)
        .safeParse(filterParsedGrants); // to satisfy type checker again did a z.parse()

      if (!parsedGrant.success) return []; // type check fails

      // Filter grants available for this year and next
      const today = new Date();
      const yearFilteredGrants = parsedGrant.data.filter(
        (e) =>
          e.start_date.getFullYear() <= today.getFullYear() &&
          e.end_date.getFullYear() >= today.getFullYear(),
      );

      if (yearFilteredGrants.length < 1) return [];

      form.setValue("grant_id", yearFilteredGrants[0].id, {
        shouldDirty: true,
        shouldValidate: true,
      });
      form.setValue(
        "year",
        Math.max(
          yearFilteredGrants[0].start_date.getFullYear(),
          new Date().getFullYear(),
        ),
        {
          shouldDirty: true,
          shouldValidate: true,
        },
      );

      return yearFilteredGrants;
    },
  });
}

export function AddImpactDialog({
  granteeId,
  addImpact,
}: {
  granteeId: string;
  addImpact: UseMutationResult<unknown, unknown, AddImpactType>;
}) {
  const [open, setOpen] = React.useState(false);

  const form = useForm<AddImpactType>({
    resolver: zodResolver(addImpactSchema),
    defaultValues: {
      impact_statement: "",
      pre_intervention_assessment: "",
      post_intervention_assessment: "",
      tools_used: "",
      grant_id: undefined,
      year: undefined,
    },
  });

  function onSubmit(values: AddImpactType) {
    addImpact
      .mutateAsync(values)
      .then(() => {
        setOpen(false);
        toast.success("Created impact");
      })
      .catch((err) => {
        toast.error(err.message || "Failed to cretate impact");
      });
  }

  const grantsQuery = getGrantsQuery(granteeId, form);

  const getYearsForGrant = useMemo(() => {
    const today = new Date();

    if (!grantsQuery.data) return undefined;

    const grant = grantsQuery.data.find((e) => e.id === form.watch("grant_id"));

    if (!grant) return undefined;

    form.setValue(
      "year",
      Math.max(today.getFullYear(), grant.start_date.getFullYear()),
    );

    return {
      start_year: Math.max(today.getFullYear(), grant.start_date.getFullYear()),
      end_year: grant.end_date.getFullYear(),
    };
  }, [form.watch("grant_id")]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="w-fit self-end bg-teal-500 hover:bg-teal-500/80 duration-300">
          <PlusIcon /> Add Impact
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Impact</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-2">
            <FormField
              name="grant_id"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Grant</FormLabel>
                  {grantsQuery.isLoading && <Skeleton className="h-5 w-full" />}
                  {grantsQuery.data && (
                    <ReportGrantSelector
                      selectedGrant={field.value}
                      setSelectedGrant={field.onChange}
                      grants={grantsQuery.data}
                    />
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            {typeof form.watch("grant_id") !== "undefined" && (
              <FormField
                name="year"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Year</FormLabel>

                    <Select
                      onValueChange={field.onChange}
                      value={field.value.toString()}
                    >
                      <SelectTrigger>
                        <FormControl>
                          <SelectValue />
                        </FormControl>
                      </SelectTrigger>
                      <SelectContent>
                        {grantsQuery.isLoading && getYearsForGrant && (
                          <Skeleton className="h-5 w-full" />
                        )}
                        {grantsQuery.data &&
                          getYearsForGrant &&
                          Array.from({
                            length:
                              getYearsForGrant.end_year -
                              getYearsForGrant.start_year +
                              1,
                          }).map((_, i) => (
                            <SelectItem
                              key={getYearsForGrant.start_year + i}
                              value={`${getYearsForGrant.start_year + i}`}
                            >
                              {getYearsForGrant.start_year + i}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              name="impact_statement"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Statement</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              name="pre_intervention_assessment"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pre-intervention Assessment</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value ?? ""} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              name="post_intervention_assessment"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Post-intervention Assessment</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value ?? ""} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              name="tools_used"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tools Used</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value ?? ""} />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button className="mt-4">Submit</Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export function EditImpactButton({
  impact,
  editImpact,
}: {
  impact: ImpactTypeWithOrganization;
  editImpact: UseMutationResult<unknown, unknown, AddImpactType>;
}) {
  const [open, setOpen] = React.useState(false);

  const form = useForm<AddImpactType>({
    resolver: zodResolver(addImpactSchema),
    defaultValues: {
      impact_statement: "",
      pre_intervention_assessment: "",
      post_intervention_assessment: "",
      tools_used: "",
      grant_id: undefined,
      year: undefined,
      ...impact,
    },
  });

  const grantsQuery = getGrantsQuery(impact.organization.id.toString(), form);

  const getYearsForGrant = useMemo(() => {
    const today = new Date();

    if (!grantsQuery.data) return undefined;

    const grant = grantsQuery.data.find((e) => e.id === form.watch("grant_id"));

    if (!grant) return undefined;

    form.setValue(
      "year",
      Math.max(today.getFullYear(), grant.start_date.getFullYear()),
    );

    return {
      start_year: Math.max(today.getFullYear(), grant.start_date.getFullYear()),
      end_year: grant.end_date.getFullYear(),
    };
  }, [form.watch("grant_id")]);

  function onSubmit(values: AddImpactType) {
    editImpact
      .mutateAsync(values)
      .then(() => {
        setOpen(false);
        toast.success("Edited impact");
      })
      .catch((err) => {
        toast.error(err.message || "Failed to cretate impact");
      });
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <PencilIcon className="size-4" />
          Edit
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Impact</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-2">
            <FormField
              name="grant_id"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Grant</FormLabel>
                  {grantsQuery.isLoading && <Skeleton className="h-5 w-full" />}
                  {grantsQuery.data && (
                    <ReportGrantSelector
                      selectedGrant={field.value}
                      setSelectedGrant={field.onChange}
                      grants={grantsQuery.data}
                    />
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            {typeof form.watch("grant_id") !== "undefined" && (
              <FormField
                name="year"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Year</FormLabel>

                    <Select
                      onValueChange={field.onChange}
                      value={field.value.toString()}
                    >
                      <SelectTrigger>
                        <FormControl>
                          <SelectValue />
                        </FormControl>
                      </SelectTrigger>
                      <SelectContent>
                        {grantsQuery.isLoading && getYearsForGrant && (
                          <Skeleton className="h-5 w-full" />
                        )}
                        {grantsQuery.data &&
                          getYearsForGrant &&
                          Array.from({
                            length:
                              getYearsForGrant.end_year -
                              getYearsForGrant.start_year +
                              1,
                          }).map((_, i) => (
                            <SelectItem
                              key={getYearsForGrant.start_year + i}
                              value={`${getYearsForGrant.start_year + i}`}
                            >
                              {getYearsForGrant.start_year + i}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              name="impact_statement"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Statement</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              name="pre_intervention_assessment"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pre-intervention Assessment</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value ?? ""} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              name="post_intervention_assessment"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Post-intervention Assessment</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value ?? ""} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              name="tools_used"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tools Used</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value ?? ""} />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button className="mt-4">Submit</Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export function QuantitativeReports({ granteeId }: { granteeId: string }) {
  const addImpact = useMutation<unknown, unknown, AddImpactType>({
    mutationKey: ["impacts"],
    mutationFn: async (impact: AddImpactType) => {
      try {
        const response = await apiClient.post(
          "/api/reports/grantmaker/quantitative/impacts/",
          {
            organization_id: Number(granteeId),
            ...impact,
          },
        );

        impactQuery.refetch();

        return response;
      } catch (error: any) {
        const errorData = error.response?.data;
        if (errorData?.non_field_errors?.[0]) {
          throw new Error(errorData.non_field_errors[0]);
        }
        const firstError = errorData ? Object.values(errorData)[0] : null;
        throw new Error(
          Array.isArray(firstError) ? firstError[0] : "Failed to submit report",
        );
      }
    },
  });

  const impactQuery = useQuery<ImpactType[], Error>({
    queryKey: ["impacts"],
    queryFn: async () => {
      const impacts = await getGrantmakerImpacts(granteeId);
      return impacts;
    },
  });

  return (
    <div className="flex flex-col gap-6">
      <AddImpactDialog addImpact={addImpact} granteeId={granteeId} />

      <ImpactList
        userType="GRANT_MAKER"
        granteeId={granteeId}
        impactQuery={impactQuery}
      />
    </div>
  );
}
