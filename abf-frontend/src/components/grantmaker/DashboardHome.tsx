"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Layout from "@/components/grantmaker/Layout";
import { SummaryCards } from "@/components/grantmaker/dashboard/SummaryCards";
import { OrganizationDistributionChart } from "@/components/grantmaker/dashboard/OrganizationDistributionChart";
import { FundingChart } from "@/components/grantmaker/dashboard/FundingChart";
import { SourceBreakdownChart } from "@/components/grantmaker/dashboard/SourceBreakdownChart";
import { FundUtilizationCard } from "@/components/grantmaker/dashboard/FundUtilizationCard";

import { GeographicalSpreadMap } from "@/components/grantmaker/dashboard/GeographicalSpreadMap";
import { ImpactStoriesCarousel } from "@/components/grantmaker/dashboard/ImpactStoriesCarousel";
import { DashboardFilterSelector } from "@/components/grantmaker/dashboard/DashboardFilterSelector";
import { getDashboardSummary, DashboardSummary, getDashboardFundingEntities } from "@/services/grantmaker/grantmaker-service";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";


export default function DashboardHome() {
  const router = useRouter();
  const [data, setData] = useState<DashboardSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Filter state variables
  const [selectedQuarter, setSelectedQuarter] = useState('All');
  const [selectedYear, setSelectedYear] = useState('2025');
  const [selectedFundingEntity, setSelectedFundingEntity] = useState('all');
  const [viewMode, setViewMode] = useState<'quarterly' | 'yearly'>('quarterly');

  // Available funding entities for the selector
  const [fundingEntities, setFundingEntities] = useState<Array<{id: string, name: string}>>([
    { id: 'all', name: 'All Funding Entities' }
  ]);

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn");

    if (isLoggedIn !== "true") {
      router.push("/login");
      return;
    }

    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        const dashboardData = await getDashboardSummary(
          selectedYear,
          selectedQuarter,
          selectedFundingEntity,
          viewMode
        );
        setData(dashboardData);
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [router, selectedYear, selectedQuarter, selectedFundingEntity, viewMode]);

  // Fetch funding entities on component mount
  useEffect(() => {
    const fetchFundingEntities = async () => {
      try {
        const entities = await getDashboardFundingEntities();
        setFundingEntities(entities);
      } catch (error) {
        console.error("Error fetching funding entities:", error);
      }
    };

    fetchFundingEntities();
  }, []);



  const handleBarClick = (month: string, _amount: number) => {
    router.push(`/grantmaker/funding?month=${month}`);
  };

  // Removed handleViewOrganizationDetails as we no longer use the GranteeTable

  if (loading) {
    return (
      <Layout title="Grant Maker Dashboard">
        <div className="flex justify-center items-center h-[calc(100vh-6rem)]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      </Layout>
    );
  }

  if (error || !data) {
    return (
      <Layout title="Grant Maker Dashboard">
        <div className="w-full max-w-7xl mx-auto">
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
            <CardContent className="p-8 text-center">
              <div className="text-red-500 text-xl mb-4">{error || "Failed to load dashboard data"}</div>
              <Button onClick={() => window.location.reload()} className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400 hover:from-orange-600 hover:via-amber-600 hover:to-orange-500 text-white">
                Retry Loading
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Grant Maker Dashboard">
      <div className="w-full max-w-7xl mx-auto">
        {/* Filter Selector */}
        <div className="mb-6">
          <DashboardFilterSelector
            selectedQuarter={selectedQuarter}
            setSelectedQuarter={setSelectedQuarter}
            selectedYear={selectedYear}
            setSelectedYear={setSelectedYear}
            selectedFundingEntity={selectedFundingEntity}
            setSelectedFundingEntity={setSelectedFundingEntity}
            viewMode={viewMode}
            setViewMode={setViewMode}
            fundingEntities={fundingEntities}
          />
        </div>

        {/* Summary Cards */}
        <div className="mb-8">
          <SummaryCards
            totalAmount={data.totalAmountGiven}
            totalFunding={data.fundUtilization.totalBudget}
            numberOfGrantees={data.numberOfGrantees}
            activeGrantees={data.activeGrantees}
            pendingGrantees={data.pendingGrantees}
            completedGrantees={data.completedGrantees}
            livesImpacted={data.projectsImpact.livesImpacted}
          />
        </div>

        {/* Funding Sources and Sector Distribution Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {selectedFundingEntity === 'all' && (
            <div className="h-full">
              <SourceBreakdownChart data={data.fundingSources} />
            </div>
          )}
          <div className={`h-full ${selectedFundingEntity === 'all' ? 'lg:col-span-1' : 'lg:col-span-2'}`}>
            <OrganizationDistributionChart
              data={data.sectorDistribution}
            />
          </div>
        </div>

        {/* Fund Utilization and Quarterly Disbursements Section - COMMENTED OUT AS REQUESTED */}
        {/*
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="h-full">
            <FundUtilizationCard
              totalBudget={data.fundUtilization.totalBudget}
              totalDisbursed={data.fundUtilization.totalDisbursed}
              totalUtilized={data.fundUtilization.totalUtilized}
            />
          </div>
          <div className="h-full">
            <FundingChart
              data={data.quarterlyDisbursements}
              onBarClick={handleBarClick}
              selectedQuarter={selectedQuarter}
            />
          </div>
        </div>
        */}

        {/* Geographical Spread Map */}
        <div className="mb-8">
          <GeographicalSpreadMap data={data.geographicalSpread} />
        </div>

        {/* Impact Stories Carousel */}
        <div className="mb-8">
          <ImpactStoriesCarousel stories={data.impactStories} />
        </div>
      </div>
    </Layout>
  );
}
