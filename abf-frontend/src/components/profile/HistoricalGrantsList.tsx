

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Pencil, Trash2, Building } from "lucide-react";
import { HistoricalGrant } from "@/types/profile";
import { toast } from "sonner";
import { deleteHistoricalGrant, editHistoricalGrant, transformHistoricalGrantToOrganizationGrantHistoryAPIPayload } from "@/services/profile-service";

interface HistoricalGrantsListProps {
  historicalGrants: HistoricalGrant[] | undefined;
}

export const HistoricalGrantsList: React.FC<HistoricalGrantsListProps> = ({ historicalGrants }) => {
  const [localGrants, setLocalGrants] = useState<HistoricalGrant[]>(historicalGrants || []);
  // Inline editing state
  const [editingGrantId, setEditingGrantId] = useState<number | null>(null);
  const [editedGrant, setEditedGrant] = useState<Partial<HistoricalGrant>>({});

  useEffect(() => {
    setLocalGrants(historicalGrants || []);
  }, [historicalGrants]);

  const handleEditGrant = async (grant: HistoricalGrant) => {
    // Merge grant and editedGrant before sending API call and updating state
    const updatedGrant = { ...grant, ...editedGrant };
    const payload = transformHistoricalGrantToOrganizationGrantHistoryAPIPayload(updatedGrant);
    try {
      const response = await editHistoricalGrant(grant.id, payload);
      console.log("Response from editHistoricalGrant: ", response);
      if (response) {
        toast.success("Historical grant updated successfully");
        setLocalGrants((prev) =>
          prev.map((g) => (g.id === grant.id ? updatedGrant : g))
        );
      }
    } catch (error) {
      toast.error("Unable to edit historical grant");
    }
  };

  const handleDeleteGrant = async (id: number) => {
    try {
      const response = await deleteHistoricalGrant(id);
      if (response.status === 204) {
        setLocalGrants((prev) => prev.filter((historicalGrant) => historicalGrant.id !== id));
        toast.success("Historical Grant deleted");
      }

    }
    catch (error) {
      toast.error("Unable to delete historical grant");
    }
  };

  return (
    <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300 mb-4">
      <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
      <CardHeader className="pb-2 bg-white">
        <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
          <Building className="h-5 w-5 mr-2 text-[#00998F]" />
          Grant History
        </CardTitle>
        <CardDescription>Past grants received by the organization</CardDescription>
      </CardHeader>
      <CardContent>
        {localGrants.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {localGrants.map((grant) => {
              const isEditingGrant = editingGrantId === grant.id;
              return (
                <Card key={grant.id} className="border border-gray-200 rounded-md shadow-sm p-4 bg-white">
                  <div className="flex justify-end mb-2 gap-2">
                    {isEditingGrant ? (
                      <>
                        <Button
                          size="sm"
                          type='button'
                          onClick={async () => {
                            await handleEditGrant({ ...grant, ...editedGrant });
                            setEditingGrantId(null);
                            setEditedGrant({});
                          }}
                          className="bg-teal-500 hover:bg-teal-700"
                        >
                          Save
                        </Button>
                        <Button
                          size="sm"
                          type="button"
                          variant="ghost"
                          onClick={() => {
                            setEditingGrantId(null);
                            setEditedGrant({});
                          }}
                        >
                          Cancel
                        </Button>
                      </>
                    ) : (

                      <>
                        <Button
                          size="icon"
                          type="button"
                          variant="secondary"
                          onClick={() => {
                            setEditingGrantId(grant.id!);
                            setEditedGrant(grant);
                          }}
                          className="hover:bg-gray-200"
                        >
                          <Pencil className="w-4 h-4 text-gray-600" />
                        </Button>
                        <Button
                          size="icon"
                          type="button"
                          variant="destructive"
                          onClick={() => handleDeleteGrant(grant.id!)}
                          className="bg-gray-100 text-black hover:bg-red-200"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                    {[
                      { field: "grantName", label: "Grant Name" },
                      { field: "grantPurpose", label: "Purpose" },
                      { field: "amount", label: "Amount" },
                      { field: "statusDisplay", label: "Status" },
                      { field: "startDate", label: "Start Date" },
                      { field: "endDate", label: "End Date" },
                    ].map(({ field, label }) => (
                      <div key={field}>
                        <span className="text-sm font-medium text-gray-500">{label}</span>
                        {isEditingGrant ? (
                          <input
                            className="mt-1 border rounded px-2 py-1 w-full"
                            type={
                              field === "amount"
                                ? "number"
                                : field.toLowerCase().includes("date")
                                  ? "date"
                                  : "text"
                            }
                            value={
                              editedGrant[field as keyof HistoricalGrant] ??
                              (grant as any)[field] ??
                              ""
                            }
                            onChange={(e) => {
                              let value: any = e.target.value;
                              if (field === "amount") {
                                value = Number(value);
                              }
                              setEditedGrant((prev) => ({
                                ...prev,
                                [field]: value,
                              }));
                            }}
                          />
                        ) : (
                          <p className="text-gray-800 font-medium">
                            {field === "amount"
                              ? `₹${grant.amount}`
                              : (grant as any)[field]}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </Card>
              );
            })}
          </div>
        ) : (
          <p className="text-gray-600">No historical grants found.</p>
        )}
      </CardContent>
    </Card>
  );
};