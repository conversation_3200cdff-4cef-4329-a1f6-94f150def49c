/*
  One thing to note is documents are hardcoded.
  Also only first grant is displayed.
*/

/* eslint-disable */
"use client";
import { useEffect, useRef, useState } from "react";
import { Card, CardTitle, CardDescription, CardHeader, CardContent } from "../ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { getMyGrants } from "@/services/grantmaker/grantee-service";
import { transformGrantsAPIResponseToOrganizationGrants, getGrantAttachmentTypes, getGrantAttachments, uploadOrReplaceGrantAttachment } from "@/services/profile-service";
import { Organization, OrganizationGrant } from "@/types/profile";
import { getPresignedUrl, uploadFileToS3 } from "@/lib/s3Uploader";
import { toast } from "sonner";

interface GrantsListProps {
  organization: Organization | null;
}

export function GrantsList({organization}: GrantsListProps) {
  const [grants, setGrants] = useState<OrganizationGrant[]>(organization?.grants || [])
  const [attachmentTypes, setAttachmentTypes] = useState([]);
  const [attachments, setAttachments] = useState(organization?.grants?.[0]?.attachments || []);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [selectedTypeId, setSelectedTypeId] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const grant = grants?.[0];

  useEffect(() => {
    console.log("Grant = ", organization?.grants[0]);
    async function fetchData() {
      if (!grant) return;

      const [typesRes, attachmentsRes] = await Promise.all([
        getGrantAttachmentTypes(),
        getGrantAttachments(grant.id)
      ]);

      setAttachmentTypes(typesRes || []);
      setAttachments(attachmentsRes || []);
    }

    fetchData();
  }, [grant]);

  const handleUploadClick = (typeId: string) => {
    setSelectedTypeId(typeId);
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !selectedTypeId || !grant) return;

    // 1. Validate file
    if (file.type !== "application/pdf") {
      toast.error("Please upload only PDF files");
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size should not exceed 5MB");
      return;
    }

    setIsUploading(true);
    try {
      // 2. Get presigned URL
      const presignedUrlData = await getPresignedUrl(file);

      // 3. Upload file to S3
      await uploadFileToS3(file, presignedUrlData.upload_url);

      // 4. Prepare JSON payload
      const attachmentData = {
        grant: grant.id.toString(),
        attachment_type: selectedTypeId,
        filename: file.name,
        s3_key: presignedUrlData.object_key,
      };

      // 5. Update backend
      await uploadOrReplaceGrantAttachment(attachmentData);

      // 6. Refresh attachments and notify user
      const updated = await getGrantAttachments(grant.id);
      setAttachments(updated || []);
      toast.success("Document uploaded successfully");
    } catch (error) {
      console.error("Error uploading file:", error);
      toast.error("An error occurred while uploading the document");
    } finally {
      setIsUploading(false);
    }
  };

  // Refined UI layout with Card and gradient border, styled tiles, and documents inside the Card
  return (
    <>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
      />
      <div className="space-y-6">
      <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden">
        <div className="h-1.5 bg-gradient-to-r from-teal-300 via-teal-500 to-teal-700"></div>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-semibold text-gray-800">Grant Overview</CardTitle>
              <CardDescription>Core information about this grant</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <h3 className="text-sm font-medium text-gray-500">Grant Name</h3>
              <p className="mt-1 text-gray-900 font-medium">{grant?.name}</p>
            </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500">Grant Annual Budget</h3>
              <p className="mt-1 text-gray-900 font-medium">₹{grant?.amount}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500">Program Start Date</h3>
              <p className="mt-1 text-gray-900 font-medium">{grant?.startDate}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500">Program End Date</h3>
              <p className="mt-1 text-gray-900 font-medium">{grant?.endDate}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg col-span-3">
              <h3 className="text-sm font-medium text-gray-500">Funding Entity</h3>
              <p className="mt-1 text-gray-900">{grant?.fundingSources || "NIL"}</p>
            </div>
          </div>

          <div className="mt-10">
            <h2 className="text-lg font-medium text-gray-700 mb-4">Grant Specific Documents</h2>
            <div className="space-y-4">
              {attachmentTypes.map((type: any) => {
                const matchedDoc = attachments.find((a: any) => a.attachment_type === type.id);
                return (
                  <div key={type.id} className="flex items-center justify-between p-3 border rounded-md bg-white shadow-sm hover:shadow transition">
                    <div className="flex items-center space-x-3">
                      <div className="text-red-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                          <polyline points="14 2 14 8 20 8"></polyline>
                        </svg>
                      </div>
                      <div>
                        <p className="font-medium">{type.name}</p>
                        {matchedDoc ? (
                          <p className="text-sm text-gray-500">{matchedDoc.filename}</p>
                        ) : (
                          <p className="text-sm text-gray-400 italic">No document uploaded</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {matchedDoc && (
                        <Button variant="outline" size="sm" onClick={() => window.open(matchedDoc.download_url, "_blank")}>
                          Download
                        </Button>
                      )}
                      <Button variant="secondary" size="sm" onClick={() => handleUploadClick(type.id)} disabled={isUploading}>
                        {matchedDoc ? "Re-upload" : "Upload"}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
    </>
  );
}