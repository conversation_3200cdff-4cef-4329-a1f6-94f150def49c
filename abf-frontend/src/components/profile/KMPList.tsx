import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardT<PERSON>le, CardDescription } from "@/components/ui/card";
import { Pencil, Trash2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { KMP } from "@/types/profile";
import { deleteKMP, editKMP, transformKMPToKMPUpdateRequest } from "@/services/profile-service";
import { toast } from "sonner";
import { extractErrors } from "@/lib/axios-error-utils";
import { motion } from "framer-motion";
import EditableTextField from "../common/EditableTextField";

interface KMPListProps {
	kmps: KMP[] | undefined;
}

export const KMPList: React.FC<KMPListProps> = ({ kmps }) => {
	const [localKMPs, setLocalKMPs] = useState<KMP[]>(kmps || []);
	const [editingIndex, setEditingIndex] = useState<number | null>(null);

	useEffect(() => {
		setLocalKMPs(kmps || []);
	}, [kmps]);

	const handleDeleteKMP = async (id: number) => {
		try {
			const response = await deleteKMP(id);
			console.log("Response ", JSON.stringify(response));
			if (response.status === 204) {
				toast.success("KMP deleted successfully");
				setLocalKMPs((prev) => prev.filter((kmp) => kmp.id !== id));
				// If the currently edited KMP is deleted, reset editingIndex
				setEditingIndex((prevIndex) => {
					const idx = localKMPs.findIndex((kmp) => kmp.id === id);
					if (prevIndex === idx) {
						return null;
					}
					return prevIndex;
				});
			}

		} catch (error: unknown) {
			toast.error("Unable to delete KMP!")

		}

	};

	// Centralized input change handler
	const handleInputChange = (index: number, field: keyof KMP, value: string) => {
		setLocalKMPs((prev) => {
			const newKMPs = [...prev];
			newKMPs[index] = { ...newKMPs[index], [field]: value };
			return newKMPs;
		});
	};

	const handleEdit = async (kmpData: Partial<KMP>) => {
		const { id: id, ...kmpPayload } = kmpData;
		setEditingIndex(null);
		try {
			const payload = transformKMPToKMPUpdateRequest(kmpPayload)
			const response = await editKMP(id!, payload);
			if (response.status === "SUCCESS") {
				toast.success("KMP updated successfully")
			}

		} catch (error) {
			const errorMessages = extractErrors(error);
			errorMessages.forEach((msg) => toast.error(msg));
		}

	}

	return (
		<Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300 mb-4">
			<div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400" />
			<CardHeader className="pb-2 bg-white">
				<CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
					<Pencil className="h-5 w-5 mr-2 text-[#00998F]" />
					Core Leadership Team
				</CardTitle>
				<CardDescription>Manage the key people in your organization</CardDescription>
			</CardHeader>
			<CardContent>
				{localKMPs.length > 0 ? (
					<div className="grid grid-cols-1 gap-4">
						{localKMPs.map((kmp, index) => (
							<Card key={kmp.id} className="relative shadow-sm border border-gray-100 rounded-xl bg-white hover:shadow-md transition-shadow duration-300">
								<CardContent className="p-4">
									<div className="absolute right-4 top-4 flex space-x-2">
										{editingIndex === index ? (
											<>
												<Button
													size="sm"
													type="button"
													variant="default"
													onClick={() => handleEdit(kmp)}
													className="bg-teal-500 hover:bg-teal-700"
												>
													Save
												</Button>
												<Button
													type="button"
													size="sm"
													variant="outline"
													onClick={() => setEditingIndex(null)}
												>
													Cancel
												</Button>
											</>
										) : (
											<>
												<Button
													size="icon"
													type="button"
													variant="secondary"
													onClick={() => setEditingIndex(index)}
													className="hover:bg-gray-200"
												>
													<Pencil className="w-4 h-4" />
												</Button>
												<Button
													type="button"
													size="icon"
													variant="destructive"
													onClick={() => handleDeleteKMP(kmp.id)}
													className="bg-gray-100 text-black hover:bg-red-200"
												>
													<Trash2 className="w-4 h-4" />
												</Button>
											</>
										)}
									</div>
									<div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 pt-6">
										<EditableTextField>
											<span className="text-sm font-medium text-gray-500">Name</span>
											{editingIndex === index ? (
												<Input
													value={kmp.name}
													onChange={(e) => handleInputChange(index, "name", e.target.value)}
													className="border-none"
												/>
											) : (
												<p className="text-gray-900 font-medium">{kmp.name}</p>
											)}
										</EditableTextField>
										<EditableTextField>
											<span className="text-sm font-medium text-gray-500">Designation</span>
											{editingIndex === index ? (
												<Input
													value={kmp.designation}
													onChange={(e) => handleInputChange(index, "designation", e.target.value)}
													className="border-none"

												/>
											) : (
												<p className="text-gray-900 font-medium">{kmp.designation}</p>
											)}
										</EditableTextField>
										<EditableTextField>
											<span className="text-sm font-medium text-gray-500">DIN</span>
											{editingIndex === index ? (
												<Input
													value={kmp.din}
													onChange={(e) => handleInputChange(index, "din", e.target.value)}
													className="border-none"
												/>
											) : (
												<p className="text-gray-900 font-medium">{kmp.din}</p>
											)}
										</EditableTextField>
										<EditableTextField>
											<span className="text-sm font-medium text-gray-500">Phone Number</span>
											{editingIndex === index ? (
												<Input
													value={kmp.phoneNumber}
													onChange={(e) => handleInputChange(index, "phoneNumber", e.target.value)}
													className="border-none"
												/>
											) : (
												<p className="text-gray-900 font-medium">{kmp.phoneNumber}</p>
											)}
										</EditableTextField>
										<EditableTextField>
											<span className="text-sm font-medium text-gray-500">Email</span>
											{editingIndex === index ? (
												<Input
													value={kmp.email}
													onChange={(e) => handleInputChange(index, "email", e.target.value)}
													className="border-none"
												/>
											) : (
												<p className="text-gray-900 font-medium">{kmp.email}</p>
											)}
										</EditableTextField>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				) : (
					<p className="text-gray-600">No core leadership team added yet.</p>
				)}
			</CardContent>
		</Card>
	);
};