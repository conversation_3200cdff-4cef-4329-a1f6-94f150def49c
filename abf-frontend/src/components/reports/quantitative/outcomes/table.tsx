"use client";

import { ActivityType, OutcomeType, OutputType } from "@/types/quantitative";
import { Fragment } from "react";

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  actualKeys,
  calculateOutputProgress,
  getQuarterTotal,
  plannedKeys,
  quarterKeyPairs,
} from "@/app/grantmaker/reports/_lib/quantitative";
import { Gauge } from "@/components/ui/gauge";
import { QuarterCell } from "./columns";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

function ActivityRow({
  activity,
  index,
  colCount,
}: {
  activity: ActivityType;
  index: number;
  colCount: number;
}) {
  const plannedTotal = getQuarterTotal(activity, plannedKeys);
  const actualTotal = getQuarterTotal(activity, actualKeys);

  return (
    <TableRow className="bg-gray-50 border-b border-gray-200">
      <TableCell className="px-4 py-2">
        <div className="ml-10 flex items-center gap-2">
          <Gauge value={calculateOutputProgress(activity)} size={"xs"} />
          <Badge variant="secondary" className="text-xs">
            Activity {index + 1}
          </Badge>
          <span className="text-sm">{activity.description}</span>
        </div>
      </TableCell>
      {quarterKeyPairs.map((e, i) => (
        <Fragment key={i}>
          <TableCell className="px-4 py-2 text-center text-sm">
            {activity[e.plan] ? `${activity[e.plan]} ${activity.unit}` : "-"}
          </TableCell>
          <TableCell className="px-4 py-2 text-center text-sm">
            <QuarterCell
              value={activity[e.actual]}
              unit={activity.unit}
              remark={activity[e.remark]}
            />
          </TableCell>
        </Fragment>
      ))}
      <TableCell className="px-4 py-2 text-center text-sm">
        {plannedTotal ? `${plannedTotal} ${activity.unit}` : "-"}
      </TableCell>
      <TableCell className="px-4 py-2 text-center text-sm">
        {actualTotal ? `${actualTotal} ${activity.unit}` : "-"}
      </TableCell>
    </TableRow>
  );
}

export function OutputTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    initialState: {
      expanded: true,
    },
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getRowCanExpand: (row) => {
      const output = row.original as OutputType;
      return !!(output.activities && output.activities.length > 0);
    },
  });

  return (
    <div className="w-full overflow-x-auto">
      <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
        <Table className="w-full border-collapse">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                key={headerGroup.id}
                className="border-b border-gray-200 bg-gray-50"
              >
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    colSpan={header.colSpan}
                    className="px-4 py-3 text-left text-sm font-medium text-gray-900"
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => {
                const output = row.original as OutputType;
                return (
                  <Fragment key={row.id}>
                    <TableRow className="border-b border-gray-200 hover:bg-gray-50 transition-colors">
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className="px-4 py-3 text-sm text-gray-900"
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                    {row.getIsExpanded() &&
                      output.activities?.map((activity, index) => (
                        <ActivityRow
                          key={activity.id}
                          activity={activity}
                          index={index}
                          colCount={columns.length}
                        />
                      ))}
                  </Fragment>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="px-4 py-12 text-center text-sm text-gray-500"
                >
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
