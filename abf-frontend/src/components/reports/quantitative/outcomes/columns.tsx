"use client";

import {
  actualKeys,
  calculateOutputProgress,
  getQuarterTotal,
  plannedKeys,
} from "@/app/grantmaker/reports/_lib/quantitative";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Gauge } from "@/components/ui/gauge";
import { Tooltip, TooltipContent } from "@/components/ui/tooltip";
import { OutputType } from "@/types/quantitative";
import { TooltipTrigger } from "@radix-ui/react-tooltip";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { ChevronDownIcon, ChevronRightIcon, InfoIcon } from "lucide-react";

const columnGroups = createColumnHelper<OutputType>();

export function QuarterCell({
  value,
  unit,
  remark,
}: {
  value?: string | number | null;
  unit?: string | null;
  remark?: string | null;
}) {
  const display = value ? `${value} ${unit}` : "-";

  if (remark) {
    return (
      <div className="text-center">
        <Tooltip>
          <TooltipTrigger className="inline-flex items-center gap-1">
            <InfoIcon className="size-4" />
            {display}
          </TooltipTrigger>
          <TooltipContent>{remark}</TooltipContent>
        </Tooltip>
      </div>
    );
  }

  return (
    <div className="text-center inline-flex items-center gap-1">{display}</div>
  );
}

export const output_columns: ColumnDef<OutputType>[] = [
  columnGroups.group({
    id: "description",
    header: "Description",
    columns: [
      {
        accessorKey: "description",
        header: "",
        cell: (info) => (
          <div className="flex items-center gap-2">
            {info.row.getCanExpand() ? (
              <Button
                size="icon"
                variant="ghost"
                className="cursor-pointer h-7 w-8"
                {...{
                  onClick: info.row.getToggleExpandedHandler(),
                }}
              >
                {info.row.getIsExpanded() ? (
                  <ChevronDownIcon className="size-4" />
                ) : (
                  <ChevronRightIcon className="size-4" />
                )}
              </Button>
            ) : null}
            <Gauge
              value={calculateOutputProgress(info.row.original)}
              size={"xs"}
              showValue={false}
            />
            <Badge>Output {info.row.index + 1}</Badge>{" "}
            <span className="max-w-[200px] truncate">{info.renderValue()}</span>
          </div>
        ),
      },
    ],
  }),
  columnGroups.group({
    id: "Q1",
    header: () => <div className="text-center">Q1</div>,
    columns: [
      {
        accessorKey: "q1_plan",
        header: () => <div className="text-center">Planned</div>,
        cell: (info) => (
          <div className="text-center">
            {info.renderValue()
              ? `${info.renderValue()} ${info.row.original.unit}`
              : "-"}
          </div>
        ),
      },
      {
        accessorKey: "q1_actual",
        header: () => <div className="text-center">Actual</div>,
        cell: (info) => (
          <QuarterCell
            value={info.renderValue()}
            unit={info.row.original.unit}
            remark={info.row.original.q1_remark}
          />
        ),
      },
    ],
  }),
  columnGroups.group({
    id: "Q2",
    header: () => <div className="text-center">Q2</div>,
    columns: [
      {
        accessorKey: "q2_plan",
        header: () => <div className="text-center">Planned</div>,
        cell: (info) => (
          <div className="text-center">
            {info.renderValue()
              ? `${info.renderValue()} ${info.row.original.unit}`
              : "-"}
          </div>
        ),
      },
      {
        accessorKey: "q2_actual",
        header: () => <div className="text-center">Actual</div>,
        cell: (info) => (
          <QuarterCell
            value={info.renderValue()}
            unit={info.row.original.unit}
            remark={info.row.original.q2_remark}
          />
        ),
      },
    ],
  }),
  columnGroups.group({
    id: "Q3",
    header: () => <div className="text-center">Q3</div>,
    columns: [
      {
        accessorKey: "q3_plan",
        header: () => <div className="text-center">Planned</div>,
        cell: (info) => (
          <div className="text-center">
            {info.renderValue()
              ? `${info.renderValue()} ${info.row.original.unit}`
              : "-"}
          </div>
        ),
      },
      {
        accessorKey: "q3_actual",
        header: () => <div className="text-center">Actual</div>,
        cell: (info) => (
          <QuarterCell
            value={info.renderValue()}
            unit={info.row.original.unit}
            remark={info.row.original.q3_remark}
          />
        ),
      },
    ],
  }),
  columnGroups.group({
    id: "Q4",
    header: () => <div className="text-center">Q4</div>,
    columns: [
      {
        accessorKey: "q4_plan",
        header: () => <div className="text-center">Planned</div>,
        cell: (info) => (
          <div className="text-center">
            {info.renderValue()
              ? `${info.renderValue()} ${info.row.original.unit}`
              : "-"}
          </div>
        ),
      },
      {
        accessorKey: "q4_actual",
        header: () => <div className="text-center">Actual</div>,
        cell: (info) => (
          <QuarterCell
            value={info.renderValue()}
            unit={info.row.original.unit}
            remark={info.row.original.q4_remark}
          />
        ),
      },
    ],
  }),
  columnGroups.group({
    id: "Total",
    header: () => <div className="text-center">Total</div>,
    columns: [
      {
        accessorKey: "planned",
        header: () => <div className="text-center">Planned</div>,
        cell: (info) => {
          const totalPlanned = getQuarterTotal(info.row.original, plannedKeys);

          return (
            <div className="text-center">
              {totalPlanned ? `${totalPlanned} ${info.row.original.unit}` : "-"}
            </div>
          );
        },
      },
      {
        accessorKey: "actual",
        header: () => <div className="text-center">Actual</div>,
        cell: (info) => {
          const totalActual = getQuarterTotal(info.row.original, actualKeys);

          return (
            <div className="text-center">
              {totalActual ? `${totalActual} ${info.row.original.unit}` : "-"}
            </div>
          );
        },
      },
    ],
  }),
];
