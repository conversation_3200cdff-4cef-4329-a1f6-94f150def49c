import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../ui/card";
import {
  BarChartIcon,
  ChevronsDownIcon,
  ExternalLinkIcon,
  LineChartIcon,
  WrenchIcon,
} from "lucide-react";
import Link from "next/link";
import {
  ImpactType,
  OutcomeType,
  OutcomeTypeWithImpact,
} from "@/types/quantitative";
import {
  actualKeys,
  calculateImpactProgress,
  calculateOutcomeProgress,
  calculateOutputProgress,
  getQuarterTotal,
  plannedKeys,
  quarterKeyPairs,
} from "@/app/grantmaker/reports/_lib/quantitative";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  TableBody,
  Table,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import { Fragment } from "react";
import { Gauge } from "../../ui/gauge";
import { Tooltip, TooltipContent, TooltipTrigger } from "../../ui/tooltip";
import { Button } from "../../ui/button";
import { Progress } from "@/components/ui/progress";
import { UserType } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { useProgress } from "@/hooks/useProgress";

export function OutcomesList({
  outcomes,
  userType,
}: {
  outcomes: OutcomeTypeWithImpact[];
  userType: UserType;
}) {
  const outcomeUrl = (outcome: OutcomeTypeWithImpact) => {
    switch (userType) {
      case "GRANTEE":
        return `/milestones-reports/quantitative/${outcome.impact.id}/outcome/${outcome.id}`;
      case "GRANT_MAKER":
        return `/grantmaker/reports/quantitative/${outcome.impact.id}/outcome/${outcome.id}`;
      default:
        return "";
    }
  };

  return (
    <>
      {Array.isArray(outcomes) &&
        outcomes.map((outcome, i) => {
          const progress = useProgress(() => {
            return calculateOutcomeProgress(outcome);
          });

          return (
            <motion.div
              key={i}
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -10, opacity: 0 }}
              transition={{ delay: (i + 1) * 0.2, duration: 0.2 }}
            >
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <Link href={outcomeUrl(outcome)}>
                      <CardTitle className="text-xl leading-tight hover:underline items-center inline-block">
                        {outcome.outcome_statement}
                        <ExternalLinkIcon className="size-4 inline-block ml-2" />
                      </CardTitle>
                    </Link>
                    <CardDescription className="flex flex-wrap justify-end items-center gap-2">
                      <span>{outcome.outputs.length} outputs</span>
                      <span>•</span>
                      <span>
                        {
                          outcome.outputs.filter((output) => {
                            const actual = getQuarterTotal(output, actualKeys);
                            const planned = getQuarterTotal(
                              output,
                              plannedKeys,
                            );
                            return actual && planned && planned <= actual;
                          }).length
                        }{" "}
                        completed
                      </span>
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <BarChartIcon className="h-4 w-4" />
                      <h3 className="font-medium">
                        Pre-intervention assessment (baseline):
                      </h3>
                    </div>
                    <p className="text-sm text-muted-foreground ml-6">
                      {outcome.pre_intervention_assessment}
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <LineChartIcon className="h-4 w-4 text-slate-500" />
                      <h3 className="font-medium">
                        Post-intervention assessment (endline):
                      </h3>
                    </div>
                    <p className="text-sm text-slate-600 ml-6">
                      {outcome.post_intervention_assessment}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span className="font-medium">
                        {progress.toFixed(1)}%
                      </span>
                    </div>
                    <Progress
                      value={progress}
                      indicatorClassName="duration-600"
                    />
                  </div>

                  <div className="space-y-2">
                    <Collapsible>
                      <CollapsibleContent className="grid gap-2 pb-2">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="text-center" rowSpan={2}>
                                Output
                              </TableHead>
                              {Array.from({ length: 4 }).map((_, index) => (
                                <TableHead
                                  className="text-center"
                                  colSpan={2}
                                  key={index}
                                >
                                  Q{index + 1}
                                </TableHead>
                              ))}
                              <TableHead
                                className="text-center bg-muted"
                                colSpan={2}
                              >
                                Total
                              </TableHead>
                            </TableRow>
                            <TableRow>
                              {Array.from({ length: 4 }).map((_, index) => (
                                <Fragment key={index}>
                                  <TableHead className="text-center">
                                    Planned
                                  </TableHead>
                                  <TableHead className="text-center">
                                    Actual
                                  </TableHead>
                                </Fragment>
                              ))}
                              <TableHead className="text-center bg-muted">
                                Planned
                              </TableHead>
                              <TableHead className="text-center bg-muted">
                                Actual
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {outcome.outputs.map((output) => {
                              const totalPlanned = getQuarterTotal(
                                output,
                                plannedKeys,
                              );
                              const totalActual = getQuarterTotal(
                                output,
                                actualKeys,
                              );

                              return (
                                <TableRow key={output.id}>
                                  <TableCell rowSpan={2} className="flex gap-2">
                                    <Gauge
                                      value={Math.round(
                                        calculateOutputProgress(output),
                                      )}
                                      size={"xs"}
                                    />
                                    <Tooltip>
                                      <TooltipTrigger className="truncate max-w-[200px]">
                                        {output.description}
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        {output.description}
                                      </TooltipContent>
                                    </Tooltip>
                                  </TableCell>
                                  {quarterKeyPairs.map((quarter, index) => (
                                    <Fragment key={quarter.quarter}>
                                      <TableCell className={"text-center"}>
                                        <Tooltip>
                                          <TooltipTrigger className="truncate max-w-[75px]">
                                            {output[quarter.plan]
                                              ? `${output[quarter.plan]} ${output.unit}`
                                              : "-"}
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            {output[quarter.plan]
                                              ? `${output[quarter.plan]} ${output.unit}`
                                              : "-"}
                                          </TooltipContent>
                                        </Tooltip>
                                      </TableCell>
                                      <TableCell
                                        className={"text-center font-semibold"}
                                      >
                                        <Tooltip>
                                          <TooltipTrigger className="truncate w-full max-w-[75px]">
                                            {output[quarter.actual]
                                              ? `${output[quarter.actual]} ${output.unit}`
                                              : "-"}
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            {output[quarter.actual]
                                              ? `${output[quarter.actual]} ${output.unit}`
                                              : "-"}
                                          </TooltipContent>
                                        </Tooltip>
                                      </TableCell>
                                    </Fragment>
                                  ))}
                                  <TableCell className={"text-center"}>
                                    <Tooltip>
                                      <TooltipTrigger className="truncate w-full max-w-[75px]">
                                        {totalPlanned ?? "-"}{" "}
                                        {totalPlanned && output.unit}
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        {totalPlanned ?? "-"}{" "}
                                        {totalPlanned && output.unit}
                                      </TooltipContent>
                                    </Tooltip>
                                  </TableCell>
                                  <TableCell
                                    className={"text-center font-semibold"}
                                  >
                                    <Tooltip>
                                      <TooltipTrigger className="truncate w-full max-w-[75px]">
                                        {totalActual ?? "-"}{" "}
                                        {totalActual && output.unit}
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        {totalActual ?? "-"}{" "}
                                        {totalActual && output.unit}
                                      </TooltipContent>
                                    </Tooltip>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </CollapsibleContent>
                      <CollapsibleTrigger asChild>
                        <Button
                          className="w-full group text-muted-foreground text-xs"
                          variant="ghost"
                        >
                          <div>
                            View{" "}
                            <span className="group-data-[state=closed]:hidden">
                              Less
                            </span>
                            <span className="group-data-[state=open]:hidden">
                              More
                            </span>
                          </div>
                          <ChevronsDownIcon className="transition-transform duration-300 group-data-[state=open]:rotate-180" />
                        </Button>
                      </CollapsibleTrigger>
                    </Collapsible>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
    </>
  );
}

export function OutcomeHeader({ outcome }: { outcome: OutcomeType }) {
  const outcomeProgress = useProgress(() => {
    return calculateOutcomeProgress(outcome);
  });

  return (
    <>
      <h4 className="text-sm px-6 text-muted-foreground mb-3">Outcome</h4>
      <h1 className="text-3xl tracking-tight font-semibold mb-6 px-6">
        {outcome.outcome_statement}
      </h1>

      <div className="space-y-6 p-0">
        <Accordion type="multiple" className="w-full border-y">
          <motion.div
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -10, opacity: 0 }}
            transition={{ delay: 0.2, duration: 0.2 }}
          >
            <AccordionItem value="assessment">
              {/* Needs Assessment Section */}
              <AccordionTrigger className="flex items-center px-6">
                <div className="flex items-center gap-2">
                  <BarChartIcon className="h-5 w-5 text-blue-500" />
                  <h2>Assessment</h2>
                </div>
              </AccordionTrigger>
              <AccordionContent className="flex flex-col gap-2 px-8 pb-6 text-balance">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <BarChartIcon className="h-4 w-4 text-slate-500" />
                    <h3 className="font-medium">
                      Pre-intervention assessment (baseline):
                    </h3>
                  </div>
                  <p className="text-sm text-slate-600 ml-6">
                    {outcome.pre_intervention_assessment}
                  </p>
                </div>

                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <LineChartIcon className="h-4 w-4 text-slate-500" />
                    <h3 className="font-medium">
                      Post-intervention assessment (endline):
                    </h3>
                  </div>
                  <p className="text-sm text-slate-600 ml-6">
                    {outcome.post_intervention_assessment}
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>
          </motion.div>
          {(outcome.tools_used !== "" || !!outcome.tools_used) && (
            <AccordionItem value={"tools"}>
              <AccordionTrigger className="flex items-center px-6">
                <div className="flex items-center gap-2">
                  <WrenchIcon className="h-5 w-5 text-yellow-400" />
                  <h2>Tools Used</h2>
                </div>
              </AccordionTrigger>

              <AccordionContent className="flex flex-col gap-2 px-8 pb-6 text-balance">
                {outcome.tools_used}
              </AccordionContent>
            </AccordionItem>
          )}
        </Accordion>

        <div className="space-y-4 px-6 pb-6">
          <div className="flex justify-between text-sm">
            <span>Overall Impact Progress</span>
            <span className="font-medium">{outcomeProgress.toFixed(1)}%</span>
          </div>

          <Progress
            value={outcomeProgress}
            className="h-3"
            indicatorClassName="bg-teal-500 duration-600"
          />
        </div>
      </div>
    </>
  );
}
