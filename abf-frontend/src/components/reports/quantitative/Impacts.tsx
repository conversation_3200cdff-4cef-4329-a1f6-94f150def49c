import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Progress } from "@/components/ui/progress";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../ui/card";
import {
  BarChartIcon,
  InboxIcon,
  LineChartIcon,
  WrenchIcon,
} from "lucide-react";
import { Separator } from "../../ui/separator";
import Link from "next/link";
import { UserType } from "@/contexts/AuthContext";
import { ImpactType, OutcomeType } from "@/types/quantitative";
import { calculateImpactProgress } from "@/app/grantmaker/reports/_lib/quantitative";
import { motion } from "framer-motion";
import React from "react";
import { useProgress } from "@/hooks/useProgress";
import { UseQueryResult } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";

export function ImpactList({
  granteeId,
  userType,
  impactQuery,
}: {
  granteeId: string;
  userType: UserType;
  impactQuery: UseQueryResult<ImpactType[], Error>;
}) {
  const impactUrl = (impact: { id: number }) => {
    switch (userType) {
      case "GRANTEE":
        return `/milestones-reports/quantitative/${impact.id}/`;
      case "GRANT_MAKER":
        return `/grantmaker/reports/quantitative/${impact.id}/`;
      default:
        return "";
    }
  };

  return (
    <div className="grid gap-6">
      {impactQuery.isLoading &&
        Array.from({ length: 4 }).map((e, i) => (
          <Skeleton key={i} className="h-[200px]" />
        ))}
      {impactQuery.data && impactQuery.data.length < 1 && (
        <div className="flex flex-col items-center justify-center py-6 text-center col-span-full">
          <div className="rounded-full bg-gray-100 p-6 mb-4">
            <InboxIcon className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No impacts found
          </h3>
          <p className="text-gray-500 max-w-sm">
            Impact goals will appear here once added.
          </p>
        </div>
      )}
      {impactQuery.data &&
        impactQuery.data.map((impact, index) => (
          <motion.div
            key={index}
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -10, opacity: 0 }}
            transition={{ delay: (index + 1) * 0.2, duration: 0.2 }}
          >
            <Link href={impactUrl(impact)}>
              <Card className="shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                <CardHeader className="">
                  <CardDescription>Statement</CardDescription>
                  <CardTitle className="text-lg">
                    {impact.impact_statement}
                  </CardTitle>
                </CardHeader>

                <CardContent className="space-y-6 border-t p-0 pt-6">
                  {/* Needs Assessment Section */}
                  <div className="px-6">
                    <div className="flex items-center gap-2 mb-4">
                      <BarChartIcon className="h-5 w-5 text-blue-500" />
                      <h2 className="text-lg font-semibold">Assessment</h2>
                    </div>

                    <div className="space-y-4 ml-4">
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <BarChartIcon className="h-4 w-4 text-slate-500" />
                          <h3 className="font-medium">
                            Pre-intervention assessment:
                          </h3>
                        </div>
                        <p className="text-sm text-slate-600 ml-6">
                          {impact.pre_intervention_assessment}
                        </p>
                      </div>

                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <LineChartIcon className="h-4 w-4 text-slate-500" />
                          <h3 className="font-medium">
                            Post-intervention assessment:
                          </h3>
                        </div>
                        <p className="text-sm text-slate-600 ml-6">
                          {impact.post_intervention_assessment}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Tools Used Section */}
                  {(impact.tools_used !== "" || !!impact.tools_used) && (
                    <>
                      <Separator />

                      <div className="px-6">
                        <div className="flex items-center gap-2 mb-3">
                          <WrenchIcon className="h-5 w-5 text-yellow-400" />
                          <h2 className="text-lg font-semibold">Tools Used</h2>
                        </div>
                        <div className="prose">{impact.tools_used}</div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </Link>
          </motion.div>
        ))}
    </div>
  );
}

export function ImpactHeader({
  impact,
  outcomes,
}: {
  impact: ImpactType;
  outcomes: OutcomeType[];
}) {
  const impactProgress = useProgress(() => {
    return calculateImpactProgress(outcomes);
  });

  return (
    <div className="pb-0 pt-6">
      <h4 className="text-sm px-6 text-muted-foreground mb-3">Impact</h4>
      <motion.h1
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: -10, opacity: 0 }}
        transition={{ delay: 0.1, duration: 0.2 }}
        className="text-3xl tracking-tight font-semibold mb-6 px-6"
      >
        {impact.impact_statement}
      </motion.h1>

      <div className="space-y-6 p-0">
        <Accordion type="multiple" className="w-full border-y">
          <AccordionItem value="assessment">
            <motion.div
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -10, opacity: 0 }}
              transition={{ delay: 0.3, duration: 0.2 }}
            >
              {/* Needs Assessment Section */}
              <AccordionTrigger className="flex items-center px-6">
                <div className="flex items-center gap-2">
                  <BarChartIcon className="h-5 w-5 text-blue-500" />
                  <h2>Assessment</h2>
                </div>
              </AccordionTrigger>
              <AccordionContent className="flex flex-col gap-2 px-8 pb-6 text-balance">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <BarChartIcon className="h-4 w-4 text-slate-500" />
                    <h3 className="font-medium">
                      Pre-intervention assessment (baseline):
                    </h3>
                  </div>
                  <p className="text-sm text-slate-600 ml-6">
                    {impact.pre_intervention_assessment}
                  </p>
                </div>

                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <LineChartIcon className="h-4 w-4 text-slate-500" />
                    <h3 className="font-medium">
                      Post-intervention assessment (endline):
                    </h3>
                  </div>
                  <p className="text-sm text-slate-600 ml-6">
                    {impact.post_intervention_assessment}
                  </p>
                </div>
              </AccordionContent>
            </motion.div>
          </AccordionItem>

          {(impact.tools_used !== "" || !!impact.tools_used) && (
            <motion.div
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -10, opacity: 0 }}
              transition={{ delay: 0.4, duration: 0.2 }}
            >
              <AccordionItem value={"tools"}>
                <AccordionTrigger className="flex items-center px-6">
                  <div className="flex items-center gap-2">
                    <WrenchIcon className="h-5 w-5 text-yellow-400" />
                    <h2>Tools Used</h2>
                  </div>
                </AccordionTrigger>

                <AccordionContent className="flex flex-col gap-2 px-8 pb-6 text-balance">
                  {impact.tools_used}
                </AccordionContent>
              </AccordionItem>
            </motion.div>
          )}
        </Accordion>

        <div className="space-y-4 px-6 pb-6">
          <div className="flex justify-between text-sm">
            <span>Overall Impact Progress</span>
            <span className="font-medium">{impactProgress.toFixed(1)}%</span>
          </div>

          <Progress
            value={impactProgress}
            className="h-3"
            indicatorClassName="bg-teal-500 duration-600"
          />
        </div>
      </div>
    </div>
  );
}
