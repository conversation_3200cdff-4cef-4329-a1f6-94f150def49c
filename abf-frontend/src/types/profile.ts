
export interface ProfileData {
  organization: {
    organizationName: string;
    organizationLegalType: string;
    organizationFunctionType: string;
    taxRegistrationNumber: string;
    panNumber: string;
  };
  contact: {
    fullName: string;
    phone: string;
    email: string;
    address: string;
  };
  details: {
    teamMembers: string;
    website: string;
    mission: string;
    history: string;
  };
  identificationDetails: {
    csrRegistrationNumber: string;
    taxRegistrationNumber: string;
    taxRegistrationNumberUnder12A: string;
    trustRegistrationNumber: string;
    darpanId: string;
    fcraRegistrationNumber: string;
  };
  previousGrants: HistoricalGrant[]; 
  keyPersonnel: KMP[]; // same here, can be `KeyPersonnel[]`
}

export interface GrantAPIResponse {
  id: number; // assuming you'll include the ID in API responses
  organization: number | string; // ID or slug if you're referencing it that way
  grant_maker_organization: number | string;
  grant_name: string;
  grant_duration?: string;
  grant_purpose?: string;
  annual_budget?: string; // or number if you're converting decimal properly
  funding_sources?: string;
  created_at: string; // ISO format string (from DRF)
  updated_at: string;
}

export interface ApplicantDetailsProps {
  organization: Organization | null;
}

export interface HistoricalGrantAPIPayload {
  grant_name: string;
  budget: number;
  start_date: string;
  end_date: string;
  status: 'PENDING' | 'ACTIVE' | 'COMPLETED';
  grant_purpose: string;
}

export interface HistoricalGrantAPIResponse {
  id: number,
  grant_name: string;
  organization: number,
  budget: string;
  start_date: string;
  end_date: string;
  status: 'PENDING' | 'ACTIVE' | 'COMPLETED';
  status_display: string;
  grant_purpose: string;
  created_at: string,
  updated_at: string
}

export interface OrganizationAttachment {
  id: number;
  objectKey: string;
  attachmentType: string;
  attachmentTypeName: string;
  originalFilename: string;
  remarks: string | null;
  status: "NOT_UPLOADED" | "PENDING" | "VERIFIED" | "REJECTED"; 
  uploadedAt: string;
  uploadedBy: number;
  uploadedByEmail: string;
}

export interface OrganizationAttachmentAPIResponse {

  id: number;
  object_key: string;
  attachment_type: string;
  attachment_type_name: string;
  original_filename: string;
  remarks: string | null;
  status: "PENDING" | "VERIFIED" | "REJECTED"; // same as above
  uploaded_at: string;
  uploaded_by: number;
  uploaded_by_email: string;
}


export interface OrganizationGrant {
  id: number;
  name: string;
  startDate: string; // Format: YYYY-MM-DD
  endDate: string;   // Format: YYYY-MM-DD
  purpose: string;
  amount: number; 
  fundingSources: string;
  createdAt: string; // ISO timestamp
  updatedAt: string; // ISO timestamp
  organization: number;
  grantMakerOrganization: number;
  recipientOrganizationName: string;
  attachments: any[];
}

export interface OrganizationGrantAPIResponse {
  id: number;
  grant_name: string;
  start_date: string; // Format: YYYY-MM-DD
  end_date: string;   // Format: YYYY-MM-DD
  grant_purpose: string;
  annual_budget: string; // or number if parsed
  funding_sources: string;
  created_at: string; // ISO timestamp
  updated_at: string; // ISO timestamp
  organization: number;
  grant_maker_organization: number;
  recipient_organization_name: string;
}



export interface Organization {
  id: number;
  logoKey: string | null;
  pointOfContactName: string | null;
  organizationFunctionType: "GRANTEE_ORGANIZATION" | string;
  organizationLegalType: "NON_PROFIT" | string;
  organizationFunctionTypeName: string;
  organizationLegalTypeName: string;
  organizationName: string;
  panNumber: string;
  phoneNumber: string;
  emailAddress: string;
  websiteUrl: string;
  numberOfTeamMembers: number;
  mission: string;
  vision: string;
  budget: number | null;
  registeredYear: number | null;
  backgroundHistory: string;
  csrRegistrationNumber: string;
  taxRegistrationNumber: string;
  taxRegistrationNumberUnder12A: string;
  fcraRegistrationNumber: string;
  trustRegistrationNumber: string;
  darpanId: string;
  createdAt: string;
  updatedAt: string;
  previousGrants: HistoricalGrant[];
  kmps: KMP[];
  grants: OrganizationGrant[];
  attachments: OrganizationAttachment[];
  address: Address;
}

export interface OrganizationAPIResponse {
  id: number;
  logo_key: string | null;
  point_of_contact_name: string | null;
  organization_name: string;
  organization_legal_type: string;
  organization_function_type: string;
  organization_legal_type_name: string;
  organization_function_type_name: string;
  pan_number: string;
  phone_number: string;
  email_address: string;
  website_url: string;
  number_of_team_members: number;
  mission: string;
  vision: string;
  registered_year: number | null;
  budget: number | null;
  background_history: string;
  previous_grants: HistoricalGrantAPIResponse[],
  tax_registration_number: string;
  csr_registration_number: string;
  tax_registration_number_under_12_a: string;
  trust_registration_number: string
  darpan_id: string;
  fcra_registration_number: string;
  created_at: string;
  updated_at: string;
  kmps: KMPAPIResponse[];
  grants: OrganizationGrantAPIResponse[];
  attachments: OrganizationAttachmentAPIResponse[];
  address: AddressAPIResponse;
}

export interface AddressAPIResponse {
  id: number;
  address_line_1: string;
  address_line_2: string;
  locality: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}
export interface Address {
  id: number;
  addressLine1: string;
  addressLine2: string;
  locality: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface KMP {
  id: number;
  name: string;
  designation: string;
  din: string;
  phoneNumber: string;
  email: string;
  organization: string;
}

export interface KMPUpdateRequest {
  name: string;
  designation: string;
  din: string;
  phone_number: string;
  email: string;
}

export interface KMPAPIResponse extends KMPUpdateRequest{
  id: number;
  organization: number;
}

// Historical Grants, Previous Grants
export interface HistoricalGrant{
  id: number;
  organization: number;
  grantName: string;
  grantPurpose: string;
  startDate: string;
  endDate: string;
  amount: number;
  status: "ACTIVE" | "PENDING" | "COMPLETED";
  statusDisplay: string;
  createdAt: string;
  updatedAt: string;
}


export interface OrganizationListAPIResponse {
  status: "SUCCESS" | "ERROR";
  data: OrganizationAPIResponse[];
  message?: string;
}


export interface GranteeProfile {
  id: string;
  name: string;
  sector: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  location: string;
  startDate: string;
  endDate?: string;
  status: 'active' | 'pending' | 'completed';
  description: string;
  mission: string;
  vision: string;
  foundedYear: number;
  registrationNumber: string;
  taxExemptionNumber?: string;
  taxRegistrationNumberUnder12A: string;
  darpanId: string;
  panCard: string;
  website?: string;
  grants: OrganizationGrant[];
  kmp_details?: Array<{
    id: string;
    name: string;
    email: string;
    phone: string;
    din: string;
    designation: string;
  }>;
  totalFunding: number;
  disbursedAmount: number,
  remainingAmount: number
  supportingDocuments: OrganizationAttachment[];
  expenseBreakdown?: Array<{ name: string; value: number; color: string }>;
  monthlyExpenses?: Array<{ month: string; budget: number; actual: number }>;

}
// --- REQUEST TYPES FOR ORGANIZATION UPDATE ---

export interface AddressUpdateRequest {
  address_line_1: string;
  address_line_2: string;
  locality: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export interface OrganizationGrantAPIRequest {
  grant_name: string;
  start_date: string;
  end_date: string;
  grant_purpose: string;
  annual_budget: string;
  funding_sources: string;
  organization: number;
  grant_maker_organization: number;
}

export interface OrganizationGrantHistoryAPIRequest {
  grant_name: string;
  budget: number;
  start_date: string;
  end_date: string;
  status: 'PENDING' | 'ACTIVE' | 'COMPLETED';
  grant_purpose: string;
}

export interface HistoricalGrantAPIRequest {
  grant_name: string;
  organization: number;
  budget: string;
  start_date: string;
  end_date: string;
  status: 'PENDING' | 'ACTIVE' | 'COMPLETED';
  grant_purpose: string;
}

export interface OrganizationUpdateRequest {
  logo_key: string | null;
  point_of_contact_name: string | null;
  organization_name: string;
  organization_legal_type: string;
  organization_function_type: string;
  pan_number: string;
  phone_number: string;
  email_address: string;
  website_url: string;
  number_of_team_members: number;
  mission: string;
  vision: string;
  budget: number | null;
  registered_year: number | null;
  background_history: string;
  previous_grants: HistoricalGrantAPIRequest[];
  tax_registration_number: string;
  csr_registration_number: string;
  tax_registration_number_under_12_a: string;
  trust_registration_number: string;
  darpan_id: string;
  fcra_registration_number: string;
  kmps: KMPUpdateRequest[];
  grants: OrganizationGrantAPIRequest[];
  attachments: OrganizationAttachmentAPIResponse[];
  address: AddressUpdateRequest;
}

export type APIErrorResponse = {
  message: string;
  errors?: Record<string, string[]>;
};

export interface AddKMPResponse {
  status: "SUCCESS" | "ERROR",
  data: KMPAPIResponse,
  message?: string;
}

export interface EditKMPResponse {
  status: "SUCCESS" | "ERROR",
  data: KMPAPIResponse,
  message?: string;
}

export interface DeleteKMPResponse {
  status: "SUCCESS" | "ERROR",
  data: KMPAPIResponse,
  message?: string;
}

export type AddHistoricalGrantResponse = {
  status: "SUCCESS" | "ERROR",
  data: HistoricalGrantAPIResponse,
  message?: string
}
export type EditHistoricalGrantResponse = {
  status: "SUCCESS" | "ERROR",
  data: HistoricalGrantAPIResponse,
  message?: string
}

export interface getOrganizationDetailsResponse {
  status: "SUCCESS" | "ERROR";
  data: OrganizationAPIResponse;
  detail?: string;
}

export interface updateOrganizationDetailsResponse {
  status: "SUCCESS" | "ERROR";
  data: OrganizationAPIResponse;
  detail?: string;
}

export interface DocumentType {
  id: number;
  code: string;
  name: string;
  description: string;
}

export interface DocumentTypeAPIResponse extends DocumentType {
}

export interface DocumentTypeListResponse {
  status: "SUCCESS" | "ERROR";
  data: DocumentTypeAPIResponse[];
  message?: string;
}
