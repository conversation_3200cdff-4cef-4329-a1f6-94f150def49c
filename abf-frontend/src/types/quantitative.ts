import z from "zod";

export const ImpactSchema = z.object({
  id: z.number(),
  impact_statement: z.string(),
  year: z.coerce.number(),
  pre_intervention_assessment: z.string().nullish(),
  post_intervention_assessment: z.string().nullish(),
  tools_used: z.string().nullish(),
});

export type ImpactType = z.infer<typeof ImpactSchema>;

export const ImpactSchemaWithOrganization = ImpactSchema.extend({
  organization: z.object({
    id: z.number(),
    organization_name: z.string(),
  }),
});

export type ImpactTypeWithOrganization = z.infer<
  typeof ImpactSchemaWithOrganization
>;

export const ActivitySchema = z.object({
  id: z.coerce.number(),
  description: z.string(),
  unit: z.string(),
  q1_plan: z.coerce.number().nullish(),
  q1_actual: z.coerce.number().nullish(),
  q1_remark: z.string().nullish(),
  q2_plan: z.coerce.number().nullish(),
  q2_actual: z.coerce.number().nullish(),
  q2_remark: z.string().nullish(),
  q3_plan: z.coerce.number().nullish(),
  q3_actual: z.coerce.number().nullish(),
  q3_remark: z.string().nullish(),
  q4_plan: z.coerce.number().nullish(),
  q4_actual: z.coerce.number().nullish(),
  q4_remark: z.string().nullish(),
  means_of_verification: z.string(),
  remarks: z.string().nullish(),
});

export type ActivityType = z.infer<typeof ActivitySchema>;

export const OutputSchema = z.object({
  id: z.coerce.number(),
  description: z.string(),
  unit: z.string(),
  q1_plan: z.coerce.number().nullish(),
  q1_actual: z.coerce.number().nullish(),
  q1_remark: z.string().nullish(),
  q2_plan: z.coerce.number().nullish(),
  q2_actual: z.coerce.number().nullish(),
  q2_remark: z.string().nullish(),
  q3_plan: z.coerce.number().nullish(),
  q3_actual: z.coerce.number().nullish(),
  q3_remark: z.string().nullish(),
  q4_plan: z.coerce.number().nullish(),
  q4_actual: z.coerce.number().nullish(),
  q4_remark: z.string().nullish(),
  means_of_verification: z.string(),
  remarks: z.string().nullish(),
  activities: z.array(ActivitySchema),
});

export type OutputType = z.infer<typeof OutputSchema>;

export const OutcomeSchema = z.object({
  id: z.number(),
  outcome_statement: z.string(),
  pre_intervention_assessment: z.string().nullish(),
  post_intervention_assessment: z.string().nullish(),
  tools_used: z.string().nullish(),
  outputs: z.array(OutputSchema),
});

export type OutcomeType = z.infer<typeof OutcomeSchema>;

export const OutcomeSchemaWithImpact = OutcomeSchema.extend({
  impact: ImpactSchema,
});

export type OutcomeTypeWithImpact = z.infer<typeof OutcomeSchemaWithImpact>;
