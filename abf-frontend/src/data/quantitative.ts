import { OutcomeType } from "@/types/quantitative";

export const impact = {
  id: 4,
  year: 2025,
  impact_statement:
    "Establishment of peer support groups for mental health and academic help",
  needs_assessment: "Focus group with students",
  pre_intervention_assessment: "No formal peer groups exist",
  post_intervention_assessment: "Peer groups active in 80% of classes",
  tools_used: "Group meeting logs, student surveys",
};

export const outcome: OutcomeType = {
  id: 1,
  outcome_statement:
    "Mental Health Ecosystem structure developed in 37 EMRS in Maharashtra",
  pre_intervention_assessment: "",
  post_intervention_assessment: "",
  tools_used: "",
  outputs: [
    {
      id: 1,
      description: "Cadre of trained 37 counsellors available in every EMRS",
      unit: "counsellors",
      q1_plan: 37,
      q1_actual: 25,
      q2_plan: 20,
      q2_actual: 20,
      q3_plan: undefined,
      q3_actual: undefined,
      q4_plan: undefined,
      q4_actual: undefined,
      means_of_verification: "Pre-post training survey",
      activities: [
        {
          id: 1,
          description: "Onboarding of counsellors",
          unit: "",
          q1_plan: undefined,
          q1_actual: 5,
          q2_plan: 5,
          q2_actual: undefined,
          q3_plan: 5,
          q3_actual: undefined,
          q4_plan: 5,
          q4_actual: undefined,
          means_of_verification: "TDD HR Records",
        },
        {
          id: 2,
          description: "Training of counsellors in RISE curriculum",
          unit: "",
          q1_plan: undefined,
          q1_actual: undefined,
          q2_plan: undefined,
          q2_actual: undefined,
          q3_plan: undefined,
          q3_actual: undefined,
          q4_plan: undefined,
          q4_actual: undefined,
          means_of_verification: "Training attendance sheet",
        },
      ],
    },
    {
      id: 2,
      description: "Superintendents sensitized in Mental Health",
      unit: "superintendents",
      q1_plan: 70,
      q1_actual: 70,
      q2_plan: undefined,
      q2_actual: undefined,
      q3_plan: undefined,
      q3_actual: undefined,
      q4_plan: undefined,
      q4_actual: undefined,
      means_of_verification: "Training feedback; pre-post assessments",
      activities: [
        {
          id: 1,
          description: "12 hour training of superintendents",
          unit: "",
          q2_plan: 10,
          q1_actual: undefined,
          q3_plan: 3,
          q2_actual: undefined,
          q4_plan: 2,
          q3_actual: undefined,
          q4_actual: undefined,
          means_of_verification: "Attendance; workshop reports",
        },
      ],
    },
    {
      id: 3,
      description:
        "Each school adopts academic calendar including RISE sessions",
      unit: "counsellors",
      q1_plan: undefined,
      q1_actual: undefined,
      q2_plan: undefined,
      q2_actual: undefined,
      q3_plan: undefined,
      q3_actual: undefined,
      q4_plan: undefined,
      q4_actual: undefined,
      means_of_verification: "Academic calendars",
      activities: [
        {
          id: 1,
          description: "Finalization of calendar with schools",
          unit: "",
          q1_plan: undefined,
          q1_actual: undefined,
          q2_plan: undefined,
          q2_actual: undefined,
          q3_plan: undefined,
          q3_actual: undefined,
          q4_plan: undefined,
          q4_actual: undefined,
          means_of_verification: "Finalized academic calendars",
        },
      ],
    },
  ],
};

export const outcomes = Array.from({ length: 5 }).map(() => outcome);
