// app/support-faq/page.tsx
"use client";

import { useEffect, useState } from "react";
import Head from "next/head";
import { Search, ChevronDown, ChevronUp, HelpCircle } from "lucide-react";

export default function SupportFAQ() {
  const [faqs, setFaqs] = useState([
    {
      question: "What is the eligibility criteria for applying to a grant?",
      answer:
        "To be eligible for a grant, your organization must be a registered non-profit, have a valid tax-exempt status, and align with the grant's focus area (e.g., education, healthcare, or environmental sustainability). Additionally, you must submit a detailed project proposal and budget plan.",
      isOpen: false,
    },
    {
      question: "How long does the grant approval process take?",
      answer:
        "The grant approval process typically takes 6-8 weeks from the submission deadline. This includes an initial review (2 weeks), a detailed evaluation by the committee (3-4 weeks), and final approval (1-2 weeks). You will be notified via email at each stage.",
      isOpen: false,
    },
    {
      question: "Can I apply for multiple grants at the same time?",
      answer:
        "Yes, you can apply for multiple grants simultaneously. However, each application must be for a distinct project, and you must ensure that the projects do not overlap in terms of funding requests or objectives. Duplicate submissions may lead to disqualification.",
      isOpen: false,
    },
    {
      question: "What happens if I miss the application deadline?",
      answer:
        "If you miss the application deadline, your application will not be considered for that funding cycle. You can apply in the next cycle, which typically opens every quarter. Check the 'Grant Calendar' section for upcoming deadlines.",
      isOpen: false,
    },
    {
      question: "How can I contact support for technical issues?",
      answer:
        "For technical issues, you can reach out to our support team via <NAME_EMAIL> or call our helpline at +1-800-555-1234, available Monday to Friday, 9 AM to 5 PM EST. Alternatively, you can raise a ticket through the 'Support' section of the portal.",
      isOpen: false,
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFaqs, setFilteredFaqs] = useState(faqs);

  // Handle search functionality
  useEffect(() => {
    if (!searchTerm) {
      setFilteredFaqs(faqs);
      return;
    }

    const lowerSearch = searchTerm.toLowerCase();
    const filtered = faqs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(lowerSearch) ||
        faq.answer.toLowerCase().includes(lowerSearch)
    );
    setFilteredFaqs(filtered);
  }, [searchTerm, faqs]);

  const toggleFAQ = (index) => {
    setFilteredFaqs(
      filteredFaqs.map((faq, i) =>
        i === index ? { ...faq, isOpen: !faq.isOpen } : { ...faq, isOpen: false }
      )
    );
  };

  return (
    <>
      <Head>
        <title>Support FAQ - Grant Management System</title>
      </Head>

      <div className="min-h-screen p-8 bg-white">
        {/* FAQ Section */}
        <div className="w-full max-w-5xl mx-auto">
          <div className="flex items-center space-x-3 mb-6">
            <HelpCircle className="w-8 h-8 text-teal-600" />
            <h2 className="text-3xl font-bold text-teal-600 tracking-tight">
              Frequently Asked Questions
            </h2>
          </div>

          {/* Search Bar */}
          <div className="relative mb-8">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
            <input
              type="text"
              placeholder="Search FAQs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 text-sm border border-gray-200 rounded-lg shadow-md focus:ring-2 focus:ring-teal-600 focus:border-teal-600 transition-all duration-300 bg-white placeholder-gray-400"
            />
          </div>

          {/* FAQ List */}
          {filteredFaqs.length === 0 ? (
            <p className="text-gray-600 text-center text-lg italic">
              No FAQs found matching your search.
            </p>
          ) : (
            filteredFaqs.map((faq, index) => (
              <div
                key={index}
                className="mb-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-200 w-full box-border"
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full text-left py-5 px-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center space-x-3">
                    <HelpCircle className="w-5 h-5 text-gray-600" />
                    <span className="text-lg font-semibold text-gray-800">
                      {faq.question}
                    </span>
                  </div>
                  <span className="text-gray-600">
                    {faq.isOpen ? (
                      <ChevronUp className="w-6 h-6 text-teal-600 hover:text-teal-800 transition-colors duration-200" />
                    ) : (
                      <ChevronDown className="w-6 h-6 text-teal-600 hover:text-teal-800 transition-colors duration-200" />
                    )}
                  </span>
                </button>
                {faq.isOpen && (
                  <div className="px-6 pb-5 text-gray-600 leading-relaxed border-t border-gray-100 overflow-wrap break-word w-full">
                    {faq.answer || "No answer provided yet."}
                    {faq.answer && (
                      <a
                        href="#"
                        className="text-teal-600 hover:underline ml-2 font-medium flex items-center space-x-1"
                      >
                        {/* <span>Dive Deeper</span> */}
                        {/* <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        > */}
                          {/* <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 5l7 7-7 7"
                          />
                        </svg> */}
                      </a>
                    )}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </>
  );
}