// app/support-ticket/my-tickets/page.tsx
"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Head from "next/head";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { format, formatDistanceToNow } from "date-fns";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  Download,
  AlertCircle,
  Ticket,
  Clock,
  MessageSquare,
  CheckCircle,
  X,
  ChevronRight,
} from "lucide-react";
import { fetchAllTickets, getGrants } from "@/services/supportTicket.service";
import { toast } from "sonner";

export default function MyTickets() {
  const router = useRouter();
  const [tickets, setTickets] = useState([]);
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [grants, setGrants] = useState([]);
  const [grantFilter, setGrantFilter] = useState([]);
  const [statusFilter, setStatusFilter] = useState([]);
  const [customStartDate, setCustomStartDate] = useState(null);
  const [customEndDate, setCustomEndDate] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const pollingRef = useRef(null);
  const lastStatuses = useRef({});
  const lastMessages = useRef({});
  const startRef = useRef(null);
  const endRef = useRef(null);

  const STATUSES = ["open", "under review", "resolved"];

  const formatDate = (date) => {
    const d = new Date(date);
    return format(d, "dd MMM yyyy");
  };

  const capitalize = (text) =>
    text ? text.charAt(0).toUpperCase() + text.slice(1).toLowerCase() : "";

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [ticketData, grantData] = await Promise.all([fetchAllTickets(), getGrants()]);
        const formattedTickets = ticketData.map((t) => ({
          id: t.id,
          title: t.title || `Ticket #${t.id}`,
          grant_name: grantData.find((g) => g.id === t.grant)?.grant_name || `Grant ${t.grant}`,
          status: t.status?.toLowerCase().replace(/\s+/g, "-"),
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));
        setTickets(formattedTickets);
        setGrants(grantData);
        setFilteredTickets(formattedTickets);
        setLoading(false);

        ticketData.forEach((t) => {
          lastStatuses.current[t.id] = t.status;
          lastMessages.current[t.id] = new Date(t.created_at).getTime();
        });
      } catch (err) {
        setError(err.message || "Failed to load tickets or grants.");
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  useEffect(() => {
    pollingRef.current = setInterval(async () => {
      try {
        const updated = await fetchAllTickets();
        const grantData = await getGrants();
        const formattedUpdated = updated.map((t) => ({
          id: t.id,
          title: t.title || `Ticket #${t.id}`,
          grant_name: grantData.find((g) => g.id === t.grant)?.grant_name || `Grant ${t.grant}`,
          status: t.status?.toLowerCase().replace(/\s+/g, "-"),
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));

        updated.forEach((ticket) => {
          const prevStatus = lastStatuses.current[ticket.id];
          const prevCreatedAt = lastMessages.current[ticket.id];

          if (ticket.status !== prevStatus) {
            toast.info(`Ticket ${ticket.id} status changed to ${capitalize(ticket.status)}`);
            lastStatuses.current[ticket.id] = ticket.status;
          }

          if (new Date(ticket.created_at).getTime() > prevCreatedAt) {
            toast(`New update on Ticket ${ticket.id}`, {
              description: ticket.title || "",
            });
            lastMessages.current[ticket.id] = new Date(ticket.created_at).getTime();
          }
        });

        setTickets(formattedUpdated);
      } catch {
        // Silent fail
      }
    }, 5000);

    return () => clearInterval(pollingRef.current);
  }, []);

  const normalize = (text) =>
    text?.toLowerCase().replace(/[^a-z0-9 ]/gi, "") || "";

  const filteredTicketsMemo = useMemo(() => {
    const search = normalize(searchTerm);
    const tokens = search.split(" ").filter(Boolean);

    let result = [...tickets];

    if (statusFilter.length > 0) {
      result = result.filter((t) => statusFilter.includes(t.status));
    }

    if (grantFilter.length > 0) {
      result = result.filter((t) => grantFilter.includes(t.grant_name));
    }

    if (customStartDate || customEndDate) {
      result = result.filter((t) => {
        const created = new Date(t.created_at);
        return (
          (!customStartDate || created >= customStartDate) &&
          (!customEndDate || created <= customEndDate)
        );
      });
    }

    return result.filter((t) => {
      const target = normalize(`${t.title} ${t.grant_name} ${t.status}`);
      return search.length === 0 || tokens.every((token) => target.includes(token));
    });
  }, [tickets, searchTerm, statusFilter, grantFilter, customStartDate, customEndDate]);

  const quickStats = useMemo(
    () => [
      {
        label: "Total Tickets",
        value: filteredTicketsMemo.length,
        icon: <Ticket className="w-5 h-5 text-gray-600" />,
        bg: "bg-white border border-gray-200 shadow-sm",
        color: "text-gray-900",
      },
      {
        label: "Open",
        value: filteredTicketsMemo.filter((t) => t.status === "open").length,
        icon: <MessageSquare className="w-5 h-5 text-blue-600" />,
        bg: "bg-white border border-gray-200 shadow-sm",
        color: "text-blue-600",
      },
      {
        label: "Under Review",
        value: filteredTicketsMemo.filter((t) => t.status === "under-review").length,
        icon: <Clock className="w-5 h-5 text-amber-600" />,
        bg: "bg-white border border-gray-200 shadow-sm",
        color: "text-amber-600",
      },
      {
        label: "Resolved",
        value: filteredTicketsMemo.filter((t) => t.status === "resolved").length,
        icon: <CheckCircle className="w-5 h-5 text-green-600" />,
        bg: "bg-white border border-gray-200 shadow-sm",
        color: "text-green-600",
      },
    ],
    [filteredTicketsMemo]
  );

  const allGrants = useMemo(() => {
    const names = tickets.map((t) => t.grant_name);
    return [...new Set(names)].sort();
  }, [tickets]);

  const getStatusBadgeStyle = (status) => {
    switch (status) {
      case "open":
        return "bg-teal-100 text-teal-800 border-teal-200";
      case "under-review":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "resolved":
        return "bg-teal-100 text-teal-800 border-teal-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter([]);
    setGrantFilter([]);
    setCustomStartDate(null);
    setCustomEndDate(null);
  };

  return (
    <>
      <Head>
        <title>My Tickets</title>
      </Head>

      <div className="w-full px-4 py-6">
        <Card className="bg-white shadow-sm border border-gray-200">
          <CardHeader className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="border-l-4 border-teal-600 pl-4">
                <CardTitle className="text-2xl font-semibold text-teal-600">
                  My Tickets
                </CardTitle>
                <CardDescription className="text-gray-600 text-sm mt-1">
                  Seamlessly manage and track your support tickets
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  className="bg-teal-600 hover:bg-teal-700 text-white transition-all duration-200 hover:shadow-md rounded-lg"
                  onClick={() => router.push("/support-ticket/my-tickets/create-support-ticket")}
                >
                  Raise Ticket
                </Button>
                <Button
                  size="sm"
                  className="bg-teal-600 hover:bg-teal-700 text-white transition-all duration-200 hover:shadow-md rounded-lg"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Tickets
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {quickStats.map((stat) => (
                <Card
                  key={stat.label}
                  className={`${stat.bg} rounded-xl hover:shadow-lg transition-all duration-300 group cursor-default`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 rounded-lg bg-gray-50">
                          {stat.icon}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                          <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Filters and Search */}
            <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
              <div className="relative w-full md:w-80">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search tickets or grants..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 focus:border-gray-400 focus:ring-2 focus:ring-gray-200 outline-none transition-all text-sm placeholder:text-gray-400 rounded-lg"
                />
              </div>
              <div className="flex items-center gap-2 w-full md:w-auto">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      className="bg-gray-800 hover:bg-gray-900 text-white transition-all duration-200 rounded-lg"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Status
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-48 shadow-lg border-gray-200">
                    <DropdownMenuLabel className="text-gray-700 font-medium">Filter by Status</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-200" />
                    {STATUSES.map((status) => (
                      <DropdownMenuCheckboxItem
                        key={status}
                        checked={statusFilter.includes(status)}
                        onCheckedChange={(checked) => {
                          setStatusFilter((prev) =>
                            checked ? [...prev, status] : prev.filter((s) => s !== status)
                          );
                        }}
                        className="capitalize text-gray-700 hover:bg-gray-50"
                      >
                        {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      className="bg-gray-800 hover:bg-gray-900 text-white transition-all duration-200 rounded-lg"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Grant
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64 max-h-60 overflow-y-auto shadow-lg border-gray-200">
                    <DropdownMenuLabel className="text-gray-700 font-medium">Filter by Grant</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-200" />
                    {allGrants.map((grant) => (
                      <DropdownMenuCheckboxItem
                        key={grant}
                        checked={grantFilter.includes(grant)}
                        onCheckedChange={(checked) => {
                          setGrantFilter((prev) =>
                            checked ? [...prev, grant] : prev.filter((g) => g !== grant)
                          );
                        }}
                        className="text-gray-700 hover:bg-gray-50"
                      >
                        {grant.length > 28 ? `${grant.slice(0, 26)}...` : grant}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DatePicker
                  selected={customStartDate}
                  onChange={(date) => {
                    setCustomStartDate(date);
                    if (customEndDate && date > customEndDate) setCustomEndDate(null);
                  }}
                  placeholderText="Start Date"
                  dateFormat="dd MMM yyyy"
                  className="px-3 py-2 border rounded-lg text-sm w-32"
                  maxDate={customEndDate || undefined}
                  ref={startRef}
                />
                <DatePicker
                  selected={customEndDate}
                  onChange={setCustomEndDate}
                  placeholderText="End Date"
                  dateFormat="dd MMM yyyy"
                  className="px-3 py-2 border rounded-lg text-sm w-32"
                  minDate={customStartDate || undefined}
                  ref={endRef}
                />

                <Button
                  size="sm"
                  onClick={clearFilters}
                  className="bg-gray-800 hover:bg-gray-900 text-white transition-all duration-200 rounded-lg"
                >
                  Clear Filters
                </Button>
              </div>
            </div>

            {/* Filter Tags */}
            <div className="flex flex-wrap gap-2 mb-6">
              {statusFilter.map((status) => (
                <span
                  key={status}
                  className="flex items-center gap-1 text-xs bg-gray-100 text-gray-800 px-2 py-1 border border-gray-200 hover:bg-gray-200 transition-all rounded-lg"
                >
                  {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                  <X
                    className="w-3 h-3 cursor-pointer text-gray-500 hover:text-teal-800"
                    onClick={() => setStatusFilter((prev) => prev.filter((s) => s !== status))}
                  />
                </span>
              ))}
              {grantFilter.map((grant) => (
                <span
                  key={grant}
                  className="flex items-center gap-1 text-xs bg-teal-100 text-teal-800 px-2 py-1 border border-teal-200 hover:bg-teal-200 transition-all rounded-lg"
                >
                  {grant.length > 28 ? `${grant.slice(0, 26)}...` : grant}
                  <X
                    className="w-3 h-3 cursor-pointer text-gray-500 hover:text-gray-800"
                    onClick={() => setGrantFilter((prev) => prev.filter((g) => g !== grant))}
                  />
                </span>
              ))}
              {customStartDate && (
                <span
                  className="flex items-center gap-1 text-xs bg-gray-100 text-gray-800 px-2 py-1 border border-gray-200 hover:bg-gray-200 transition-all rounded-lg"
                >
                  Start: {format(customStartDate, "dd MMM yyyy")}
                  <X
                    className="w-3 h-3 cursor-pointer text-gray-500 hover:text-gray-800"
                    onClick={() => setCustomStartDate(null)}
                  />
                </span>
              )}
              {customEndDate && (
                <span
                  className="flex items-center gap-1 text-xs bg-gray-100 text-gray-800 px-2 py-1 border border-gray-200 hover:bg-gray-200 transition-all rounded-lg"
                >
                  End: {format(customEndDate, "dd MMM yyyy")}
                  <X
                    className="w-3 h-3 cursor-pointer text-gray-500 hover:text-gray-800"
                    onClick={() => setCustomEndDate(null)}
                  />
                </span>
              )}
            </div>

            {/* Table View */}
            <div className="w-full">
              {loading ? (
                <p className="text-gray-600 text-center py-10">Loading tickets...</p>
              ) : error ? (
                <div className="text-center text-gray-600 py-10">
                  <AlertCircle className="w-6 h-6 mb-2 mx-auto text-gray-400" />
                  <p className="text-sm font-medium">Error: {error}</p>
                </div>
              ) : filteredTicketsMemo.length === 0 ? (
                <div className="text-center text-gray-600 py-10">
                  <Ticket className="w-6 h-6 mb-2 mx-auto text-gray-400" />
                  <p className="text-sm font-medium">No tickets found matching your filters.</p>
                </div>
              ) : (
                <table className="w-full border-collapse text-sm">
                  <thead>
                    <tr className="bg-gray-50 text-gray-600">
                      <th className="p-4 text-left font-medium">Ticket ID</th>
                      <th className="p-4 text-left font-medium">Title</th>
                      <th className="p-4 text-left font-medium">Grant</th>
                      <th className="p-4 text-left font-medium">Status</th>
                      <th className="p-4 text-left font-medium">Created</th>
                      <th className="p-4 text-left font-medium">Updated</th>
                      <th className="p-4 text-left font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTicketsMemo.map((ticket, index) => (
                      <tr
                        key={ticket.id}
                        className="hover:bg-gray-50 transition-all duration-200 cursor-pointer"
                        style={{ animation: `fadeIn 0.3s ease-in ${index * 100}ms` }}
                        onClick={() =>
                          router.push(`/support-ticket/my-tickets/view-support-ticket?id=${ticket.id}`)
                        }
                      >
                        <td className="p-4 border-b border-gray-200">{ticket.id}</td>
                        <td className="p-4 border-b border-gray-200">
                          <div className="font-medium text-gray-800">{ticket.title}</div>
                          <div className="text-xs text-gray-500">
                            Created: {format(new Date(ticket.created_at), "dd MMM yyyy")}
                          </div>
                        </td>
                        <td className="p-4 border-b border-gray-200 text-gray-700">
                          {ticket.grant_name}
                        </td>
                        <td className="p-4 border-b border-gray-200">
                          <span
                            className={`inline-flex items-center px-2 py-1 text-xs font-medium border rounded-lg ${getStatusBadgeStyle(
                              ticket.status
                            )}`}
                          >
                            {ticket.status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                          </span>
                        </td>
                        <td className="p-4 border-b border-gray-200 text-gray-600 text-xs">
                          {format(new Date(ticket.created_at), "dd MMM yyyy")}
                        </td>
                        <td className="p-4 border-b border-gray-200 text-gray-600 text-xs">
                          {formatDistanceToNow(new Date(ticket.updated_at), {
                            addSuffix: true,
                          })}
                        </td>
                        <td className="p-4 border-b border-gray-200">
                          <Button
                            size="sm"
                            className="bg-gray-800 hover:bg-gray-900 text-white transition-all duration-200 rounded-lg"
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(
                                `/support-ticket/my-tickets/view-support-ticket?id=${ticket.id}`
                              );
                            }}
                          >
                            View
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}