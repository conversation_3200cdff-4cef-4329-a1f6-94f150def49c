
"use client";

import { useEffect, useState, useRef } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Head from "next/head";
import {
  ArrowLeft,
  FileDown,
  FileImage,
  Download,
  MessageSquareText,
  CheckCircle,
  RefreshCw,
  XCircle,
} from "lucide-react";
import { toast } from "sonner";
import { getTicketDetails } from "@/services/supportTicket.service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function ViewTicketPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const ticketId = searchParams.get("id");

  const [ticket, setTicket] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastStatusRef = useRef<string | null>(null);
  const lastUpdatedRef = useRef<number | null>(null);

  const capitalizeEachWord = (text: string) =>
    text?.split(" ").map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(" ") || "";

  const getStatusBadgeClass = (status: string) => {
    const map: Record<string, string> = {
      "open": "bg-gradient-to-r from-teal-50 to-teal-100 text-teal-800 border-teal-200 shadow-sm",
      "under-review": "bg-gradient-to-r from-amber-50 to-orange-100 text-amber-800 border-amber-200 shadow-sm",
      "resolved": "bg-gradient-to-r from-emerald-50 to-green-100 text-emerald-800 border-emerald-200 shadow-sm",
    };
    return `${map[status?.toLowerCase().replace(/\s+/g, "-")] ?? "bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 border-gray-200 shadow-sm"} px-3 py-1 rounded-lg text-sm font-semibold border`;
  };

  const getPriorityBadgeColor = (priority: string) => {
    const map: Record<string, string> = {
      low: "bg-gray-100 text-gray-800 border-gray-200",
      medium: "bg-yellow-100 text-yellow-800 border-yellow-200",
      high: "bg-orange-100 text-orange-800 border-orange-200",
      urgent: "bg-red-100 text-red-800 border-red-200",
    };
    return `${map[priority?.toLowerCase()] ?? "bg-gray-100 text-gray-800 border-gray-200"} px-3 py-1 rounded-full text-sm font-semibold border`;
  };

  const showToast = (icon: any, colorClass: string, title: string, description: string) => {
    toast.custom(() => (
      <div className={`flex items-center gap-3 px-5 py-3 border shadow-md rounded-lg ${colorClass}`}>
        {icon}
        <div className="text-sm leading-snug">
          <p className="font-semibold text-gray-800">{title}</p>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>
    ));
  };

  const showStatusChangeToast = (status: string) => {
    showToast(
      <CheckCircle className="w-5 h-5 text-teal-600" />,
      "bg-teal-50 border-teal-200 text-teal-800",
      "Status Updated",
      `Changed to ${capitalizeEachWord(status)}`
    );
  };

  const showUpdatedToast = (id: number) => {
    showToast(
      <RefreshCw className="w-5 h-5 text-teal-600 animate-spin" />,
      "bg-teal-50 border-teal-200 text-teal-800",
      "Ticket Updated",
      `PF-${id.toString().padStart(4, "0")} was modified.`
    );
  };

  const showLoadErrorToast = () => {
    showToast(
      <XCircle className="w-5 h-5 text-red-600" />,
      "bg-red-50 border-red-200 text-red-600",
      "Ticket Load Failed",
      "Unable to load this ticket. Redirecting..."
    );
  };

  useEffect(() => {
    if (!ticketId) {
      showLoadErrorToast();
      router.replace("/support-ticket/my-tickets");
      return;
    }

    const fetchTicket = async () => {
      try {
        const data = await getTicketDetails(ticketId);
        if (!data?.id) throw new Error("Invalid ticket response");
        setTicket(data);
        lastStatusRef.current = data.status;
        lastUpdatedRef.current = new Date(data.updated_at).getTime();
      } catch (err) {
        showLoadErrorToast();
        router.replace("/support-ticket/my-tickets");
      } finally {
        setLoading(false);
      }
    };

    fetchTicket();
  }, [ticketId, router]);

  useEffect(() => {
    if (!ticketId) return;

    pollingRef.current = setInterval(async () => {
      try {
        const updated = await getTicketDetails(ticketId);
        if (!updated) return;

        if (lastStatusRef.current !== updated.status) {
          showStatusChangeToast(updated.status);
          lastStatusRef.current = updated.status;
        }

        const updatedTime = new Date(updated.updated_at).getTime();
        if (updatedTime > (lastUpdatedRef.current ?? 0)) {
          showUpdatedToast(updated.id);
          lastUpdatedRef.current = updatedTime;
        }

        setTicket(updated);
      } catch (err) {
        console.error("Polling error", err);
      }
    }, 5000);

    return () => clearInterval(pollingRef.current!);
  }, [ticketId]);

  const handleExport = () => {
    if (ticket) {
      window.open(`/support-ticket/export?id=${ticket.id}`, "_blank");
    }
  };

  if (loading || !ticket) {
    return (
      <div className="flex items-center justify-center h-full text-gray-600 text-base font-semibold">
        Loading ticket details...
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Ticket PF-{ticket.id.toString().padStart(4, "0")} - {ticket.title}</title>
        <meta name="description" content={`Support Ticket: ${ticket.title}`} />
      </Head>

      <div className="w-full px-4 py-6">
        <div className="mb-6">
          <Button
            onClick={() => router.push("/support-ticket/my-tickets")}
            className="bg-gray-800 hover:bg-gray-900 text-white rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Tickets
          </Button>
        </div>

        <Card className="bg-white shadow-md border-none max-w-5xl mx-auto">
          <CardHeader className="bg-gradient-to-r from-teal-50 to-teal-100 border-b border-teal-200 px-6 py-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                  <MessageSquareText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-gray-900">
                    Ticket #{ticket.id}
                  </CardTitle>
                  <CardDescription className="text-base text-gray-600 mt-1">
                    {ticket.title}
                  </CardDescription>
                </div>
              </div>
              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={() =>
                    router.push(`/support-ticket/my-tickets/ticket-history?id=${ticket.id}`)
                  }
                  className="px-6 py-3 text-base font-semibold bg-teal-600 text-white hover:bg-teal-700 transition-all duration-200 rounded-lg shadow-sm hover:shadow-md"
                >
                  <MessageSquareText className="w-5 h-5 mr-2" /> Chat Now
                </Button>
                <Button
                  onClick={handleExport}
                  className="px-6 py-3 text-base font-semibold bg-gray-800 text-white hover:bg-gray-900 transition-all duration-200 rounded-lg shadow-sm hover:shadow-md"
                >
                  <FileDown className="w-5 h-5 mr-2" /> Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-8">
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="bg-gradient-to-r from-teal-50 to-white px-6 py-4 border-b border-gray-200 rounded-t-lg">
                  <h3 className="text-lg font-semibold text-teal-800 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    Ticket Information
                  </h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {[
                      {
                        icon: <XCircle className="w-4 h-4" />,
                        label: "Category",
                        value: <span className="font-semibold text-gray-800 capitalize">{ticket.category}</span>
                      },
                      {
                        icon: <RefreshCw className="w-4 h-4" />,
                        label: "Priority",
                        value: (
                          <span className={getPriorityBadgeColor(ticket.priority)}>
                            {capitalizeEachWord(ticket.priority)}
                          </span>
                        )
                      },
                      {
                        icon: <CheckCircle className="w-4 h-4" />,
                        label: "Status",
                        value: (
                          <span className={getStatusBadgeClass(ticket.status)}>
                            {capitalizeEachWord(ticket.status)}
                          </span>
                        )
                      },
                      {
                        icon: <MessageSquareText className="w-4 h-4" />,
                        label: "Title",
                        value: <span className="font-semibold text-gray-800">{ticket.title}</span>,
                        fullWidth: true
                      },
                      {
                        icon: <FileImage className="w-4 h-4" />,
                        label: "Description",
                        value: (
                          <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                            {ticket.description}
                          </p>
                        ),
                        fullWidth: true
                      }
                    ].map((item, i) => (
                      <div key={i} className={`${item.fullWidth ? 'md:col-span-2' : ''} p-4 bg-gray-50 rounded-lg`}>
                        <div className="flex items-center gap-2 text-gray-600 mb-2">
                          {item.icon}
                          <span className="font-medium text-sm uppercase tracking-wide">{item.label}</span>
                        </div>
                        <div>
                          {item.value}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="bg-gradient-to-r from-teal-50 to-white px-6 py-4 border-b border-gray-200 rounded-t-lg">
                  <h3 className="text-lg font-semibold text-teal-800 flex items-center gap-2">
                    <FileImage className="w-5 h-5" />
                    Attachments
                  </h3>
                </div>
                <div className="p-6">
                  {ticket.attachments?.length > 0 ? (
                    <div className="grid gap-4">
                      {ticket.attachments.map((url: string, i: number) => {
                        const fileName = new URL(url).pathname.split("/").pop()?.split("?")[0];
                        return (
                          <div
                            key={i}
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:shadow-md transition-all"
                          >
                            <div className="flex items-center gap-3">
                              <FileImage className="w-6 h-6 text-teal-600" />
                              <span className="text-gray-800 font-medium">{fileName}</span>
                            </div>
                            <a
                              href={url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-teal-600 hover:text-teal-700 font-medium flex items-center gap-2 hover:underline"
                            >
                              <Download className="w-4 h-4" /> Download
                            </a>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <p className="text-gray-500 italic">No Attachments Available</p>
                  )}
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="bg-gradient-to-r from-teal-50 to-white px-6 py-4 border-b border-gray-200 rounded-t-lg">
                  <h3 className="text-lg font-semibold text-teal-800 flex items-center gap-2">
                    <MessageSquareText className="w-5 h-5" />
                    Contact Information
                  </h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {[
                      {
                        label: "Grant Name",
                        value: ticket.grant_name,
                        icon: <CheckCircle className="w-4 h-4" />
                      },
                      {
                        label: "Phone Number",
                        value: ticket.phone,
                        icon: <RefreshCw className="w-4 h-4" />
                      },
                      {
                        label: "Point of Contact Name",
                        value: ticket.point_of_contact_name || "No name here please",
                        icon: <XCircle className="w-4 h-4" />
                      },
                      {
                        label: "Email",
                        value: ticket.email,
                        icon: <MessageSquareText className="w-4 h-4" />
                      }
                    ].map((item, i) => (
                      <div key={i} className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2 text-gray-600 mb-2">
                          {item.icon}
                          <span className="font-medium text-sm uppercase tracking-wide">{item.label}</span>
                        </div>
                        <p className="font-semibold text-gray-800">{item.value}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
