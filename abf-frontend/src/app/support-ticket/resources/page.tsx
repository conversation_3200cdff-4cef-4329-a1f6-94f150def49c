// src/app/support-ticket/resources/page.tsx
"use client";

import Head from "next/head";
import { useEffect, useState } from "react";
import { FileText, Download, DownloadCloud } from "lucide-react";
import { getResources } from "@/services/resources-service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface Resource {
  id: number;
  title: string;
  file_description?: string;
  created_at: string;
  attachments: string[];
  file_names?: string[];
}

export default function SupportResources() {
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredResources, setFilteredResources] = useState<Resource[]>([]);

  // Fetch resources on component mount
  useEffect(() => {
    const fetchResources = async () => {
      try {
        const data = await getResources();
        const mappedData = data.map((resource: Resource) => ({
          ...resource,
          file_names: resource.file_names || resource.attachments.map((url, i) => url.split('/').pop() || `File ${i + 1}`),
        }));
        setResources(mappedData);
        setFilteredResources(mappedData);
        setLoading(false);
      } catch (err: any) {
        setError(err.message);
        setLoading(false);
      }
    };
    fetchResources();
  }, []);

  // Handle search functionality
  useEffect(() => {
    if (!searchTerm) {
      setFilteredResources(resources);
      return;
    }
    const lowerSearch = searchTerm.toLowerCase();
    const filtered = resources.filter(
      (resource) =>
        resource.title.toLowerCase().includes(lowerSearch) ||
        (resource.file_description && resource.file_description.toLowerCase().includes(lowerSearch))
    );
    setFilteredResources(filtered);
  }, [searchTerm, resources]);

  // Handle individual file download
  const handleDownload = (fileUrl: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = fileName || "resource";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle download all files via server-side ZIP
  const handleDownloadAll = async () => {
    const JSZip = (await import('jszip')).default;
    const FileSaver = (await import('file-saver')).default;
    const zip = new JSZip();
    try {
      for (const resource of resources) {
        for (let i = 0; i < resource.attachments.length; i++) {
          const fileUrl = resource.attachments[i];
          const fileName = resource.file_names?.[i] || `File ${i + 1}`;
          const response = await fetch(fileUrl);
          if (!response.ok) throw new Error(`Failed to fetch ${fileName}`);
          const blob = await response.blob();
          zip.file(fileName, blob);
        }
      }
      const content = await zip.generateAsync({ type: 'blob' });
      FileSaver.saveAs(content, 'resources.zip');
    } catch (err: any) {
      console.error('Download all error:', err);
      setError('Failed to download all files');
    }
  };

  return (
    <>
      <Head>
        <title>Download Resources - Grant Management System</title>
      </Head>
      <div className="min-h-screen p-6 bg-white">
        <div className="w-full max-w-6xl mx-auto space-y-6">
          {/* Header */}


          {/* Search Bar */}
          <div className="relative mb-6">
            <input
              type="text"
              placeholder="Search resources..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-3 pr-10 rounded-lg border border-gray-200 shadow-sm focus:ring-2 focus:ring-gray-300 focus:border-gray-500 text-sm bg-white transition-all duration-300"
            />
            <svg
              className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 1116.65 16.65z"
              />
            </svg>
          </div>

          {/* Resources List */}
          <Card className="bg-white/90 backdrop-blur-sm shadow-lg rounded-xl border border-gray-100">
            <CardHeader className="flex flex-row items-center justify-between p-6 border-b border-gray-100">
              <CardTitle className="text-xl font-semibold text-gray-800 ">
              <div className="flex items-center space-x-4 mb-6">
                <FileText className="w-8 h-8 text-teal-600" />
                <h2 className="text-3xl font-bold text-teal-800 tracking-tight ">
                  Resources
                </h2>
              </div>
            </CardTitle>
              {filteredResources.length > 0 && (
                <Button
                  onClick={handleDownloadAll}
                  className="flex items-center space-x-2 bg-gray-800 hover:bg-gray-900 text-white text-sm px-4 py-2 rounded-lg transition-all duration-300 shadow-sm"
                  aria-label="Download all resources as ZIP"
                >
                  <DownloadCloud className="w-4 h-4" />
                  <span>Download All</span>
                </Button>
              )}
            </CardHeader>
            <CardContent className="p-6">
              {loading && (
                <p className="text-gray-500 text-center text-sm font-medium animate-pulse">
                  Loading resources...
                </p>
              )}
              {error && (
                <p className="text-red-500 text-center text-sm font-medium bg-red-50 p-3 rounded-lg">
                  {error}
                </p>
              )}
              {!loading && !error && filteredResources.length === 0 && (
                <p className="text-gray-500 text-center text-sm italic">
                  No resources available.
                </p>
              )}
              <div className="space-y-4">
                {filteredResources.map((resource) => (
                  <div
                    key={resource.id}
                    className="group bg-white border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                  >
                    <div className="p-5 flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <FileText className="w-6 h-6 text-gray-600" />
                          <h3 className="text-lg font-semibold text-gray-800  group-hover:text-gray-900 transition-colors duration-300">
                            {resource.title}
                          </h3>
                        </div>
                        <p className="text-sm text-gray-500 mt-2 ml-9">
                          Created: {new Date(resource.created_at).toLocaleDateString()}
                        </p>
                        {resource.file_description && (
                          <p className="text-sm text-gray-600 mt-2 ml-9 line-clamp-2">
                            {resource.file_description}
                          </p>
                        )}
                      </div>
                      {resource.attachments?.length > 0 && (
                        <div className="flex flex-col space-y-2">
                          {resource.attachments.map((attachment, index) => (
                            <Button
                              key={index}
                              onClick={() => handleDownload(attachment, resource.file_names?.[index] || `File ${index + 1}`)}
                              className="flex items-center space-x-2 bg-gray-100 text-gray-700 hover:bg-gray-200 text-sm px-3 py-1.5 rounded-md transition-all duration-300"
                              aria-label={`Download ${resource.file_names?.[index] || `File ${index + 1}`}`}
                            >
                              <Download className="w-4 h-4" />
                              <span>{resource.file_names?.[index] || `File ${index + 1}`}</span>
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}