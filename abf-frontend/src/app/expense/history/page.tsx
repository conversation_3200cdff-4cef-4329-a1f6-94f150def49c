"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Download } from "lucide-react";
import * as XLSX from 'xlsx';
import * as granteeExpenseService from "@/services/grantee-expense-service";

interface ExpenseRow {
  id?: number;
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  expense_date?: string;
  source_type?: string; // 'excel' or 'manual' or 'grantmaker_excel'
  is_frozen?: boolean;
  remarks?: string;
}

export default function ExpenseHistoryPage() {
  const [expenses, setExpenses] = useState<ExpenseRow[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const isActualExceedingBudget = (expense: ExpenseRow) => {
    const budgetSum = typeof expense.total_budget === 'number'
      ? expense.total_budget
      : Object.values(expense.budget_quarterly).reduce((acc, val) => acc + val, 0);

    const actualsSum = typeof expense.total_actual === 'number'
      ? expense.total_actual
      : Object.values(expense.actuals_quarterly).reduce((acc, val) => acc + val, 0);

    return actualsSum > budgetSum;
  };

  useEffect(() => {
    fetchExpenses();
  }, []);

  const fetchExpenses = async () => {
    setLoading(true);
    setError(null);

    try {
      // Use the grantee expense service to fetch expenses
      const expenseHistory = await granteeExpenseService.getExpenseHistory();

      // Transform the API data to match our ExpenseRow interface
      const transformedData = expenseHistory.map((item, index) => {
        console.log('Processing expense item:', item);
        return {
          id: parseInt(item.id.toString()),
          sr_no: index + 1,
          particulars: item.particulars || item.description || '',
          main_header: item.main_headers || item.category || '',
          sub_headers: item.sub_headers || '',
          units: item.units || '',
          frequency: item.frequency || '',
          cost_per_unit: item.cost_per_unit || 0,
          budget_quarterly: {
            Q1: item.budget_q1 || item.budget_quarterly?.Q1 || 0,
            Q2: item.budget_q2 || item.budget_quarterly?.Q2 || 0,
            Q3: item.budget_q3 || item.budget_quarterly?.Q3 || 0,
            Q4: item.budget_q4 || item.budget_quarterly?.Q4 || 0
          },
          actuals_quarterly: {
            Q1: item.actual_q1 || item.actuals_quarterly?.Q1 || 0,
            Q2: item.actual_q2 || item.actuals_quarterly?.Q2 || 0,
            Q3: item.actual_q3 || item.actuals_quarterly?.Q3 || 0,
            Q4: item.actual_q4 || item.actuals_quarterly?.Q4 || 0
          },
          total_budget: item.total_budget || item.totalBudget || 0,
          total_actual: item.total_actual || item.totalActualSpent || 0,
          expense_date: item.expense_date || item.loggedDate,
          source_type: item.source_type || 'manual', // Default to manual if not specified
          is_frozen: item.is_frozen || false,
          remarks: item.remarks || '',
          attachment: item.receipt || item.attachment || (item.source_type === 'excel' ? 'Excel Upload' : 'Manual Entry'),
          receipt: item.receipt || null
        };
      });

      console.log('Transformed expense history data:', transformedData);

      setExpenses(transformedData);
    } catch (err) {
      console.error('Error fetching expenses:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast.error('Failed to load expense data. Please try again later.');
      setExpenses([]);
    } finally {
      setLoading(false);
    }
  };

  // Removed mock data function as we're only using real API data

  const viewExpenseDetails = (expenseId: number) => {
    router.push(`/funding/expenses/${expenseId}`);
  };

  // Export function to generate Excel file
  const handleExport = () => {
    if (!expenses || expenses.length === 0) {
      toast.error('No expense data to export');
      return;
    }

    try {
      // Prepare data for export
      const exportData = expenses.map((expense, index) => ({
        'Sr. No.': index + 1,
        'ID': expense.id,
        'Date': expense.expense_date || 'N/A',
        'Particulars': expense.particulars || 'N/A',
        'Main Header': expense.main_header || 'N/A',
        'Sub Headers': expense.sub_headers || 'N/A',
        'Units': expense.units || 'N/A',
        'Frequency': expense.frequency || 'N/A',
        'Cost per Unit': expense.cost_per_unit || 0,
        'Budget Q1': expense.budget_quarterly?.Q1 || 0,
        'Budget Q2': expense.budget_quarterly?.Q2 || 0,
        'Budget Q3': expense.budget_quarterly?.Q3 || 0,
        'Budget Q4': expense.budget_quarterly?.Q4 || 0,
        'Total Budget': typeof expense.total_budget === 'number'
          ? expense.total_budget
          : Object.values(expense.budget_quarterly || {}).reduce((acc, val) => acc + val, 0),
        'Actual Q1': expense.actuals_quarterly?.Q1 || 0,
        'Actual Q2': expense.actuals_quarterly?.Q2 || 0,
        'Actual Q3': expense.actuals_quarterly?.Q3 || 0,
        'Actual Q4': expense.actuals_quarterly?.Q4 || 0,
        'Total Actual': typeof expense.total_actual === 'number'
          ? expense.total_actual
          : Object.values(expense.actuals_quarterly || {}).reduce((acc, val) => acc + val, 0),
        'Remarks': expense.remarks || 'N/A',
        'Source Type': expense.source_type === 'excel' ? 'Excel' :
                      expense.source_type === 'grantmaker_excel' ? 'Grantmaker' : 'Manual',
        'Status': expense.is_frozen ? 'Frozen' : 'Active'
      }));

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // Set column widths for better readability
      const colWidths = [
        { wch: 8 },   // Sr. No.
        { wch: 10 },  // ID
        { wch: 12 },  // Date
        { wch: 25 },  // Particulars
        { wch: 20 },  // Main Header
        { wch: 20 },  // Sub Headers
        { wch: 10 },  // Units
        { wch: 12 },  // Frequency
        { wch: 15 },  // Cost per Unit
        { wch: 12 },  // Budget Q1
        { wch: 12 },  // Budget Q2
        { wch: 12 },  // Budget Q3
        { wch: 12 },  // Budget Q4
        { wch: 15 },  // Total Budget
        { wch: 12 },  // Actual Q1
        { wch: 12 },  // Actual Q2
        { wch: 12 },  // Actual Q3
        { wch: 12 },  // Actual Q4
        { wch: 15 },  // Total Actual
        { wch: 30 },  // Remarks
        { wch: 15 },  // Source Type
        { wch: 10 }   // Status
      ];
      ws['!cols'] = colWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, 'Expense History');

      // Generate filename with current date
      const currentDate = new Date().toISOString().split('T')[0];
      const filename = `Expense_History_${currentDate}.xlsx`;

      // Save the file
      XLSX.writeFile(wb, filename);

      toast.success('Expense history exported successfully!');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export expense history');
    }
  };

  return (
    <div className="min-h-screen p-8 bg-gradient-to-br from-gray-50 to-gray-100">
      <Card className="max-w-7xl mx-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-2xl font-bold">Expense History</CardTitle>
          <div className="flex gap-2">
            <Button
              onClick={handleExport}
              variant="outline"
              className="flex items-center gap-1 border-teal-200 text-teal-700 hover:bg-teal-50 hover:text-teal-800"
              disabled={!expenses || expenses.length === 0}
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button
              onClick={() => router.push('/expense')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Add New Expense
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 p-4">
              <p>{error}</p>
              <Button
                onClick={fetchExpenses}
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
              >
                Retry
              </Button>
            </div>
          ) : expenses.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-gray-500 mb-4">No expense data found</p>
              <Button
                onClick={() => router.push('/expense')}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Add Your First Expense
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <table className="w-full border-collapse bg-white">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="p-3 border text-left font-semibold">ID</th>
                      <th className="p-3 border text-left font-semibold">Particulars</th>
                      <th className="p-3 border text-left font-semibold">Main Header</th>
                      <th className="p-3 border text-left font-semibold">Sub-Headers</th>
                      <th className="p-3 border text-center font-semibold">Total Budget</th>
                      <th className="p-3 border text-center font-semibold">Total Actual</th>
                      <th className="p-3 border text-center font-semibold">Remarks</th>
                      <th className="p-3 border text-center font-semibold">Source</th>
                      <th className="p-3 border text-center font-semibold">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {expenses.map((expense) => (
                      <tr key={expense.id} className={`hover:bg-gray-50 ${expense.is_frozen ? 'bg-gray-50' : ''}`}>
                        <td className="p-3 border">{expense.id}</td>
                        <td className="p-3 border">{expense.particulars}</td>
                        <td className="p-3 border">{expense.main_header}</td>
                        <td className="p-3 border">{expense.sub_headers}</td>
                        <td className="p-3 border text-right">
                          {typeof expense.total_budget === 'number'
                            ? expense.total_budget.toFixed(2)
                            : Object.values(expense.budget_quarterly).reduce((acc, val) => acc + val, 0).toFixed(2)}
                        </td>
                        <td className="p-3 border text-right">
                          {typeof expense.total_actual === 'number'
                            ? expense.total_actual.toFixed(2)
                            : Object.values(expense.actuals_quarterly).reduce((acc, val) => acc + val, 0).toFixed(2)}
                        </td>
                        <td className="p-3 border">
                          {expense.remarks && (
                            <div className="text-sm">
                              {expense.remarks}
                              {isActualExceedingBudget(expense) && (
                                <div className="mt-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                  Actual > Budget
                                </div>
                              )}
                            </div>
                          )}
                        </td>
                        <td className="p-3 border text-center">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            expense.source_type === 'excel'
                              ? 'bg-green-100 text-green-800'
                              : expense.source_type === 'grantmaker_excel'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-gray-100 text-gray-800'
                          }`}>
                            {expense.source_type === 'excel'
                              ? 'Excel'
                              : expense.source_type === 'grantmaker_excel'
                                ? 'Grantmaker'
                                : 'Manual'}
                          </span>
                          {expense.is_frozen && (
                            <div className="mt-1 text-xs text-gray-500">(Frozen)</div>
                          )}
                        </td>
                        <td className="p-3 border text-center">
                          <Button
                            onClick={() => viewExpenseDetails(expense.id || 0)}
                            className="bg-blue-600 hover:bg-blue-700 text-white text-sm"
                            size="sm"
                          >
                            View Details
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}