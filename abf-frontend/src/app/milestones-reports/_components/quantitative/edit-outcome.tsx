"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  OutcomeSchema,
  OutputSchema,
  ActivitySchema,
  OutcomeType,
  OutcomeTypeWithImpact,
} from "@/types/quantitative";
import { zodResolver } from "@hookform/resolvers/zod";
import { Control, useFieldArray, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { SaveIcon } from "lucide-react";
import React from "react";
import {
  actualKeys,
  plannedKeys,
  quarterKey<PERSON>airs,
  remarkKeys,
} from "@/app/grantmaker/reports/_lib/quantitative";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import {
  getQuarterDateRangeAsDates,
  getQuarterDateRange,
} from "@/utils/quarterUtils";
import { addDays } from "date-fns";

const BUFFER_DAYS = 20;

export function EditOutcome({ outcome }: { outcome: OutcomeTypeWithImpact }) {
  const newActivitiesSchema = ActivitySchema.omit({
    id: true,
  }).superRefine((data, ctx) => {
    quarterKeyPairs.forEach(({ plan, actual, remark, quarter }) => {
      const quarterRange = getQuarterDateRangeAsDates(
        quarter,
        outcome.impact.year.toString(),
      );
      const today = new Date();

      if (!data || !data[plan] || !quarterRange) return;

      if (
        (!data[actual] || data[actual] < data[plan]) &&
        (!data[remark] || data[remark].trim() === "") &&
        !(
          quarterRange.start >= today &&
          today <= addDays(quarterRange.end, BUFFER_DAYS)
        )
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: [remark],
          message: `Why was the target missed?`,
        });
      }
    });
  });

  const newOutputSchema = OutputSchema.omit({
    id: true,
    description: true,
    unit: true,
    activities: true,
  })
    .extend({
      activities: z.array(newActivitiesSchema),
    })
    .superRefine((data, ctx) => {
      quarterKeyPairs.forEach(({ plan, actual, remark, quarter }) => {
        const quarterRange = getQuarterDateRangeAsDates(
          quarter,
          outcome.impact.year.toString(),
        );

        const today = new Date();

        if (!data || !data[plan] || !quarterRange) return;

        if (
          (!data[actual] || data[actual] < data[plan]) &&
          (!data[remark] || data[remark].trim() === "") &&
          !(
            quarterRange.start >= today &&
            today <= addDays(quarterRange.end, BUFFER_DAYS)
          )
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: [remark],
            message: `Why was the target missed?`,
          });
        }
      });
    });

  const newOutcomeSchema = OutcomeSchema.omit({
    id: true,
    outcome_statement: true,
  }).extend({
    outputs: z.array(newOutputSchema),
  });

  type NewOutcomeType = z.infer<typeof newOutcomeSchema>;

  const form = useForm<NewOutcomeType>({
    defaultValues: {
      ...outcome,
      outputs: outcome.outputs.map((e) => {
        plannedKeys.forEach((key) => (e[key] = e[key] ?? 0));
        actualKeys.forEach((key) => (e[key] = e[key] ?? 0));
        remarkKeys.forEach((key) => (e[key] = e[key] || ""));

        e.activities.forEach((a) => {
          plannedKeys.forEach((key) => (a[key] = a[key] ?? 0));
          actualKeys.forEach((key) => (a[key] = a[key] ?? 0));
          remarkKeys.forEach((key) => (a[key] = a[key] || ""));
        });

        return e;
      }),
    },
    resolver: zodResolver(newOutcomeSchema),
  });

  const outputsArray = useFieldArray({
    control: form.control,
    name: "outputs",
  });

  const mutation = useMutation({
    mutationFn: async (values: NewOutcomeType) => {
      console.log("Inside mutation:", values);

      const response = await apiClient.patch(
        `/api/reports/quantitative/outcomes/${outcome.id}/`,
        values,
      );

      console.log("Outcome:", response.data);

      if (!response) {
        throw new Error("Failed to update outcome");
      }

      return response;
    },
    onSuccess: () => {
      toast.success("Outcome updated successfully");
    },
    onError: () => {
      toast.error("Failed to update outcome");
    },
  });

  const onSubmit = (values: NewOutcomeType) => {
    mutation.mutate(values);
    return;
  };

  const TotalDisplay: React.FC<{
    control: Control<any>; // You can make this more specific if needed
    name: string;
    keys: string[];
    className?: string;
  }> = ({ control, name, keys, className = "" }) => {
    const watchedValues = useWatch({
      control,
      name: name,
    });

    const total = React.useMemo(() => {
      if (!watchedValues) return 0;

      return keys.reduce((sum, key) => {
        const value = parseFloat(watchedValues[key]) || 0;
        return sum + value;
      }, 0);
    }, [watchedValues, keys]);

    return total;
  };

  function UnmodifiableField({
    className,
    containerClassName,
    label,
    children,
  }: {
    className?: string;
    containerClassName?: string;
    label: React.ReactNode;
    children?: React.ReactNode;
  }) {
    return (
      <div className={cn("grid gap-1", containerClassName)}>
        <Label>{label}</Label>
        <div className={cn("text-xl font-bold tracking-tight", className)}>
          {children}
        </div>
      </div>
    );
  }

  function ActivityForm({ outputIndex }: { outputIndex: number }) {
    const activityArray = useFieldArray({
      control: form.control,
      name: `outputs.${outputIndex}.activities`,
    });

    return (
      <div className="grid">
        <h3 className="text-2xl pt-6 ml-10 font-semibold tracking-tight mb-3">
          Activities
        </h3>

        {activityArray.fields.map((e, i) => {
          const activity = outcome.outputs[outputIndex].activities[i];

          if (!activity) return null;

          return (
            <div key={i}>
              <div className="flex gap-2 items-center relative">
                <div className="absolute size-8 bg-muted border rounded-2xl grid place-items-center">
                  {i + 1}
                </div>
                <Separator />
              </div>

              <div className="space-y-3 border-l ml-4 px-4 py-4">
                <div className="grid grid-cols-[1fr_auto] gap-2">
                  <UnmodifiableField label={"Description"}>
                    {activity.description}
                  </UnmodifiableField>

                  {activity.unit && (
                    <div className="grid gap-1">
                      <Label>Unit</Label>
                      <div className="text-xl font-bold tracking-tight">
                        {activity.unit}
                      </div>
                    </div>
                  )}
                </div>

                <Table>
                  <TableHeader>
                    <TableRow className="hover:bg-transparent border-transparent">
                      <TableHead className="p-0 h-fit"></TableHead>
                      {quarterKeyPairs.map((q) => (
                        <TableHead
                          key={q.quarter}
                          className="text-center p-0 h-fit text-muted-foreground"
                        >
                          {q.quarter} (
                          {getQuarterDateRange(
                            q.quarter,
                            outcome.impact.year.toString(),
                          )}
                          )
                        </TableHead>
                      ))}
                      <TableHead>Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow className="hover:bg-transparent">
                      <TableCell className="h-fit min-w-[100px]">
                        Planned
                      </TableCell>
                      {plannedKeys.map((e) => (
                        <TableCell
                          key={e}
                          className="py-1 text-center font-bold"
                        >
                          {activity[e] ?? "-"}
                        </TableCell>
                      ))}
                      <TableCell className="font-semibold text-center">
                        <TotalDisplay
                          name={`outputs.${outputIndex}.activities.${i}`}
                          control={form.control}
                          keys={plannedKeys}
                        />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="h-fit min-w-[100px]">
                        Actual
                      </TableCell>
                      {quarterKeyPairs.map((e) => {
                        const today = new Date();
                        const quarterRange = getQuarterDateRangeAsDates(
                          e.quarter,
                          outcome.impact.year.toString(),
                        );

                        if (!quarterRange) {
                          return (
                            <TableCell key={e.actual}>
                              Quarter Range couldn't be found
                            </TableCell>
                          );
                        }

                        return (
                          <TableCell key={e.actual} className="py-1">
                            <FormField
                              control={form.control}
                              name={`outputs.${outputIndex}.activities.${i}.${e.actual}`}
                              render={({ field }) => (
                                <FormItem className="grid place-items-center">
                                  <FormControl>
                                    <Input
                                      className="text-center"
                                      {...field}
                                      value={field.value ?? ""}
                                      disabled={
                                        quarterRange.start >= today &&
                                        today <=
                                          addDays(quarterRange.end, BUFFER_DAYS)
                                      }
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </TableCell>
                        );
                      })}
                      <TableCell className="font-semibold text-center">
                        <TotalDisplay
                          name={`outputs.${outputIndex}.activities.${i}`}
                          control={form.control}
                          keys={actualKeys}
                        />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Remarks</TableCell>
                      {quarterKeyPairs.map((e) => {
                        const today = new Date();
                        const quarterRange = getQuarterDateRangeAsDates(
                          e.quarter,
                          outcome.impact.year.toString(),
                        );

                        if (!quarterRange) {
                          return (
                            <TableCell key={e.remark}>
                              Quarter Range couldn't be found
                            </TableCell>
                          );
                        }

                        return (
                          <TableCell key={e.remark} className="py-1">
                            <FormField
                              control={form.control}
                              name={`outputs.${outputIndex}.activities.${i}.${e.remark}`}
                              render={({ field }) => (
                                <FormItem className="grid place-items-center">
                                  <FormControl>
                                    <Input
                                      className="text-center"
                                      {...field}
                                      value={field.value ?? ""}
                                      disabled={
                                        quarterRange.start >= today &&
                                        today <=
                                          addDays(quarterRange.end, BUFFER_DAYS)
                                      }
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="py-2 grid gap-4">
            <UnmodifiableField label={"Outcome"} className="text-3xl">
              {outcome.outcome_statement}
            </UnmodifiableField>

            <FormField
              control={form.control}
              name="pre_intervention_assessment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pre Intervention Assesment</FormLabel>
                  <FormControl>
                    <Textarea {...field} value={field.value ?? ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="post_intervention_assessment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Post Intervention Assement</FormLabel>
                  <FormControl>
                    <Textarea {...field} value={field.value ?? ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tools_used"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tools Used</FormLabel>
                  <FormControl>
                    <Textarea {...field} value={field.value ?? ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-6 pt-2">
              <h2 className="border-b mb-6 pb-2 text-3xl font-semibold  tracking-tight mt-6">
                Outputs
              </h2>
              {outputsArray.fields.map((e, i) => {
                const output = outcome.outputs[i];

                if (!output) return null;

                return (
                  <Card key={i}>
                    <CardHeader>
                      <CardTitle>
                        <Badge>Output {i + 1}</Badge>
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-[1fr_auto] gap-2">
                        <FormField
                          name={`outputs.${i}.description`}
                          render={({ field }) => (
                            <FormItem className="grid gap-0">
                              <FormLabel>Statement</FormLabel>

                              <FormControl>
                                <div className="text-xl font-bold tracking-tight">
                                  {field.value}
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <UnmodifiableField label="Unit">
                          {outcome.outputs[i].unit}
                        </UnmodifiableField>
                      </div>

                      <Table className="rounded-xl overflow-hidden">
                        <TableHeader>
                          <TableRow className="border-transparent hover:bg-transparent">
                            <TableHead className="h-fit p-0"></TableHead>
                            {quarterKeyPairs.map((q) => (
                              <TableHead
                                key={q.quarter}
                                className="text-center h-fit text-muted-foreground"
                              >
                                {q.quarter} (
                                {getQuarterDateRange(
                                  q.quarter,
                                  outcome.impact.year.toString(),
                                )}
                                )
                              </TableHead>
                            ))}
                            <TableHead>Total</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow className="hover:bg-transparent">
                            <TableCell className="h-fit min-w-[100px]">
                              Planned
                            </TableCell>
                            {plannedKeys.map((e) => (
                              <TableCell
                                key={e}
                                className="py-1 text-center font-bold"
                              >
                                {output[e] ?? "-"}
                              </TableCell>
                            ))}
                            <TableCell className="font-semibold text-center">
                              <TotalDisplay
                                name={`outputs.${i}.${e}`}
                                control={form.control}
                                keys={plannedKeys}
                              />
                            </TableCell>
                          </TableRow>

                          <TableRow>
                            <TableCell className="h-fit min-w-[100px]">
                              Actual
                            </TableCell>
                            {quarterKeyPairs.map((e) => {
                              const today = new Date();
                              const quarterRange = getQuarterDateRangeAsDates(
                                e.quarter,
                                outcome.impact.year.toString(),
                              );

                              if (!quarterRange)
                                return (
                                  <TableCell>
                                    Quarter Range couldn't be found
                                  </TableCell>
                                );

                              return (
                                <TableCell key={e.actual} className="py-1">
                                  <FormField
                                    control={form.control}
                                    name={`outputs.${i}.${e.actual}`}
                                    render={({ field }) => (
                                      <FormItem className="grid place-items-center">
                                        <FormControl>
                                          <Input
                                            className="text-center"
                                            {...field}
                                            value={field.value ?? ""}
                                            disabled={
                                              quarterRange.start >= today &&
                                              today <=
                                                addDays(
                                                  quarterRange.end,
                                                  BUFFER_DAYS,
                                                )
                                            } // BUFFER_DAYS days buffer
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                </TableCell>
                              );
                            })}
                            <TableCell className="font-semibold text-center">
                              <TotalDisplay
                                name={`outputs.${i}.${e}`}
                                control={form.control}
                                keys={actualKeys}
                              />
                            </TableCell>
                          </TableRow>

                          <TableRow>
                            <TableCell>Remarks</TableCell>
                            {quarterKeyPairs.map((e) => {
                              const today = new Date();
                              const quarterRange = getQuarterDateRangeAsDates(
                                e.quarter,
                                outcome.impact.year.toString(),
                              );

                              if (!quarterRange)
                                return (
                                  <TableCell key={e.remark}>
                                    Quarter Range couldn't be found
                                  </TableCell>
                                );

                              return (
                                <TableCell key={e.remark} className="py-1">
                                  <FormField
                                    control={form.control}
                                    name={`outputs.${i}.${e.remark}`}
                                    render={({ field }) => (
                                      <FormItem className="grid place-items-center">
                                        <FormControl>
                                          <Input
                                            className="text-center"
                                            {...field}
                                            value={field.value ?? ""}
                                            disabled={
                                              quarterRange.start >= today &&
                                              today <=
                                                addDays(
                                                  quarterRange.end,
                                                  BUFFER_DAYS,
                                                )
                                            } // BUFFER_DAYS days buffer
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                </TableCell>
                              );
                            })}
                          </TableRow>
                        </TableBody>
                      </Table>

                      <Separator />

                      <ActivityForm outputIndex={i} />
                    </CardContent>
                  </Card>
                );
              })}
            </div>
            <Separator />
            <Button size="lg" className="bg-teal-500 place-self-end">
              <SaveIcon />
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
