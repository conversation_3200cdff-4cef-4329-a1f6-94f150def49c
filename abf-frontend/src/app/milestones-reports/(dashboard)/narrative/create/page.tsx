"use client";

import { motion } from "framer-motion";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { toast } from "sonner";
import React from "react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  CalendarIcon,
  CalendarRange,
  FileTextIcon,
  IndianRupeeIcon,
  InfoIcon,
} from "lucide-react";

import {
  NarrativeReportGrantSchema,
  NarrativeReportGrantType,
  ReportGrantSelector,
} from "../../../_components/grant-selector";
import apiClient from "@/lib/apiClient";
import { formatCurrency } from "@/lib/utils";

// Constants
const QUESTIONS = [
  {
    label:
      "What were your key objectives for this quarter, and which strategies did you implement to pursue them?",
    description:
      "Please cite specific activities, timelines, and the outcomes you aimed to achieve.",
    defaultAnswer:
      "Our key objective this quarter was to significantly expand outreach in rural districts, targeting underserved communities. To achieve this, we initiated a comprehensive mobile awareness campaign. We deployed a fleet of specially equipped vans, loaded with educational materials and interactive displays, which successfully visited 35 distinct villages over an intensive eight-week period. This direct engagement strategy allowed us to reach a broad demographic, providing valuable information and fostering direct interaction with residents.\n\nBeyond the mobile campaign, we strengthened our on-the-ground presence by onboarding three new, dedicated field coordinators. Their primary role is to cultivate stronger local relationships and facilitate sustained community engagement. These coordinators are crucial for ensuring the long-term impact of our outreach efforts. The primary outcome we aimed for was a 20% increase in community participation across all our programs within these rural areas. This ambitious target will serve as our key metric for evaluating the overall success and effectiveness of our multifaceted outreach initiatives. We are currently compiling data to assess the preliminary impact and identify areas for further optimization in our ongoing commitment to rural development.",
    min: 600,
    max: 1800,
  },
  {
    label:
      "Which measurable results did you record this quarter, and what qualitative shifts accompanied those numbers?",
    description:
      "Include data points alongside brief anecdotes or observations that bring the figures to life.",
    defaultAnswer:
      "We recorded a **26% increase in attendance** at local training sessions compared to the previous quarter, a significant jump. Importantly, **74% of these attendees were first-time participants**, indicating successful outreach to new individuals within the communities. Qualitatively, we observed encouraging shifts: there was noticeably **more active involvement from women in village councils**, signifying a positive step towards gender inclusivity. Furthermore, a remarkable change in perspective was evident as **local leaders displayed a more inclusive attitude** during post-event discussions, suggesting a deeper impact of our initiatives on community leadership.",
    min: 600,
    max: 1800,
  },
  {
    label:
      "Can you highlight 2 case studies that illustrate a significant success or challenge?",
    description:
      "Ensure informed consent for any images and describe the context, participants, and lessons learned.",
    defaultAnswer:
      "One compelling **case study** involves **Shanti**, a dedicated village leader who, after participating in our programs, successfully mobilized **40 families** to join our nutrition awareness program. This direct action led to a significant **15% increase in regular child health checkups** within her community, demonstrating a tangible improvement in child welfare. Another impactful case centers on **Ravi**, a resourceful youth who leveraged the skills gained from our digital literacy training to **secure a remote job**, showcasing the immediate economic empowerment our initiatives can provide. These individual success stories collectively highlight the broader, transformative impact of our work on both community health and economic opportunity.",
    min: 600,
    max: 1800,
  },
  {
    label:
      "What partnerships or collaborations were most instrumental in advancing your work?",
    description:
      "Mention any new alliances or shifts in existing relationships and the tangible benefits they brought.",
    defaultAnswer:
      "Our partnership with the **District Health Department** proved instrumental this quarter, providing us with access to critical health data and enabling coordinated outreach efforts. This collaboration resulted in a **30% improvement in vaccination coverage** across our target villages. Additionally, our new alliance with **Local Women's Self-Help Groups** brought tremendous value, as these groups became key advocates for our programs within their communities. The partnership with **Rural Technology Centers** enabled us to establish digital literacy hubs in three villages, directly benefiting over 200 individuals. These strategic collaborations not only amplified our reach but also ensured sustainable, community-driven implementation of our initiatives.",
    min: 600,
    max: 1800,
  },
  {
    label:
      "What challenges or unexpected obstacles did you face, and what key lessons will inform your planning?",
    description:
      "Reflect on any strategic pivots, team capacity-building, or external factors that influenced your decisions.",
    defaultAnswer:
      "We encountered several challenges, notably unpredictable weather patterns that occasionally disrupted our mobile campaign schedule, requiring last-minute adjustments. Initial community skepticism in some villages also proved an unexpected hurdle, necessitating a strategic pivot in our engagement approach. We quickly learned the importance of immediate, localized trust-building, leading us to empower our new field coordinators with greater autonomy to tailor their interactions and messaging on the spot. This also highlighted a need for more intensive, culturally-sensitive capacity-building for our team. External factors, such as local harvest seasons, sometimes impacted attendance at our sessions, teaching us the critical lesson of conducting more thorough pre-campaign demographic and activity mapping. Moving forward, these experiences underscore the need for enhanced logistical flexibility, deeper preliminary community assessments, and continuous, adaptive training for our field teams to better navigate unforeseen obstacles and maximize our outreach effectiveness.",
    min: 600,
    max: 1800,
  },
];

type Question = (typeof QUESTIONS)[0];

// Utility Functions
function getCurrentQuarterAndYear() {
  const now = new Date();
  const currentMonth = now.getMonth() + 1;
  const currentYear = now.getFullYear();

  let currentQuarter;
  if (currentMonth >= 4 && currentMonth <= 6) {
    currentQuarter = 1;
  } else if (currentMonth >= 7 && currentMonth <= 9) {
    currentQuarter = 2;
  } else if (currentMonth >= 10 && currentMonth <= 12) {
    currentQuarter = 3;
  } else {
    currentQuarter = 4;
  }

  return { quarter: currentQuarter, year: currentYear };
}

function getQuarterDateRange(quarter: number, year: number) {
  const quarters = {
    1: `Apr 1 - Jun 30, ${year}`,
    2: `Jul 1 - Sep 30, ${year}`,
    3: `Oct 1 - Dec 31, ${year}`,
    4: `Jan 1 - Mar 31, ${year}`,
  };
  return quarters[quarter as keyof typeof quarters];
}

// Form Schema
const createFormSchema = () =>
  z.object({
    grant: z.number({ required_error: "Please select a grant" }),
    quarter: z.number(),
    year: z.number(),
    questions: z.array(
      z.object({
        question: z.string(),
        answer: z
          .string()
          .min(600, "Answer must be at least 600 characters")
          .max(1800, "Answer must not exceed 1800 characters"),
      }),
    ),
  });

type FormData = z.infer<ReturnType<typeof createFormSchema>>;

// API Functions
async function fetchGrants() {
  const response = await apiClient.get("/api/v1/me/grants");
  return z.array(NarrativeReportGrantSchema).parse(response.data);
}

async function fetchExistingReports(quarter: number, year: number) {
  try {
    const response = await apiClient.get("/api/reports/narrative/", {
      params: { quarter, year },
    });
    return response.data;
  } catch {
    return [];
  }
}

async function createNarrativeReport(data: FormData) {
  try {
    const response = await apiClient.post("/api/reports/narrative/", {
      grant_id: data.grant,
      quarter: data.quarter,
      year: data.year,
      questions: data.questions,
    });
    return response.data;
  } catch (error: any) {
    const errorData = error.response?.data;
    if (errorData?.non_field_errors?.[0]) {
      throw new Error(errorData.non_field_errors[0]);
    }
    const firstError = errorData ? Object.values(errorData)[0] : null;
    throw new Error(
      Array.isArray(firstError) ? firstError[0] : "Failed to submit report",
    );
  }
}

// Components
function LoadingState() {
  return (
    <div className="max-w-4xl py-8 mx-auto px-4">
      <div className="mb-8 mx-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Narrative Report
        </h1>
        <p className="text-gray-600">
          Complete your grant narrative report with detailed activities and
          outcomes.
        </p>
      </div>
      <Card>
        <CardContent className="p-6">
          <Skeleton className="h-6 w-full mb-4" />
          <Skeleton className="h-10 w-full" />
        </CardContent>
      </Card>
    </div>
  );
}

function NoGrantsAvailable({
  currentQuarter,
  currentYear,
}: {
  currentQuarter: number;
  currentYear: number;
}) {
  return (
    <div className="max-w-4xl py-8 mx-auto px-4">
      <div className="mb-8 mx-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Narrative Report
        </h1>
        <p className="text-gray-600">
          Complete your grant narrative report with detailed activities and
          outcomes.
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <InfoIcon className="h-5 w-5 text-amber-500" />
            No Available Grants
          </CardTitle>
          <CardDescription>
            All grants already have narrative reports submitted for Q
            {currentQuarter} {currentYear}. You can only submit one report per
            quarter.
          </CardDescription>
        </CardHeader>
      </Card>
    </div>
  );
}

function GrantInfoCard({ grant }: { grant: NarrativeReportGrantType }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <div>
        <h3 className="font-medium text-sm text-muted-foreground mb-1">
          Purpose
        </h3>
        <p className="text-sm">{grant.grant_purpose}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="font-medium text-sm text-muted-foreground mb-1">
            Grant Period
          </h3>
          <div className="flex items-center gap-2">
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              {format(new Date(grant.start_date), "PPP")} to{" "}
              {format(new Date(grant.end_date), "PPP")}
            </span>
          </div>
        </div>

        <div>
          <h3 className="font-medium text-sm text-muted-foreground mb-1">
            Annual Budget
          </h3>
          <div className="flex items-center gap-2">
            <IndianRupeeIcon className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              {formatCurrency(grant.annual_budget)}
            </span>
          </div>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-sm text-muted-foreground mb-1">
          Funding Sources
        </h3>
        <p className="text-sm">{grant.funding_sources}</p>
      </div>
    </motion.div>
  );
}

function QuestionCard({
  question,
  index,
  field,
}: {
  question: Question;
  index: number;
  field: any;
}) {
  const charCount = field.value?.length || 0;

  const getCharCountColor = () => {
    if (charCount < question.min) return "text-red-500";
    if (charCount > question.max) return "text-red-500";
    return "text-green-600";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.05 * index }}
    >
      <Card>
        <CardHeader>
          <CardTitle>
            <FormLabel className="text-lg m-0">{question.label}</FormLabel>
          </CardTitle>
          <CardDescription>
            <FormDescription className="m-0">
              {question.description}
            </FormDescription>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FormItem>
            <FormControl>
              <Textarea
                {...field}
                rows={6}
                placeholder="Enter your detailed response here..."
              />
            </FormControl>
            <FormMessage />
            <div className="flex justify-between items-center text-sm mt-2">
              <span className="text-gray-500">
                {question.min} - {question.max} characters required
              </span>
              <span className={`font-medium ${getCharCountColor()}`}>
                {charCount} / {question.max}
              </span>
            </div>
          </FormItem>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Main Component
export default function CreateNarrativeReport() {
  const router = useRouter();
  const { quarter: currentQuarter, year: currentYear } =
    getCurrentQuarterAndYear();

  // Fetch grants
  const grantsQuery = useQuery({
    queryKey: ["grants"],
    queryFn: fetchGrants,
  });

  // Fetch existing reports
  const existingReportsQuery = useQuery({
    queryKey: ["narrative-reports", currentQuarter, currentYear],
    queryFn: () => fetchExistingReports(currentQuarter, currentYear),
    enabled: grantsQuery.isSuccess,
  });

  // Filter available grants
  const availableGrants =
    grantsQuery.data?.filter((grant) => {
      const existingReports = existingReportsQuery.data;
      if (!existingReports) return true;

      const reportsArray = Array.isArray(existingReports)
        ? existingReports
        : existingReports.results || [];

      return !reportsArray.some((report: any) => {
        const grantId = report.grant?.id || report.grant_id;
        return (
          grantId === grant.id &&
          report.quarter === currentQuarter &&
          report.year === currentYear
        );
      });
    }) || [];

  const form = useForm<FormData>({
    resolver: zodResolver(createFormSchema()),
    defaultValues: {
      quarter: currentQuarter,
      year: currentYear,
      questions: QUESTIONS.map((q) => ({
        question: q.label,
        answer: process.env.NODE_ENV === "development" ? q.defaultAnswer : "",
      })),
    },
  });

  const selectedGrant = availableGrants.find(
    (g) => g.id === form.watch("grant"),
  );

  const watchedGrant = form.watch("grant");
  React.useEffect(() => {
    if (watchedGrant) {
      form.setValue(
        "questions",
        QUESTIONS.map((q) => ({
          question: q.label,
          answer: process.env.NODE_ENV === "development" ? q.defaultAnswer : "",
        })),
      );
    }
  }, [watchedGrant, form]);

  function handleSubmit(values: FormData) {
    const submitPromise = createNarrativeReport(values).then(() => {
      router.replace("/milestones-reports");
    });

    toast.promise(submitPromise, {
      loading: "Submitting Report...",
      success: "Your narrative report has been submitted successfully.",
      error: (err) => err.message || "Failed to submit report.",
      position: "top-center",
    });
  }

  // Loading state
  if (grantsQuery.isLoading || existingReportsQuery.isLoading) {
    return <LoadingState />;
  }

  // No grants available
  if (availableGrants.length === 0) {
    return (
      <NoGrantsAvailable
        currentQuarter={currentQuarter}
        currentYear={currentYear}
      />
    );
  }

  return (
    <div className="max-w-4xl py-8 mx-auto px-4">
      <div className="mb-8 mx-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Narrative Report
        </h1>
        <p className="text-gray-600">
          Complete your grant narrative report with detailed activities and
          outcomes.
        </p>
      </div>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="gap-10 flex flex-col"
        >
          {/* Grant Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileTextIcon className="h-5 w-5" />
                Grant Information
              </CardTitle>
              <CardDescription>
                Select the grant for this narrative report. Only grants without
                existing reports for this quarter are shown.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Quarter Info */}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <CalendarRange className="h-4 w-4 text-teal-500" />
                  <span className="text-sm text-gray-600">Financial Year</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="px-3 py-2 bg-teal-50 text-teal-700 rounded-md border border-teal-200 font-medium text-sm">
                    Q{currentQuarter}
                  </div>
                  <div className="px-3 py-2 bg-teal-50 text-teal-700 rounded-md border border-teal-200 font-medium text-sm">
                    {getQuarterDateRange(currentQuarter, currentYear)}
                  </div>
                </div>
              </div>

              {/* Grant Selector */}
              <FormField
                name="grant"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-600 font-normal">
                      Grant *
                    </FormLabel>
                    <ReportGrantSelector
                      selectedGrant={field.value}
                      setSelectedGrant={field.onChange}
                      grants={availableGrants}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Grant Info Card */}
              {selectedGrant && <GrantInfoCard grant={selectedGrant} />}
            </CardContent>
          </Card>

          {/* Questions */}
          {form.watch("grant") && (
            <>
              {QUESTIONS.map((question, index) => (
                <FormField
                  key={index}
                  control={form.control}
                  name={`questions.${index}.answer`}
                  render={({ field }) => (
                    <QuestionCard
                      question={question}
                      index={index}
                      field={field}
                    />
                  )}
                />
              ))}

              {/* Submit Button */}
              <Button
                type="submit"
                className="self-end bg-teal-500 hover:bg-teal-500/80 transition-all duration-300"
                size="lg"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting
                  ? "Submitting..."
                  : "Submit Report"}
              </Button>
            </>
          )}
        </form>
      </Form>
    </div>
  );
}
