"use client";

import { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { format } from "date-fns";
import { toast } from "sonner";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
    Calendar,
    FileSpreadsheet,
    IndianRupee,
    Info,
    Undo2,
    AlertTriangle,
    CheckCircle,
    Clock,
    XCircle,
    Edit,
    Save,
    RefreshCw,
} from "lucide-react";
import apiClient from "@/lib/apiClient";
import { formatCurrency } from "@/lib/utils";
import { getQuarterDateRange } from "@/components/funding/QuarterlyDateSelector";
import { motion } from "framer-motion";

// Types
interface Grant {
    id: number;
    grant_name: string;
    grant_purpose: string;
    start_date: string;
    end_date: string;
    annual_budget: number;
    funding_sources: string;
}

interface QuestionAnswer {
    id: number;
    question: string;
    answer: string;
}

interface NarrativeReport {
    id: number;
    status: "APPROVED" | "PENDING" | "REJECTED";
    quarter: number;
    year: number;
    created_at: string;
    rejection_remarks?: string;
    remarks?: string;
    grant: Grant;
    question_answers: QuestionAnswer[];
}

type ValidationErrors = Record<number, string | undefined>;
type AnswerMap = Record<number, string>;

// Constants
const VALIDATION_RULES = {
    MIN_LENGTH: 600,
    MAX_LENGTH: 1800,
} as const;

const STATUS_CONFIG = {
    APPROVED: {
        icon: CheckCircle,
        className: "bg-green-100 text-green-800 border-green-200",
    },
    PENDING: {
        icon: Clock,
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
    },
    REJECTED: {
        icon: XCircle,
        className: "bg-red-100 text-red-800 border-red-200",
    },
} as const;

// API Functions
const fetchReport = async (id: string): Promise<NarrativeReport> => {
    const response = await apiClient.get(`/api/reports/narrative/${id}/`);
    return response.data;
};

const saveReport = async (
    id: string,
    payload: any,
): Promise<NarrativeReport> => {
    const response = await apiClient.patch(
        `/api/reports/narrative/${id}/`,
        payload,
    );
    return response.data;
};

// Utility Functions
const validateAnswer = (answer: string): string | null => {
    const trimmed = answer.trim();

    if (!trimmed) return "Answer cannot be empty.";
    if (trimmed.length < VALIDATION_RULES.MIN_LENGTH) {
        return `Answer must be at least ${VALIDATION_RULES.MIN_LENGTH} characters long.`;
    }
    if (trimmed.length > VALIDATION_RULES.MAX_LENGTH) {
        return `Answer cannot be more than ${VALIDATION_RULES.MAX_LENGTH} characters long.`;
    }

    return null;
};

const createAnswerMap = (questionAnswers: QuestionAnswer[]): AnswerMap => {
    return questionAnswers.reduce((acc, qa) => {
        acc[qa.id] = qa.answer;
        return acc;
    }, {} as AnswerMap);
};

const hasChanges = (edited: AnswerMap, original: AnswerMap): boolean => {
    return Object.keys(edited).some((qaId) => edited[+qaId] !== original[+qaId]);
};

// Components
const StatusBadge = ({ status }: { status: NarrativeReport["status"] }) => {
    const config = STATUS_CONFIG[status];
    const Icon = config.icon;

    return (
        <div className="animate-fade-in-up delay-100">
            <Badge
                className={`text-sm mb-4 px-4 py-2 font-medium shadow-sm hover:shadow-md transition-all duration-300 ${config.className}`}
            >
                <Icon className="w-4 h-4 mr-2" />
                {status}
            </Badge>
        </div>
    );
};

const LoadingSkeleton = () => (
    <div className="container mx-auto py-0 px-4 max-w-5xl">
        <div className="animate-pulse space-y-6">
            <Skeleton className="h-10 w-24 mb-4" />
            <div className="space-y-4 mb-8">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-8 w-1/2" />
                <Skeleton className="h-4 w-1/3" />
            </div>
            <div className="space-y-6">
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-48 w-full" />
                <Skeleton className="h-64 w-full" />
            </div>
        </div>
    </div>
);

const RejectionCard = ({ remarks }: { remarks: string }) => (
    <div className="animate-fade-in-up delay-300">
        <Card className="border-red-200 bg-white shadow-lg hover:shadow-xl transition-all duration-500 group">
            <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-3 text-red-800 group-hover:text-red-900 transition-colors duration-300">
                    <div className="p-2 bg-red-100 rounded-lg group-hover:bg-red-200 transition-colors duration-300">
                        <AlertTriangle className="h-5 w-5" />
                    </div>
                    Rejection Remarks
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="p-4 bg-red-50 rounded-lg border border-red-100">
                    <p className="text-red-700 whitespace-pre-wrap leading-relaxed">
                        {remarks}
                    </p>
                </div>
            </CardContent>
        </Card>
    </div>
);

const GrantCard = ({ grant }: { grant: Grant }) => (
    <div className="animate-fade-in-up delay-400">
        <Card className="shadow-lg hover:shadow-xl transition-all duration-500 border bg-white group">
            <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-slate-800 group-hover:text-slate-900 transition-colors duration-300">
                    <div className="p-2 bg-teal-100 rounded-lg group-hover:bg-teal-200 transition-all duration-300 group-hover:scale-110">
                        <Info className="h-5 w-5 text-teal-600" />
                    </div>
                    Grant Details
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                <div>
                    <h3 className="font-semibold text-sm text-slate-600 mb-3 uppercase tracking-wide">
                        Purpose
                    </h3>
                    <p className="text-slate-700 leading-relaxed">
                        {grant.grant_purpose}
                    </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 className="font-semibold text-sm text-slate-600 mb-3 uppercase tracking-wide">
                            Grant Period
                        </h3>
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-teal-100 rounded-lg hover:scale-110 transition-transform duration-300">
                                <Calendar className="h-4 w-4 text-teal-600" />
                            </div>
                            <span className="text-slate-700 font-medium">
                                {format(new Date(grant.start_date), "MMM d, yyyy")} to{" "}
                                {format(new Date(grant.end_date), "MMM d, yyyy")}
                            </span>
                        </div>
                    </div>

                    <div>
                        <h3 className="font-semibold text-sm text-slate-600 mb-3 uppercase tracking-wide">
                            Annual Budget
                        </h3>
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-teal-100 rounded-lg hover:scale-110 transition-transform duration-300">
                                <IndianRupee className="h-4 w-4 text-teal-600" />
                            </div>
                            <span className="text-slate-700 font-bold text-lg">
                                {formatCurrency(grant.annual_budget)}
                            </span>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="font-semibold text-sm text-slate-600 mb-3 uppercase tracking-wide">
                        Funding Sources
                    </h3>
                    <p className="text-slate-700 leading-relaxed">
                        {grant.funding_sources}
                    </p>
                </div>
            </CardContent>
        </Card>
    </div>
);

const QuestionEditor = ({
    qa,
    value,
    error,
    onChange,
}: {
    qa: QuestionAnswer;
    value: string;
    error?: string;
    onChange: (value: string) => void;
}) => {
    const charCount = value.length;

    return (
        <div className="space-y-3">
            <Textarea
                value={value}
                onChange={(e) => onChange(e.target.value)}
                rows={6}
                className="resize-none"
                placeholder="Enter your answer here..."
            />
            {error && <p className="text-red-500 text-sm">{error}</p>}
            <div className="flex justify-between items-center text-sm">
                <span className="text-gray-500">
                    {VALIDATION_RULES.MIN_LENGTH} - {VALIDATION_RULES.MAX_LENGTH}{" "}
                    characters
                </span>
                <span
                    className={`font-medium ${charCount < VALIDATION_RULES.MIN_LENGTH
                            ? "text-red-500"
                            : charCount > VALIDATION_RULES.MAX_LENGTH
                                ? "text-red-500"
                                : "text-green-600"
                        }`}
                >
                    {charCount} / {VALIDATION_RULES.MAX_LENGTH}
                </span>
            </div>
        </div>
    );
};

// Main Component
export default function NarrativeReportView() {
    const router = useRouter();
    const { id } = useParams<{ id: string }>();
    const queryClient = useQueryClient();

    // State
    const [editedAnswers, setEditedAnswers] = useState<AnswerMap>({});
    const [originalAnswers, setOriginalAnswers] = useState<AnswerMap>({});
    const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
        {},
    );
    const [isEditing, setIsEditing] = useState(false);

    // Query
    const {
        data: report,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ["narrative-report", id],
        queryFn: () => fetchReport(id!),
        enabled: !!id,
    });

    // Mutation
    const saveMutation = useMutation({
        mutationFn: (payload: any) => saveReport(id!, payload),
        onSuccess: (data) => {
            queryClient.setQueryData(["narrative-report", id], data);

            const updatedAnswers = createAnswerMap(data.question_answers);
            setEditedAnswers(updatedAnswers);
            setOriginalAnswers(updatedAnswers);

            const answersChanged = hasChanges(editedAnswers, originalAnswers);
            const wasResubmitted =
                report?.status === "REJECTED" &&
                answersChanged &&
                data.status === "PENDING";

            const message = wasResubmitted
                ? "Report updated and resubmitted for review."
                : answersChanged
                    ? "Report updated successfully."
                    : "No changes made.";

            toast.success(message);
            setIsEditing(false);
            setValidationErrors({});
        },
        onError: (error: any) => {
            toast.error(error.response?.data?.detail || "Failed to save changes.");
        },
    });

    // Initialize answers when report loads
    useEffect(() => {
        if (report) {
            const answerMap = createAnswerMap(report.question_answers);
            setEditedAnswers(answerMap);
            setOriginalAnswers(answerMap);
            setValidationErrors({});
        }
    }, [report]);

    // Derived state
    const answersChanged = hasChanges(editedAnswers, originalAnswers);
    const canEdit = report?.status === "REJECTED";
    const shouldResubmit = report?.status === "REJECTED" && answersChanged;

    // Handlers
    const handleAnswerChange = useCallback((qaId: number, newAnswer: string) => {
        setEditedAnswers((prev) => ({ ...prev, [qaId]: newAnswer }));
        setValidationErrors((prev) => ({ ...prev, [qaId]: undefined }));
    }, []);

    const validateAllAnswers = useCallback((): boolean => {
        if (!report?.question_answers) return false;

        const errors: ValidationErrors = {};
        let hasErrors = false;

        for (const qa of report.question_answers) {
            const answer = editedAnswers[qa.id] || "";
            const error = validateAnswer(answer);

            if (error) {
                errors[qa.id] = error;
                hasErrors = true;
            }
        }

        setValidationErrors(errors);
        return !hasErrors;
    }, [report?.question_answers, editedAnswers]);

    const handleSave = useCallback(() => {
        if (!validateAllAnswers()) {
            toast.error("Please correct the validation errors before saving.");
            return;
        }

        const payload = {
            questions: Object.entries(editedAnswers).map(([id, answer]) => ({
                id: Number(id),
                answer,
            })),
            ...(shouldResubmit && { status: "PENDING" }),
        };

        saveMutation.mutate(payload);
    }, [editedAnswers, shouldResubmit, validateAllAnswers, saveMutation]);

    const handleCancel = useCallback(() => {
        setIsEditing(false);
        setValidationErrors({});
        setEditedAnswers({ ...originalAnswers });
    }, [originalAnswers]);

    // Error state
    if (isError) {
        toast.error(
            (error as any)?.response?.data?.detail || "Failed to load report.",
        );
        return (
            <div className="container mx-auto py-0 px-4 max-w-5xl">
                <p className="text-destructive">
                    Failed to load report. Please try again.
                </p>
            </div>
        );
    }

    // Loading state
    if (isLoading || !report) return <LoadingSkeleton />;

    const rejectionRemarks = report.rejection_remarks || report.remarks;

    return (
        <div className="container mx-auto p-2">
            <div className="animate-fade-in-up">
                <Button
                    onClick={router.back}
                    className="mb-6 text-slate-600 hover:text-slate-800 hover:bg-slate-100 transition-all duration-300 group"
                    variant="ghost"
                >
                    <Undo2 className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-300" />
                    Go Back
                </Button>
            </div>

            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                <div>
                    <StatusBadge status={report.status} />
                    <div className="animate-fade-in-up delay-200">
                        <h1 className="text-4xl font-bold tracking-tight text-slate-800 mb-2">
                            {report.grant.grant_name}
                        </h1>
                        <p className="text-slate-600 text-lg font-medium">
                            Q{report.quarter} {report.year} •{" "}
                            {getQuarterDateRange(`Q${report.quarter}`, `${report.year}`)}
                        </p>
                    </div>
                </div>
            </div>

            <div className="space-y-8">
                {report.status === "REJECTED" && rejectionRemarks && (
                    <RejectionCard remarks={rejectionRemarks} />
                )}

                <GrantCard grant={report.grant} />

                <div className="animate-fade-in-up delay-500">
                    <Card className="shadow-xl hover:shadow-2xl transition-all duration-500 border bg-white">
                        <CardHeader className="pb-4 border-b border-slate-100">
                            <CardTitle className="flex items-center gap-3 text-slate-800">
                                <div className="p-2 bg-slate-100 rounded-lg">
                                    <FileSpreadsheet className="h-5 w-5 text-slate-600" />
                                </div>
                                Report Details
                            </CardTitle>
                            <CardDescription className="text-slate-600">
                                Last updated on{" "}
                                {format(new Date(report.created_at), "EEEE, MMMM d, yyyy")}
                            </CardDescription>
                        </CardHeader>

                        <CardContent className="pt-6">
                            <div className="space-y-8">
                                {report.question_answers.map((qa, index) => (
                                    <div
                                        key={qa.id}
                                        className="animate-fade-in-up group"
                                        style={{ animationDelay: `${0.6 + index * 0.1}s` }}
                                    >
                                        <div className="mb-4">
                                            <h3 className="font-semibold text-lg text-slate-800 mb-3 group-hover:text-slate-900 transition-colors duration-300">
                                                {index + 1}. {qa.question}
                                            </h3>
                                            {isEditing ? (
                                                <QuestionEditor
                                                    qa={qa}
                                                    value={editedAnswers[qa.id] || ""}
                                                    error={validationErrors[qa.id]}
                                                    onChange={(value) => handleAnswerChange(qa.id, value)}
                                                />
                                            ) : (
                                                <div className="p-6 bg-slate-50 rounded-xl border border-slate-200 hover:border-slate-300 transition-all duration-300 group-hover:shadow-sm">
                                                    <p className="text-slate-700 whitespace-pre-wrap leading-relaxed">
                                                        {qa.answer}
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                        {index < report.question_answers.length - 1 && (
                                            <Separator className="my-8 bg-slate-200" />
                                        )}
                                    </div>
                                ))}
                            </div>
                        </CardContent>

                        <CardFooter className="justify-end gap-3 pt-6 border-t border-slate-100">
                            {isEditing ? (
                                <div className="flex gap-3 animate-fade-in-up">
                                    <Button
                                        variant="outline"
                                        onClick={handleCancel}
                                        disabled={saveMutation.isPending}
                                        className="hover:bg-slate-50 transition-all duration-300"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        onClick={handleSave}
                                        disabled={saveMutation.isPending}
                                        className="min-w-[140px] bg-teal-500 hover:bg-teal-700 transition-all duration-300 group"
                                    >
                                        {saveMutation.isPending ? (
                                            <>
                                                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                                Saving...
                                            </>
                                        ) : (
                                            <>
                                                <Save className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                                                {shouldResubmit ? "Save & Resubmit" : "Save Changes"}
                                            </>
                                        )}
                                    </Button>
                                </div>
                            ) : (
                                <Button
                                    onClick={() => setIsEditing(true)}
                                    disabled={!canEdit}
                                    variant={canEdit ? "default" : "secondary"}
                                    className={
                                        canEdit
                                            ? "bg-teal-500 hover:bg-teal-700 transition-all duration-300 group hover:scale-105"
                                            : "opacity-50 cursor-not-allowed"
                                    }
                                >
                                    <Edit className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                                    {canEdit ? "Edit Answers" : "Cannot Edit"}
                                </Button>
                            )}
                        </CardFooter>
                    </Card>
                </div>
            </div>
        </div>
    );
}
