"use client";

import {
  EditOutcome,
  NewOutcomeType,
} from "@/app/milestones-reports/_components/quantitative/edit-outcome";
import { Button } from "@/components/ui/button";
import { outcome } from "@/data/quantitative";
import { getGranteeOutcome } from "@/services/grantmaker/quantitative-service";
import { OutcomeTypeWithImpact } from "@/types/quantitative";
import { useMutation, useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { Undo2Icon } from "lucide-react";
import { notFound, useParams, useRouter } from "next/navigation";

export default function () {
  const router = useRouter();

  const params = useParams();

  const id = params.outcome_id;

  if (typeof id !== "string") return notFound();

  const outcomeQuery = useQuery<OutcomeTypeWithImpact | undefined, Error>({
    queryKey: ["outcome", id],
    queryFn: async () => {
      const outcomes = await getGranteeOutcome(id);
      return outcomes;
    },
  });

  return (
    <motion.div
      initial={{ y: 10, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: -10, opacity: 0 }}
      transition={{ duration: 0.2 }}
      className="space-y-6"
    >
      <Button variant="ghost" onClick={() => router.back()}>
        <Undo2Icon />
        Back
      </Button>
      {outcomeQuery.data && <EditOutcome outcome={outcomeQuery.data} />}
    </motion.div>
  );
}
