"use client";

import { OutcomeHeader } from "@/components/reports/quantitative/Outcomes";
import { output_columns } from "@/components/reports/quantitative/outcomes/columns";
import { OutputTable } from "@/components/reports/quantitative/outcomes/table";
import { Button, buttonVariants } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { outcome } from "@/data/quantitative";
import { cn } from "@/lib/utils";
import { getGranteeOutcome } from "@/services/grantmaker/quantitative-service";
import { OutcomeType, OutcomeTypeWithImpact } from "@/types/quantitative";
import { useQuery } from "@tanstack/react-query";
import { AnimatePresence, motion } from "framer-motion";
import { PencilIcon, Undo2Icon } from "lucide-react";
import Link from "next/link";
import { notFound, useParams, useRouter } from "next/navigation";

export default function () {
  const router = useRouter();
  const params = useParams();

  const id = params.outcome_id;

  if (typeof id !== "string") return;

  const outcomeQuery = useQuery<OutcomeTypeWithImpact | undefined, Error>({
    queryKey: ["outcome", id],
    queryFn: async () => {
      const outcomes = await getGranteeOutcome(id);
      return outcomes;
    },
  });

  return (
    <motion.div
      initial={{ y: 10, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: -10, opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex justify-between">
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -10, opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Button variant="ghost" onClick={() => router.back()}>
            <Undo2Icon />
            Back
          </Button>
        </motion.div>

        {outcomeQuery.data && outcomeQuery.data.impact && (
          <motion.div
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -10, opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Link
              className={cn(buttonVariants())}
              href={`/milestones-reports/quantitative/${outcomeQuery.data.impact.id}/outcome/${id}/edit`}
            >
              <PencilIcon className="size-4" /> Edit
            </Link>
          </motion.div>
        )}
      </div>

      <div className="p-0 pt-6">
        {outcomeQuery.isLoading && <Skeleton className="h-[200px]" />}
        {outcomeQuery.data && <OutcomeHeader outcome={outcomeQuery.data} />}

        <div className="py-8">
          <AnimatePresence>
            {outcomeQuery.isLoading && (
              <motion.div
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -10, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Skeleton className="h-[500px]" />
              </motion.div>
            )}
            {outcomeQuery.data && outcomeQuery.data.outputs && (
              <motion.div>
                <OutputTable
                  columns={output_columns}
                  data={outcomeQuery.data.outputs}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
}
