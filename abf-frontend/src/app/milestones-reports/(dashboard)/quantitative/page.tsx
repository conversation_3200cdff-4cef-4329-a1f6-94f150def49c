"use client";

import { ImpactList } from "@/components/reports/quantitative/Impacts";
import { ImpactType } from "@/types/quantitative";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { getGranteeImpacts } from "@/services/grantmaker/quantitative-service";

export default function QuantitativePage() {
  const impactQuery = useQuery<ImpactType[], Error>({
    queryKey: ["impacts"],
    queryFn: async () => {
      const impacts = await getGranteeImpacts();
      return impacts;
    },
  });

  return (
    <motion.div
      initial={{ y: 10, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: -10, opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <h2 className="text-2xl font-semibold tracking-tight mb-4">Impacts</h2>

      <ImpactList granteeId="1" userType="GRANTEE" impactQuery={impactQuery} />
    </motion.div>
  );
}
