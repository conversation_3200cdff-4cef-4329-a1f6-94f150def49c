"use client";
import { ReactNode } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { Layout } from "@/components/Layout";

const tabs = [
  { name: "Narrative Reports", href: "/milestones-reports" },
  { name: "Gallery", href: "/milestones-reports/gallery" },
];

export default function MilestonesReportsLayout({
  children,
}: {
  children: ReactNode;
}) {
  return <Layout>{children}</Layout>;
}
