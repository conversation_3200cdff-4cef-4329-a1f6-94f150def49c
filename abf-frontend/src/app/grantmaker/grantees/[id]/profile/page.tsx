"use client";

import { useState, useEffect } from "react";
import React from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Layout from "@/components/grantmaker/Layout";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  FileText,
  User,
  Building,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Globe,
  FileSpreadsheet,
  Briefcase,
  CheckCircle,
  XCircle,
  IndianRupeeIcon,
  Wallet,
  LightbulbIcon,
  Info,
  Users,
  FileCheck,
  Award,
  BookOpen,
  Clock,
  Hash,
  CreditCard,
  Building2,
  Layers,
  AlertCircle,
  Download,
  FileSpreadsheetIcon,
  ImageIcon,
  ClipboardList,
  Check,
  CircleCheck,
  ChartAreaIcon,
  Eye,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

import { PendingReportsTab } from "@/components/grantmaker/PendingReportsTab";
import GranteeGalleryTab from "@/components/grantmaker/GranteeGalleryTab";
import { formatCurrency } from "@/services/grantmaker/grantmaker-service";
import {
  getGranteeProfile,
  getGrantHistory,
} from "@/services/grantmaker/grantee-service";
import { motion, AnimatePresence } from "framer-motion";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import GrantDetailsDropdown from "@/components/grantmaker/GrantDetailsDropdown";
import { toast } from "sonner";

import DisbursementTableGM from '@/components/funding/DisbursementTableGM'; 

// Define our own types to avoid conflicts with imported types
interface GrantPerson {
  id: string;
  name: string;
  email: string;
  phone: string;
  din: string;
  designation: string;
}

interface GrantDocument {
  id: number;
  objectKey: string;
  attachmentType: string;
  attachmentTypeName: string;
  originalFilename: string;
  remarks: string | null;
  status: "PENDING" | "VERIFIED" | "REJECTED";
  uploadedAt: string;
  uploadedBy: number;
  uploadedByEmail: string;
}

interface ExtendedHistoricalGrant {
  id: string | number;
  organization: number;
  grantName: string;
  grantPurpose: string;
  startDate: string;
  endDate: string;
  amount: number;
  status: string;
  statusDisplay: string;
  createdAt?: string;
  updatedAt?: string;
}

// Define a more comprehensive grant type
interface ExtendedGrant {
  id: string | number;
  name?: string;
  grantName?: string;
  purpose?: string;
  grantPurpose?: string;
  startDate: string;
  endDate: string;
  amount?: number;
  budget?: number;
  status?: string;
  statusDisplay?: string;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any; // Allow for additional properties
}

// Create a complete profile type for our needs
interface ExtendedGranteeProfile {
  // Basic properties
  id: string;
  name: string;
  sector: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  location: string;
  startDate: string;
  endDate?: string;
  status: "active" | "pending" | "completed";
  description: string;
  mission: string;
  vision: string;
  foundedYear: number;
  registrationNumber: string;
  taxExemptionNumber?: string;
  taxRegistrationNumberUnder12A: string;
  darpanId: string;
  panCard: string;
  website?: string;
  logoKey?: string | null;

  // Collections
  grants: ExtendedGrant[];
  kmp_details?: GrantPerson[];
  supportingDocuments: GrantDocument[];

  // Financial properties
  totalFunding: number;
  disbursedAmount: number;
  remainingAmount: number;

  // Additional properties
  teamMembers?: number;
  previousGrants?: ExtendedHistoricalGrant[];
  csrRegistrationNumber?: string;
  mailingAddress?: string;
  numberOfTeamMembers?: number;
  teamSize?: number;
  address?: string;
  grantHistory?: ExtendedGrant[];
  [key: string]: any; // Allow for additional properties
}
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogClose,
} from "@/components/ui/dialog";
import { updateDocumentStatus } from "@/services/profile-service";
import { GrantmakerExpenseHistoryTable } from "@/components/grantmaker/GrantmakerExpenseHistoryTable";
import { fetchExpensesByGrant } from "@/services/expenses-service";
import { GranteeReportsTab } from "@/components/grantmaker/GranteeReportsTab";
import { GranteeExpenseTab } from "@/components/grantmaker/GranteeExpenseTab";
import { QuantitativeReports } from "@/components/grantmaker/QuantitativeReports";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import CreateDisbursementForm from "@/components/funding/CreateDisbursementForm";

export default function GranteeProfilePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // Unwrap params outside of any try/catch
  const { id } = React.use(params);

  const router = useRouter();
  const [granteeProfile, setGranteeProfile] =
    useState<ExtendedGranteeProfile>();
  const [rejectedDocId, setRejectedDocId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("profile");
  const [activeDocumentTab, setActiveDocumentTab] = useState("supporting");
  const [activeGrantTab, setActiveGrantTab] = useState("current");
  const [remarks, setRemarks] = useState(""); // state for rejection remarks
  const [remarksDialog, setRemarksDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [expenses, setExpenses] = useState<any[]>([]);
  const [activeReportTab, setActiveReportTab] = useState("narrative");
  const [selectedGrantId, setSelectedGrantId] = useState<string>("");

  // Grant detail dropdown state (only for current grants)
  const [expandedGrantIds, setExpandedGrantIds] = useState<Set<string>>(
    new Set(),
  );

  // Grant history state
  const [grantHistory, setGrantHistory] = useState<any[]>([]);
  const [loadingGrantHistory, setLoadingGrantHistory] = useState(false);

  useEffect(() => {
    fetchGranteeProfile();
  }, []);

  // Function to handle toggling grant detail dropdown (only for current grants)
  const handleToggleGrantDetails = (grantId: string) => {
    setExpandedGrantIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(grantId)) {
        newSet.delete(grantId);
      } else {
        newSet.add(grantId);
      }
      return newSet;
    });
  };

  // Function to fetch grant history when Previous Grant History tab is accessed
  const fetchGrantHistoryData = async () => {
    if (!granteeProfile?.id) return;

    setLoadingGrantHistory(true);
    try {
      const historyData = await getGrantHistory(granteeProfile.id);
      setGrantHistory(historyData);
    } catch (error) {
      console.error("Error fetching grant history:", error);
      toast.error("Failed to load grant history");
      setGrantHistory([]);
    } finally {
      setLoadingGrantHistory(false);
    }
  };

  // Effect to fetch grant history when Previous Grant History tab is accessed
  useEffect(() => {
    if (activeGrantTab === "previous" && granteeProfile?.id) {
      fetchGrantHistoryData();
    }
  }, [activeGrantTab, granteeProfile?.id]);

  const fetchGranteeProfile = async () => {
    setLoading(true);
    setIsLoading(true);
    try {
      // Fetch from the real API
      const profile = await getGranteeProfile(id);
      if (profile) {
        console.log("Fetched profile data:", profile);

        // Fetch additional data from the profile
        console.log("Profile data for debugging:", profile);

        // Log the profile data for debugging
        console.log("Fetched profile data:", profile);

        // Get the raw API response data
        const apiResponse = (profile as any)._rawApiResponse || {};

        // Extract organization data from the profile
        const orgData = (profile as any).organization || profile;

        // Create a new profile object with all the data we need
        const extendedProfile: ExtendedGranteeProfile = {
          // Copy all existing properties
          ...(profile as any),

          // Use real data from the backend - directly use the properties from the profile object
          // This ensures we're using the data that was already processed in the service
          csrRegistrationNumber:
            (profile as any).csrRegistrationNumber ||
            orgData.csr_registration_number ||
            "",
          mailingAddress:
            (profile as any).mailingAddress ||
            orgData.mailing_address ||
            profile.location ||
            "",
          teamMembers:
            (profile as any).teamMembers ||
            (profile as any).number_of_team_members ||
            0,

          // Use mission, vision and description from the backend
          mission: (profile as any).mission || orgData.mission || "",
          vision: (profile as any).vision || orgData.vision || "",
          description:
            (profile as any).description || orgData.background_history || "",

          // Use KMP details from the backend - ensure it's an array with no dummy data
          kmp_details: Array.isArray((profile as any).kmp_details)
            ? (profile as any).kmp_details
            : Array.isArray(apiResponse.kmp_details)
              ? apiResponse.kmp_details
              : Array.isArray(orgData.kmps)
                ? orgData.kmps.map((kmp: any) => ({
                    id: kmp.id,
                    name: kmp.name,
                    email: kmp.email,
                    phone: kmp.phone_number,
                    phone_number: kmp.phone_number,
                    din: kmp.din,
                    designation: kmp.designation,
                  }))
                : [],

          // Use grant history from the backend - ensure it's an array with no dummy data
          previousGrants: Array.isArray((profile as any).previousGrants)
            ? (profile as any).previousGrants
            : Array.isArray((profile as any).grant_histories)
              ? (profile as any).grant_histories
              : Array.isArray(apiResponse.grant_histories)
                ? apiResponse.grant_histories
                : [],
        };

        setGranteeProfile(extendedProfile);
        setLoading(false);
        setIsLoading(false);
        return;
      } else {
        throw new Error("Profile data not found");
      }
    } catch (error) {
      console.error("Error fetching grantee profile:", error);
      setLoading(false);
      setIsLoading(false);
      // Redirect to grantees list if grantee not found
      router.push("/grantmaker/grantees");
    }
  };

  // Transform expense data to match GranteeExpenseTab interface
  const transformExpenseData = (apiData: any[]) => {
    return apiData.map((expense: any, index: number) => ({
      id: expense.id || index + 1,
      sr_no: expense.sr_no || index + 1,

      // Map to GranteeExpenseTab expected fields
      loggedDate: expense.expense_date || expense.loggedDate || new Date().toISOString().split('T')[0],
      category: expense.main_headers || expense.main_header || expense.category || "",
      description: expense.particulars || expense.description || "",
      totalBudget: expense.total_budget || expense.totalBudget || 0,
      totalActualSpent: expense.total_actual || expense.totalActualSpent || 0,
      status: expense.status || "pending",

      // Keep original fields for GrantmakerExpenseHistoryTable compatibility
      particulars: expense.particulars || "",
      main_header: expense.main_headers || expense.main_header || "",
      sub_headers: expense.sub_headers || expense.sub_header || "",
      units: expense.units || "",
      frequency: expense.frequency || "",
      cost_per_unit: expense.cost_per_unit || 0,
      budget_quarterly: {
        Q1: expense.budget_q1 || 0,
        Q2: expense.budget_q2 || 0,
        Q3: expense.budget_q3 || 0,
        Q4: expense.budget_q4 || 0,
      },
      actuals_quarterly: {
        Q1: expense.actual_q1 || 0,
        Q2: expense.actual_q2 || 0,
        Q3: expense.actual_q3 || 0,
        Q4: expense.actual_q4 || 0,
      },
      total_budget: expense.total_budget || 0,
      total_actual: expense.total_actual || 0,
      receipt:
        expense.receipt ||
        (expense.source_type === "manual" ? "Manual entry" : ""),
      remarks: expense.remarks || "",
      source_type: expense.source_type || "manual",
      expense_date: expense.expense_date || expense.loggedDate || "",
    }));
  };

  // Set default selected grant when grantee profile loads
  useEffect(() => {
    if (granteeProfile?.grants && granteeProfile.grants.length > 0 && !selectedGrantId) {
      setSelectedGrantId(granteeProfile.grants[0].id.toString());
    }
  }, [granteeProfile, selectedGrantId]);

  useEffect(() => {
    const fetchExpensesOfGrant = async () => {
      if (!selectedGrantId) return;

      try {
        console.log(
          "Fetching expenses for grant ID:",
          selectedGrantId
        );
        const response = await fetchExpensesByGrant(selectedGrantId);
        console.log("Response outside = " + JSON.stringify(response));

        if (response.status === "SUCCESS") {
          console.log("Response status success");
          const transformedData = transformExpenseData(response.data);
          setExpenses(transformedData);
        }
      } catch (error) {
        console.log("ERROR: ", JSON.stringify(error));
      }
    };

    if (selectedGrantId) {
      fetchExpensesOfGrant();
    }
  }, [selectedGrantId]);

  const handleDocumentStatusChange = async (
    documentId: number,
    status: string,
    remarks?: string,
  ) => {
    let payload: { status: string; remarks?: string } = { status };

    if (status === "REJECTED") {
      payload.remarks = remarks;
    }

    try {
      const response = await updateDocumentStatus(documentId, payload);
      if (response.status === "SUCCESS") {
        if (granteeProfile && granteeProfile.supportingDocuments) {
          const updatedDocuments = granteeProfile.supportingDocuments.map(
            (doc) =>
              doc.id === documentId
                ? {
                    ...doc,
                    status: status as "VERIFIED" | "PENDING" | "REJECTED",
                  }
                : doc,
          );
          setGranteeProfile({
            ...granteeProfile,
            supportingDocuments: updatedDocuments,
          });
        }
      } else {
        console.log("Failed to update the data");
      }
    } catch (error: any) {
      console.log("ERROR = " + error);
    }
  };

  if (loading) {
    return (
      <Layout title="Grantee Profile">
        <div className="flex flex-col justify-center items-center min-h-[400px] gap-4">
          <div className="h-16 w-16 rounded-full border-4 border-t-[#00998F] border-r-[#00998F] border-b-teal-200 border-l-teal-200 animate-spin"></div>
          <div className="text-[#00998F] font-medium text-lg bg-gradient-to-r from-[#00998F] to-teal-500 bg-clip-text text-transparent animate-pulse">
            Loading grantee profile...
          </div>
          <p className="text-gray-500 text-sm max-w-md text-center">
            Fetching comprehensive organization details from the database
          </p>
        </div>
      </Layout>
    );
  }

  if (!granteeProfile) {
    return (
      <Layout title="Grantee Profile">
        <div className="flex flex-col justify-center items-center min-h-[400px] gap-4">
          <div className="bg-red-100 p-4 rounded-full">
            <AlertCircle className="h-12 w-12 text-red-500" />
          </div>
          <div className="text-red-500 font-medium text-lg">
            Grantee profile not found
          </div>
          <p className="text-gray-500 text-sm max-w-md text-center">
            The requested profile could not be found in the database
          </p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push("/grantmaker/grantees")}
          >
            Return to Grantees List
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={granteeProfile.name}>
      <div className="space-y-6 pb-8">
        {/* Enhanced Header with back button and profile info */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex items-center mb-8 bg-white p-4 rounded-xl shadow-sm border border-gray-100"
        >
          <Button
            variant="ghost"
            className="mr-4 p-2 hover:bg-gray-100 transition-colors"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16 bg-gradient-to-br from-[#00998F] to-teal-500 text-white shadow-md ring-2 ring-teal-100">
                {granteeProfile.logoKey && (
                  <AvatarImage
                    src={granteeProfile.logoKey}
                    alt={`${granteeProfile.name} logo`}
                    className="object-cover"
                  />
                )}
                <AvatarFallback className="text-xl font-semibold">
                  {granteeProfile.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-2xl font-bold text-gray-800 flex items-center">
                  {granteeProfile.name}
                </h1>
                <div className="flex items-center mt-2 text-gray-600">
                  <Building2 className="h-4 w-4 mr-1.5 text-teal-600" />
                  <span className="font-medium">{granteeProfile.sector}</span>
                  <span className="mx-2 text-gray-300">|</span>
                  <Calendar className="h-4 w-4 mr-1.5 text-teal-600" />
                  <span>Est. {granteeProfile.foundedYear}</span>
                  {granteeProfile.website && (
                    <>
                      <span className="mx-2 text-gray-300">|</span>
                      <a
                        href={granteeProfile.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline flex items-center"
                      >
                        <Globe className="h-4 w-4 mr-1.5" />
                        Website
                      </a>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {isLoading ? (
          <div className="flex flex-col justify-center items-center py-12 gap-4">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-t-[#00998F] border-r-[#00998F] border-b-teal-200 border-l-teal-200"></div>
            <div className="text-[#00998F] font-medium text-lg bg-gradient-to-r from-[#00998F] to-teal-500 bg-clip-text text-transparent animate-pulse">
              Loading profile data...
            </div>
            <p className="text-gray-500 text-sm max-w-md text-center">
              Fetching comprehensive organization details from the database
            </p>
          </div>
        ) : (
          <Tabs
            defaultValue="reports"
            value={activeTab}
            onValueChange={setActiveTab}
            className="mt-8"
          >
            <TabsList className="flex w-full mb-8 bg-gray-100/70 p-1 rounded-lg shadow-sm">
              <TabsTrigger
                value="profile"
                className="flex-1 flex items-center gap-2 py-2.5 justify-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
              >
                <User className="h-4 w-4" />
                Profile
              </TabsTrigger>
              {/* <TabsTrigger */}
              {/*   value="approvals" */}
              {/*   className="flex-1 flex items-center gap-2 py-2.5 justify-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300" */}
              {/* > */}
              {/*   <CircleCheck className="h-4 w-4" /> */}
              {/*   Approvals */}
              {/* </TabsTrigger> */}
              <TabsTrigger
                value="expenses"
                className="flex-1 flex items-center gap-2 py-2.5 justify-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Expenses
              </TabsTrigger>
              <TabsTrigger
                value="grants"
                className="flex-1 flex items-center gap-2 py-2.5 justify-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
              >
                <Wallet className="h-4 w-4" />
                Grants
              </TabsTrigger>
              <TabsTrigger
                value="documents"
                className="flex-1 flex items-center gap-2 py-2.5 justify-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
              >
                <FileText className="h-4 w-4" />
                Documents
              </TabsTrigger>
              <TabsTrigger
                value="reports"
                className="flex-1 flex items-center gap-2 py-2.5 justify-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
              >
                <ClipboardList className="h-4 w-4" />
                Reports
              </TabsTrigger>
               <TabsTrigger
                value="disbursement"
                className="flex-1 flex items-center gap-2 py-2.5 justify-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
              >
                <CreditCard className="h-4 w-4" />
                Disbursement
              </TabsTrigger>

            </TabsList>

            {/* Profile Tab */}
            <TabsContent value="profile" className="space-y-8">
              <AnimatePresence mode="wait">
                <motion.div
                  key="profile-content"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4 }}
                >
                  {/* Profile Overview Section */}
                  <div className="mb-8">
                    {/* Combined Organization and Basic Information Card */}
                    <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
                      <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                              <Building2 className="h-5 w-5 mr-2 text-[#00998F]" />
                              Organization Information
                            </CardTitle>
                            <CardDescription>
                              Comprehensive details about the organization and
                              contact information
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {/* Organization Information Fields */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <Building2 className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                Organization Name
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                {granteeProfile.name}
                              </p>
                            </motion.div>

                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <Layers className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                Legal Entity Type
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                {granteeProfile.sector}
                              </p>
                            </motion.div>

                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <Users className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                Number of Team Members
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                {granteeProfile.teamMembers || "N/A"}
                              </p>
                            </motion.div>

                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <User className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                Point of Contact Name
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                {(granteeProfile as any).organization
                                  ?.point_of_contact_name ||
                                  (granteeProfile as any)
                                    .point_of_contact_name ||
                                  granteeProfile.contactPerson ||
                                  "N/A"}
                              </p>
                            </motion.div>

                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <Mail className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                Email
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                <a
                                  href={`mailto:${granteeProfile.contactEmail}`}
                                  className="text-blue-600 hover:underline"
                                >
                                  {granteeProfile.contactEmail}
                                </a>
                              </p>
                            </motion.div>

                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <Phone className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                Phone
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                {granteeProfile.contactPhone || "N/A"}
                              </p>
                            </motion.div>

                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <MapPin className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                Mailing Address
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                {granteeProfile.mailingAddress || "N/A"}
                              </p>
                            </motion.div>
                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <Award className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                Darpan ID
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                {granteeProfile.darpanId || "N/A"}
                              </p>
                            </motion.div>

                            <motion.div
                              className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                              whileHover={{ y: -2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <h3 className="text-sm font-medium text-gray-500 flex items-center">
                                <CreditCard className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                PAN Card
                              </h3>
                              <p className="mt-1 text-gray-900 font-medium">
                                {granteeProfile.panCard || "N/A"}
                              </p>
                            </motion.div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Mission and Vision Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {/* Mission Statement Card */}
                    <motion.div
                      whileHover={{ y: -5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden h-full bg-white hover:shadow-md transition-shadow duration-300">
                        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                                <LightbulbIcon className="h-5 w-5 mr-2 text-[#00998F]" />
                                Mission Statement
                              </CardTitle>
                              <CardDescription>
                                Organization's mission statement
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="flex-grow">
                          <div className="p-4 bg-gray-50 rounded-lg h-full">
                            {granteeProfile.mission &&
                            granteeProfile.mission.trim() ? (
                              <p className="text-gray-700 leading-relaxed">
                                {granteeProfile.mission}
                              </p>
                            ) : (
                              <div className="text-center py-4">
                                <div className="bg-teal-50 p-2 rounded-full inline-flex mb-2">
                                  <LightbulbIcon className="h-5 w-5 text-[#00998F]" />
                                </div>
                                <p className="text-gray-500">
                                  No mission statement provided.
                                </p>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>

                    {/* Vision Statement Card */}
                    <motion.div
                      whileHover={{ y: -5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden h-full bg-white hover:shadow-md transition-shadow duration-300">
                        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                                <Eye className="h-5 w-5 mr-2 text-[#00998F]" />
                                Vision Statement
                              </CardTitle>
                              <CardDescription>
                                Organization's vision statement
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="flex-grow">
                          <div className="p-4 bg-gray-50 rounded-lg h-full">
                            {granteeProfile.vision &&
                            granteeProfile.vision.trim() ? (
                              <p className="text-gray-700 leading-relaxed">
                                {granteeProfile.vision}
                              </p>
                            ) : (
                              <div className="text-center py-4">
                                <div className="bg-teal-50 p-2 rounded-full inline-flex mb-2">
                                  <Eye className="h-5 w-5 text-[#00998F]" />
                                </div>
                                <p className="text-gray-500">
                                  No vision statement provided.
                                </p>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </div>

                  {/* Background History Card - Full Width */}
                  <div className="mb-8">
                    <motion.div
                      whileHover={{ y: -5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
                        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                                <BookOpen className="h-5 w-5 mr-2 text-[#00998F]" />
                                Background History
                              </CardTitle>
                              <CardDescription>
                                Organization's historical background
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="p-4 bg-gray-50 rounded-lg">
                            {granteeProfile.description &&
                            granteeProfile.description.trim() ? (
                              <p className="text-gray-700 leading-relaxed">
                                {granteeProfile.description}
                              </p>
                            ) : (
                              <div className="text-center py-4">
                                <div className="bg-teal-50 p-2 rounded-full inline-flex mb-2">
                                  <BookOpen className="h-5 w-5 text-[#00998F]" />
                                </div>
                                <p className="text-gray-500">
                                  No background history provided.
                                </p>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </div>

                  {/* Key Management Personnel Section */}
                  <div className="mb-8">
                    <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
                      <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                              <Users className="h-5 w-5 mr-2 text-[#00998F]" />
                              Core Leadership Team
                            </CardTitle>
                            <CardDescription>
                              Core leadership team members managing the
                              organization
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {(() => {
                            console.log("KMP Details Debug:", {
                              kmp_details: granteeProfile.kmp_details,
                              isArray: Array.isArray(
                                granteeProfile.kmp_details,
                              ),
                              length: granteeProfile.kmp_details?.length,
                              rawProfile: granteeProfile,
                            });
                            return null;
                          })()}
                          {Array.isArray(granteeProfile.kmp_details) &&
                          granteeProfile.kmp_details.length > 0 ? (
                            granteeProfile.kmp_details
                              .map((person: any, index: number) => {
                                // Only render if person has required data
                                if (!person || !person.name) return null;

                                console.log("Rendering KMP person:", person);
                                return (
                                  <motion.div
                                    key={person.id || index}
                                    className="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:bg-gray-100 transition-colors duration-200"
                                    whileHover={{ y: -2, x: 2 }}
                                    transition={{ duration: 0.2 }}
                                  >
                                    <div className="flex items-start gap-3">
                                      <div className="bg-teal-100 rounded-full p-2 text-teal-700">
                                        <User className="h-5 w-5" />
                                      </div>
                                      <div className="flex-1">
                                        <h3 className="font-medium text-gray-900">
                                          {person.name}
                                        </h3>
                                        <p className="text-sm text-gray-500 mt-1">
                                          {person.designation ||
                                            "No designation specified"}
                                        </p>
                                        <div className="mt-2 space-y-1 text-sm">
                                          {person.email && (
                                            <div className="flex items-center text-gray-600">
                                              <Mail className="h-3.5 w-3.5 mr-1.5" />
                                              <a
                                                href={`mailto:${person.email}`}
                                                className="text-blue-600 hover:underline"
                                              >
                                                {person.email}
                                              </a>
                                            </div>
                                          )}
                                          {(person.phone ||
                                            person.phone_number) && (
                                            <div className="flex items-center text-gray-600">
                                              <Phone className="h-3.5 w-3.5 mr-1.5" />
                                              {person.phone ||
                                                person.phone_number}
                                            </div>
                                          )}
                                          {person.din && (
                                            <div className="flex items-center text-gray-600">
                                              <Hash className="h-3.5 w-3.5 mr-1.5" />
                                              <span>DIN: {person.din}</span>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </motion.div>
                                );
                              })
                              .filter(Boolean) // Filter out any null values
                          ) : (
                            <div className="col-span-2 p-8 text-center">
                              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-4">
                                <div className="flex justify-center mb-4">
                                  <div className="bg-teal-100 rounded-full p-3 text-teal-700">
                                    <Users className="h-8 w-8" />
                                  </div>
                                </div>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                  No Core Leadership Team
                                </h3>
                                <p className="text-gray-500 mb-4">
                                  No core leadership team data is available for
                                  this organization. Team details are added
                                  through the organization profile.
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </motion.div>
              </AnimatePresence>
            </TabsContent>

            {/* Grants Tab */}
            <TabsContent value="grants" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
              >
                <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
                  <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                          <Wallet className="h-5 w-5 mr-2 text-[#00998F]" />
                          Grant Information
                        </CardTitle>
                        <CardDescription>
                          Current and previous grants awarded to this
                          organization
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Tabs
                      defaultValue="current"
                      value={activeGrantTab}
                      onValueChange={setActiveGrantTab}
                      className="mt-2"
                    >
                      <TabsList className="grid grid-cols-2 w-full max-w-md mb-6 bg-gray-100/70 p-1 rounded-lg shadow-sm">
                        <TabsTrigger
                          value="current"
                          className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                        >
                          <CheckCircle className="h-4 w-4" />
                          Current Grant
                        </TabsTrigger>
                        <TabsTrigger
                          value="previous"
                          className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                        >
                          <Clock className="h-4 w-4" />
                          Previous Grant History
                        </TabsTrigger>
                      </TabsList>

                      {/* Current Grant Tab */}
                      <TabsContent value="current" className="space-y-4">
                        <AnimatePresence mode="wait">
                          <motion.div
                            key="current-grants"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.3 }}
                          >
                            {granteeProfile.grants &&
                            granteeProfile.grants.length > 0 ? (
                              <div className="space-y-6">
                                {granteeProfile.grants.map((grant, index) => {
                                  const today = new Date();
                                  const start = new Date(grant.startDate);
                                  const end = new Date(grant.endDate);

                                  const status =
                                    today < start
                                      ? "pending"
                                      : today > end
                                        ? "completed"
                                        : "active";

                                  return (
                                    <motion.div
                                      key={grant.id}
                                      initial={{ opacity: 0, y: 20 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      transition={{
                                        duration: 0.3,
                                        delay: index * 0.1,
                                      }}
                                      whileHover={{ y: -3 }}
                                      className="p-5 border border-gray-200 rounded-lg bg-white hover:shadow-md transition-all duration-300"
                                    >
                                      <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4 mb-4">
                                        <div>
                                          <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                            <Briefcase className="h-4 w-4 mr-2 text-[#00998F]" />
                                            {grant.name}
                                          </h3>
                                          <div className="flex flex-wrap items-center gap-2 mt-2">
                                            <Badge
                                              className={`
                                                ${status === "active" ? "bg-green-100 text-green-800 border border-green-200" : ""}
                                                ${status === "pending" ? "bg-amber-100 text-amber-800 border border-amber-200" : ""}
                                                ${status === "completed" ? "bg-blue-100 text-blue-800 border border-blue-200" : ""}
                                                px-2.5 py-0.5 rounded-full flex items-center
                                              `}
                                            >
                                              {status === "active" && (
                                                <CheckCircle className="h-3 w-3 mr-1" />
                                              )}
                                              {status === "pending" && (
                                                <Clock className="h-3 w-3 mr-1" />
                                              )}
                                              {status === "completed" && (
                                                <FileCheck className="h-3 w-3 mr-1" />
                                              )}
                                              {status.charAt(0).toUpperCase() +
                                                status.slice(1)}
                                            </Badge>
                                            <span className="text-gray-600 flex items-center bg-gray-100 px-2.5 py-0.5 rounded-full text-xs">
                                              <Calendar className="h-3 w-3 mr-1" />
                                              {new Date(
                                                grant.startDate,
                                              ).toLocaleDateString()}{" "}
                                              -{" "}
                                              {new Date(
                                                grant.endDate,
                                              ).toLocaleDateString()}
                                            </span>
                                          </div>
                                        </div>
                                        <div className="flex flex-col items-end">
                                          <motion.div
                                            className="bg-gradient-to-r from-teal-50 to-teal-100/70 px-4 py-2 rounded-lg border border-teal-200/50 shadow-sm"
                                            whileHover={{ scale: 1.05 }}
                                            transition={{ duration: 0.2 }}
                                          >
                                            <span className="text-sm text-gray-600 flex items-center">
                                              <IndianRupeeIcon className="h-3 w-3 mr-1" />
                                              Grant Amount
                                            </span>
                                            <div className="text-xl font-bold text-[#00998F]">
                                              {formatCurrency(
                                                grant.amount || 0,
                                              )}
                                            </div>
                                          </motion.div>
                                        </div>
                                      </div>
                                      <Separator className="my-4" />
                                      <div className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                        <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                                          <Info className="h-4 w-4 mr-1.5 text-[#00998F]" />
                                          Purpose
                                        </h4>
                                        <p className="text-gray-700 leading-relaxed">
                                          {grant.purpose}
                                        </p>
                                      </div>

                                      {/* Grant Details Dropdown */}
                                      <GrantDetailsDropdown
                                        grant={grant}
                                        isExpanded={expandedGrantIds.has(
                                          grant.id?.toString() || "",
                                        )}
                                        onToggle={() =>
                                          handleToggleGrantDetails(
                                            grant.id?.toString() || "",
                                          )
                                        }
                                      />
                                    </motion.div>
                                  );
                                })}
                              </div>
                            ) : (
                              <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ duration: 0.5 }}
                                className="text-center p-8 bg-gray-50 rounded-lg border border-gray-100"
                              >
                                <div className="bg-gray-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                  <Wallet className="h-8 w-8 text-gray-400" />
                                </div>
                                <p className="text-gray-600 font-medium">
                                  No current grants found
                                </p>
                                <p className="text-gray-400 text-sm mt-1">
                                  Current grants will appear here once they are
                                  awarded
                                </p>
                              </motion.div>
                            )}
                          </motion.div>
                        </AnimatePresence>
                      </TabsContent>

                      {/* Previous Grant History Tab */}
                      <TabsContent value="previous" className="space-y-4">
                        <AnimatePresence mode="wait">
                          <motion.div
                            key="previous-grants"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.3 }}
                          >
                            <div className="space-y-4">
                              {loadingGrantHistory ? (
                                <div className="flex items-center justify-center py-8">
                                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00998F]"></div>
                                  <span className="ml-2 text-gray-600">
                                    Loading grant history...
                                  </span>
                                </div>
                              ) : grantHistory.length > 0 ? (
                                grantHistory
                                  .map((grant: any, index: number) => {
                                    // Only render if grant has required data
                                    if (
                                      !grant ||
                                      (!grant.grant_name && !grant.grantName)
                                    )
                                      return null;

                                    // Get start and end dates safely
                                    let startDate = null;
                                    let endDate = null;

                                    try {
                                      startDate =
                                        grant.start_date || grant.startDate
                                          ? new Date(
                                              grant.start_date ||
                                                grant.startDate,
                                            ).toLocaleDateString()
                                          : "Date not specified";
                                    } catch (e) {
                                      startDate = "Invalid date";
                                    }

                                    try {
                                      endDate =
                                        grant.end_date || grant.endDate
                                          ? new Date(
                                              grant.end_date || grant.endDate,
                                            ).toLocaleDateString()
                                          : "Date not specified";
                                    } catch (e) {
                                      endDate = "Invalid date";
                                    }

                                    return (
                                      <motion.div
                                        key={grant.id || index}
                                        className="p-4 bg-gray-50 rounded-lg border border-gray-100 hover:bg-gray-100 transition-colors duration-200"
                                        whileHover={{ y: -2 }}
                                        transition={{ duration: 0.2 }}
                                      >
                                        <div className="flex flex-col md:flex-row justify-between gap-4">
                                          <div>
                                            <h3 className="font-medium text-gray-900">
                                              {grant.grant_name ||
                                                grant.grantName}
                                            </h3>
                                            {(grant.grant_purpose ||
                                              grant.grantPurpose) && (
                                              <p className="text-sm text-gray-500 mt-1">
                                                {grant.grant_purpose ||
                                                  grant.grantPurpose}
                                              </p>
                                            )}
                                            <div className="flex items-center mt-2">
                                              <Calendar className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                                              <span className="text-sm text-gray-600">
                                                {startDate} - {endDate}
                                              </span>
                                            </div>
                                          </div>
                                          <div className="flex flex-col items-end">
                                            {grant.status && (
                                              <Badge
                                                className={`
                                              ${grant.status === "ACTIVE" || grant.status === "active" ? "bg-green-100 text-green-800" : ""}
                                              ${grant.status === "PENDING" || grant.status === "pending" ? "bg-amber-100 text-amber-800" : ""}
                                              ${grant.status === "COMPLETED" || grant.status === "completed" ? "bg-blue-100 text-blue-800" : ""}
                                            `}
                                              >
                                                {grant.status_display ||
                                                  grant.statusDisplay ||
                                                  grant.status}
                                              </Badge>
                                            )}
                                            <div className="mt-2 font-semibold text-[#00998F]">
                                              {formatCurrency(
                                                grant.budget ||
                                                  grant.amount ||
                                                  0,
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                      </motion.div>
                                    );
                                  })
                                  .filter(Boolean) // Filter out any null values
                              ) : (
                                <div className="p-8 text-center">
                                  <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-4">
                                    <div className="flex justify-center mb-4">
                                      <div className="bg-teal-100 rounded-full p-3 text-teal-700">
                                        <Wallet className="h-8 w-8" />
                                      </div>
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                                      No Previous Grant History
                                    </h3>
                                    <p className="text-gray-500 mb-4">
                                      No previous grant history is available for
                                      this organization. Grant history will be
                                      loaded from the backend when available.
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          </motion.div>
                        </AnimatePresence>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* Expenses Tab */}
            <TabsContent value="expenses" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
              >
                {/* Grant Selector - Show when there are grants available */}
                {granteeProfile?.grants && granteeProfile.grants.length > 0 && (
                  <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white mb-6">
                    <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                    <CardHeader className="pb-4">
                      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                        <div>
                          <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                            <FileSpreadsheet className="h-5 w-5 mr-2 text-[#00998F]" />
                            Grant Selection
                          </CardTitle>
                          <CardDescription>
                            {granteeProfile.grants.length > 1
                              ? "Select a grant to view its expense history"
                              : "Viewing expense history for the available grant"}
                          </CardDescription>
                        </div>
                        {granteeProfile.grants.length > 1 && (
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">Grant:</span>
                            <Select value={selectedGrantId} onValueChange={setSelectedGrantId}>
                              <SelectTrigger className="w-[250px] border-teal-200 focus:ring-teal-500">
                                <SelectValue placeholder="Select grant" />
                              </SelectTrigger>
                              <SelectContent>
                                {granteeProfile.grants.map((grant) => (
                                  <SelectItem key={grant.id} value={grant.id.toString()}>
                                    {grant.name || grant.grant_name || 'Unnamed Grant'}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                        {granteeProfile.grants.length === 1 && (
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">Grant:</span>
                            <div className="px-3 py-2 bg-teal-50 border border-teal-200 rounded-md text-teal-800 font-medium">
                              {granteeProfile.grants[0].name || granteeProfile.grants[0].grant_name || 'Unnamed Grant'}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardHeader>
                  </Card>
                )}

                <GranteeExpenseTab
                  granteeId={id}
                  grantId={selectedGrantId}
                  expenses={expenses}
                  isLoading={isLoading}
                />

                <GrantmakerExpenseHistoryTable
                  expenses={expenses}
                  isLoading={isLoading}
                />
              </motion.div>
            </TabsContent>

          <TabsContent value="disbursement" className="space-y-6">
            <motion.div
              key="disbursement-content"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4 }}
              className="space-y-6"
            >
              {/* Add Disbursement Form Card */}
              <CreateDisbursementForm onCreated={() => router.push("?tab=disbursement")} />

              {/* Disbursement Table Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
              >
                <DisbursementTableGM />
              </motion.div>
            </motion.div>
          </TabsContent>
          
            {/* Documents Tab */}
            <TabsContent value="documents" className="space-y-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key="documents-content"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4 }}
                >
                  <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
                    <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                            <FileText className="h-5 w-5 mr-2 text-[#00998F]" />
                            Institutional Records
                          </CardTitle>
                          <CardDescription>
                            Review and manage organization documents
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Tabs
                        defaultValue="supporting"
                        value={activeDocumentTab}
                        onValueChange={setActiveDocumentTab}
                        className="mt-2"
                      >
                        <TabsList className="grid grid-cols-2 w-full max-w-md mb-6 bg-gray-100/70 p-1 rounded-lg shadow-sm">
                          <TabsTrigger
                            value="supporting"
                            className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                          >
                            <FileCheck className="h-4 w-4" />
                            Institutional Records
                          </TabsTrigger>
                          <TabsTrigger
                            value="grant-specific"
                            className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                          >
                            <Briefcase className="h-4 w-4" />
                            Grant Specific
                          </TabsTrigger>
                        </TabsList>

                        {/* Supporting Documents Tab */}
                        <TabsContent value="supporting" className="space-y-4">
                          <AnimatePresence mode="wait">
                            <motion.div
                              key="supporting-docs"
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              transition={{ duration: 0.3 }}
                            >
                              <div className="space-y-4">
                                {granteeProfile.supportingDocuments &&
                                granteeProfile.supportingDocuments.length >
                                  0 ? (
                                  granteeProfile.supportingDocuments.map(
                                    (doc, index) => (
                                      <motion.div
                                        key={doc.id}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{
                                          duration: 0.3,
                                          delay: index * 0.05,
                                        }}
                                        whileHover={{ scale: 1.01, x: 2 }}
                                        className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50/80 transition-all duration-200 shadow-sm"
                                      >
                                        <div className="flex justify-between items-center w-full">
                                          <div className="flex items-center gap-3">
                                            {doc.attachmentType ===
                                            "financial" ? (
                                              <div className="p-2.5 bg-blue-100 text-blue-700 rounded-lg shadow-sm">
                                                <FileSpreadsheet className="h-6 w-6" />
                                              </div>
                                            ) : (
                                              <div className="p-2.5 bg-teal-100 text-[#00998F] rounded-lg shadow-sm">
                                                <FileText className="h-6 w-6" />
                                              </div>
                                            )}
                                            <div>
                                              <div className="flex items-center gap-2">
                                                <p className="font-medium text-gray-800">
                                                  {doc.attachmentTypeName}
                                                </p>
                                                {doc.status === "VERIFIED" && (
                                                  <Badge className="bg-green-100 text-green-800 border border-green-200 flex items-center gap-1 px-2 py-0.5">
                                                    <CheckCircle className="h-3 w-3" />
                                                    Verified
                                                  </Badge>
                                                )}
                                                {doc.status === "PENDING" && (
                                                  <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200 px-2 py-0.5">
                                                    <Clock className="h-3 w-3 mr-1" />
                                                    Pending
                                                  </Badge>
                                                )}
                                                {doc.status === "REJECTED" && (
                                                  <Badge className="bg-red-100 text-red-800 border border-red-200 flex items-center gap-1 px-2 py-0.5">
                                                    <XCircle className="h-3 w-3" />
                                                    Update Required
                                                  </Badge>
                                                )}
                                              </div>
                                              <p className="text-sm text-gray-500 mt-1 flex items-center">
                                                <Calendar className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                                                Uploaded on{" "}
                                                {new Date(
                                                  doc.uploadedAt,
                                                ).toLocaleDateString()}
                                              </p>
                                              {doc.remarks &&
                                                doc.status === "REJECTED" && (
                                                  <p className="text-sm text-red-600 mt-1 flex items-start">
                                                    <Info className="h-3.5 w-3.5 mr-1.5 mt-0.5" />
                                                    {doc.remarks}
                                                  </p>
                                                )}
                                            </div>
                                          </div>
                                          <div className="flex gap-2 ml-auto">
                                            {/* View Button */}
                                            <Button
                                              variant="outline"
                                              size="sm"
                                              className="text-blue-600 hover:text-blue-800 border-blue-200 hover:bg-blue-50/50 transition-all duration-200"
                                              onClick={() =>
                                                window.open(
                                                  doc.objectKey,
                                                  "_blank",
                                                )
                                              }
                                            >
                                              <FileText className="h-4 w-4 mr-1.5" />
                                              View
                                            </Button>
                                            {/* Approve/Reject only if status is PENDING */}
                                            {doc.status === "PENDING" && (
                                              <>
                                                <Button
                                                  variant="outline"
                                                  size="sm"
                                                  className="text-red-600 hover:text-red-800 border-red-200 hover:bg-red-50/50 transition-all duration-200"
                                                  onClick={() => {
                                                    setRejectedDocId(doc.id);
                                                    setRemarksDialog(true);
                                                  }}
                                                >
                                                  <XCircle className="h-4 w-4 mr-1.5" />
                                                  Reject
                                                </Button>
                                                <Button
                                                  variant="outline"
                                                  size="sm"
                                                  className="text-green-600 hover:text-green-800 border-green-200 hover:bg-green-50/50 transition-all duration-200"
                                                  onClick={() =>
                                                    handleDocumentStatusChange(
                                                      doc.id,
                                                      "VERIFIED",
                                                    )
                                                  }
                                                >
                                                  <CheckCircle className="h-4 w-4 mr-1.5" />
                                                  Approve
                                                </Button>
                                              </>
                                            )}
                                          </div>
                                        </div>
                                      </motion.div>
                                    ),
                                  )
                                ) : (
                                  <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.5 }}
                                    className="text-center p-8 bg-gray-50 rounded-lg border border-gray-100"
                                  >
                                    <div className="bg-gray-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                      <FileText className="h-8 w-8 text-gray-400" />
                                    </div>
                                    <p className="text-gray-600 font-medium">
                                      No institutional records found
                                    </p>
                                    <p className="text-gray-400 text-sm mt-1">
                                      Records will appear here once they are
                                      uploaded
                                    </p>
                                  </motion.div>
                                )}
                              </div>
                            </motion.div>
                          </AnimatePresence>
                        </TabsContent>

                        {/* Grant Specific Documents Tab */}
                        <TabsContent
                          value="grant-specific"
                          className="space-y-4"
                        >
                          <AnimatePresence mode="wait">
                            <motion.div
                              key="grant-specific-docs"
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              transition={{ duration: 0.3 }}
                            >
                              <div className="space-y-4">
                                {/* MOU Document */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.05 }}
                                  whileHover={{ scale: 1.01, x: 2 }}
                                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50/80 transition-all duration-200 shadow-sm"
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="p-2.5 bg-teal-100 text-[#00998F] rounded-lg shadow-sm">
                                      <FileText className="h-6 w-6" />
                                    </div>
                                    <div>
                                      <div className="flex items-center gap-2">
                                        <p className="font-medium text-gray-800">
                                          MOU
                                        </p>
                                      </div>
                                      <p className="text-sm text-gray-500 mt-1 flex items-center">
                                        <FileText className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                                        File type: Final_id_proof
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-blue-600 hover:text-blue-800 border-blue-200 hover:bg-blue-50/50 transition-all duration-200"
                                      onClick={() =>
                                        window.open(
                                          "/documents/mou.pdf",
                                          "_blank",
                                        )
                                      }
                                    >
                                      <FileText className="h-4 w-4 mr-1.5" />
                                      View
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-[#00998F] hover:text-teal-700 border-teal-200 hover:bg-teal-50/50 transition-all duration-200"
                                      onClick={() => {
                                        const link =
                                          document.createElement("a");
                                        link.href = "/documents/mou.pdf";
                                        link.download = "MOU.pdf";
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                      }}
                                    >
                                      <Download className="h-4 w-4 mr-1.5" />
                                      Download
                                    </Button>
                                  </div>
                                </motion.div>

                                {/* M&E Work Plan Document */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.1 }}
                                  whileHover={{ scale: 1.01, x: 2 }}
                                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50/80 transition-all duration-200 shadow-sm"
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="p-2.5 bg-teal-100 text-[#00998F] rounded-lg shadow-sm">
                                      <FileText className="h-6 w-6" />
                                    </div>
                                    <div>
                                      <div className="flex items-center gap-2">
                                        <p className="font-medium text-gray-800">
                                          M&E Work Plan
                                        </p>
                                      </div>
                                      <p className="text-sm text-gray-500 mt-1 flex items-center">
                                        <FileText className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                                        File type: Certificate_id_proof
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-blue-600 hover:text-blue-800 border-blue-200 hover:bg-blue-50/50 transition-all duration-200"
                                      onClick={() =>
                                        window.open(
                                          "/documents/me_work_plan.pdf",
                                          "_blank",
                                        )
                                      }
                                    >
                                      <FileText className="h-4 w-4 mr-1.5" />
                                      View
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-[#00998F] hover:text-teal-700 border-teal-200 hover:bg-teal-50/50 transition-all duration-200"
                                      onClick={() => {
                                        const link =
                                          document.createElement("a");
                                        link.href =
                                          "/documents/me_work_plan.pdf";
                                        link.download = "ME_Work_Plan.pdf";
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                      }}
                                    >
                                      <Download className="h-4 w-4 mr-1.5" />
                                      Download
                                    </Button>
                                  </div>
                                </motion.div>

                                {/* Project Proposal Document */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.15 }}
                                  whileHover={{ scale: 1.01, x: 2 }}
                                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50/80 transition-all duration-200 shadow-sm"
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="p-2.5 bg-teal-100 text-[#00998F] rounded-lg shadow-sm">
                                      <FileText className="h-6 w-6" />
                                    </div>
                                    <div>
                                      <div className="flex items-center gap-2">
                                        <p className="font-medium text-gray-800">
                                          Project Proposal
                                        </p>
                                      </div>
                                      <p className="text-sm text-gray-500 mt-1 flex items-center">
                                        <FileText className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                                        File type: Trust Reg_id_proof
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-blue-600 hover:text-blue-800 border-blue-200 hover:bg-blue-50/50 transition-all duration-200"
                                      onClick={() =>
                                        window.open(
                                          "/documents/project_proposal.pdf",
                                          "_blank",
                                        )
                                      }
                                    >
                                      <FileText className="h-4 w-4 mr-1.5" />
                                      View
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-[#00998F] hover:text-teal-700 border-teal-200 hover:bg-teal-50/50 transition-all duration-200"
                                      onClick={() => {
                                        const link =
                                          document.createElement("a");
                                        link.href =
                                          "/documents/project_proposal.pdf";
                                        link.download = "Project_Proposal.pdf";
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                      }}
                                    >
                                      <Download className="h-4 w-4 mr-1.5" />
                                      Download
                                    </Button>
                                  </div>
                                </motion.div>

                                {/* Project Budgets Document */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.2 }}
                                  whileHover={{ scale: 1.01, x: 2 }}
                                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50/80 transition-all duration-200 shadow-sm"
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="p-2.5 bg-blue-100 text-blue-700 rounded-lg shadow-sm">
                                      <FileSpreadsheet className="h-6 w-6" />
                                    </div>
                                    <div>
                                      <div className="flex items-center gap-2">
                                        <p className="font-medium text-gray-800">
                                          Project Budgets
                                        </p>
                                      </div>
                                      <p className="text-sm text-gray-500 mt-1 flex items-center">
                                        <FileSpreadsheet className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                                        File type: Final_tax_proof
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-blue-600 hover:text-blue-800 border-blue-200 hover:bg-blue-50/50 transition-all duration-200"
                                      onClick={() =>
                                        window.open(
                                          "/documents/project_budgets.xlsx",
                                          "_blank",
                                        )
                                      }
                                    >
                                      <FileText className="h-4 w-4 mr-1.5" />
                                      View
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-[#00998F] hover:text-teal-700 border-teal-200 hover:bg-teal-50/50 transition-all duration-200"
                                      onClick={() => {
                                        const link =
                                          document.createElement("a");
                                        link.href =
                                          "/documents/project_budgets.xlsx";
                                        link.download = "Project_Budgets.xlsx";
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                      }}
                                    >
                                      <Download className="h-4 w-4 mr-1.5" />
                                      Download
                                    </Button>
                                  </div>
                                </motion.div>

                                {/* Consolidated Sheet Document */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.25 }}
                                  whileHover={{ scale: 1.01, x: 2 }}
                                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50/80 transition-all duration-200 shadow-sm"
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="p-2.5 bg-blue-100 text-blue-700 rounded-lg shadow-sm">
                                      <FileSpreadsheet className="h-6 w-6" />
                                    </div>
                                    <div>
                                      <div className="flex items-center gap-2">
                                        <p className="font-medium text-gray-800">
                                          Consolidated Sheet
                                        </p>
                                      </div>
                                      <p className="text-sm text-gray-500 mt-1 flex items-center">
                                        <FileSpreadsheet className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                                        File type: Final_id_tax
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-blue-600 hover:text-blue-800 border-blue-200 hover:bg-blue-50/50 transition-all duration-200"
                                      onClick={() =>
                                        window.open(
                                          "/documents/consolidated_sheet.xlsx",
                                          "_blank",
                                        )
                                      }
                                    >
                                      <FileText className="h-4 w-4 mr-1.5" />
                                      View
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-[#00998F] hover:text-teal-700 border-teal-200 hover:bg-teal-50/50 transition-all duration-200"
                                      onClick={() => {
                                        const link =
                                          document.createElement("a");
                                        link.href =
                                          "/documents/consolidated_sheet.xlsx";
                                        link.download =
                                          "Consolidated_Sheet.xlsx";
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                      }}
                                    >
                                      <Download className="h-4 w-4 mr-1.5" />
                                      Download
                                    </Button>
                                  </div>
                                </motion.div>
                              </div>
                            </motion.div>
                          </AnimatePresence>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  </Card>
                </motion.div>
              </AnimatePresence>
              {/* Global remarks dialog for document rejection */}
              <Dialog open={remarksDialog} onOpenChange={setRemarksDialog}>
                <DialogContent className="sm:max-w-[600px] w-full">
                  <DialogHeader>
                    <h2 className="text-lg font-semibold">
                      Add Rejection Remarks
                    </h2>
                  </DialogHeader>
                  <textarea
                    className="w-full border border-gray-300 rounded p-2 mt-2"
                    rows={4}
                    placeholder="Enter reason for rejection"
                    value={remarks}
                    onChange={(e) => setRemarks(e.target.value)}
                  />
                  <div className="mt-4 flex justify-end gap-2">
                    <DialogClose asChild>
                      <Button variant="ghost">Cancel</Button>
                    </DialogClose>
                    <Button
                      onClick={() => {
                        if (rejectedDocId !== null) {
                          handleDocumentStatusChange(
                            rejectedDocId,
                            "REJECTED",
                            remarks,
                          );
                          setRemarksDialog(false);
                        }
                      }}
                    >
                      Submit
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </TabsContent>
            <TabsContent value="reports" className="space-y-6">
              <motion.div
                key="reports-content"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
              >
                <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
                  <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                          {/* You can replace FileText with an icon relevant to reports */}
                          <FileText className="h-5 w-5 mr-2 text-[#00998F]" />
                          Reports
                        </CardTitle>
                        <CardDescription>
                          View narrative reports and gallery
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Tabs
                      defaultValue="quantitative"
                      value={activeReportTab}
                      onValueChange={setActiveReportTab}
                      className="mt-2"
                    >
                      <TabsList className="grid grid-cols-3 w-full mb-6 bg-gray-100/70 p-1 rounded-lg shadow-sm">
                        <TabsTrigger
                          value="narrative"
                          className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                        >
                          <FileCheck className="h-4 w-4" />
                          Narrative Reports
                        </TabsTrigger>

                        <TabsTrigger
                          value="quantitative"
                          className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                        >
                          <ChartAreaIcon className="h-4 w-4" />
                          Quantitative Reports
                        </TabsTrigger>

                        <TabsTrigger
                          value="gallery"
                          className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                        >
                          <ImageIcon className="h-4 w-4" />
                          Gallery
                        </TabsTrigger>
                      </TabsList>

                      {/* Narrative Reports Tab Content */}
                      <TabsContent value="narrative" className="space-y-4">
                        <motion.div
                          key="narrative-reports-content"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.3 }}
                        >
                          <GranteeReportsTab granteeId={id} />
                        </motion.div>
                      </TabsContent>

                      {/* Narrative Reports Tab Content */}
                      <TabsContent value="quantitative" className="space-y-4">
                        <motion.div
                          key="narrative-reports-content"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.3 }}
                        >
                          <QuantitativeReports granteeId={id} />
                        </motion.div>
                      </TabsContent>

                      {/* Gallery Tab Content */}
                      <TabsContent value="gallery" className="space-y-4">
                        <motion.div
                          key="gallery-content"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.3 }}
                        >
                          <GranteeGalleryTab granteeId={Number(id)} />
                        </motion.div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
            <TabsContent value="approvals" className="space-y-6">
              <motion.div
                key="reports-content"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
              >
                <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
                  <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                  <CardContent>
                    <Tabs
                      defaultValue="narrative"
                      value={activeReportTab}
                      onValueChange={setActiveReportTab}
                      className="mt-2"
                    >
                      <TabsList className="grid grid-cols-2 w-full max-w-md mb-6 bg-gray-100/70 p-1 rounded-lg shadow-sm">
                        <TabsTrigger
                          value="narrative"
                          className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                        >
                          <FileText className="h-4 w-4" />
                          Narrative Reports
                        </TabsTrigger>
                        <TabsTrigger
                          value="expenses"
                          className="flex items-center gap-2 py-2.5 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#00998F] data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                        >
                          <Wallet className="h-4 w-4" />
                          Expenses
                        </TabsTrigger>
                      </TabsList>

                      {/* Narrative Reports Tab Content */}
                      <TabsContent value="narrative" className="space-y-4">
                        <AnimatePresence mode="wait">
                          <motion.div
                            key="narrative-reports-content"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.3 }}
                          >
                            <PendingReportsTab granteeId={id} />
                          </motion.div>
                        </AnimatePresence>
                      </TabsContent>

                      {/* Expenses Tab Content */}
                      <TabsContent value="expenses" className="space-y-6">
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.4 }}
                        >
                          <h1>hi</h1>
                        </motion.div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </Layout>
  );
}

// The Wallet icon is already imported from lucide-react at the top of the file
