import { ActivityType, OutcomeType, OutputType } from "@/types/quantitative";

export const quarterKeyPairs = [
  {
    quarter: "Q1",
    plan: "q1_plan",
    actual: "q1_actual",
    remark: "q1_remark",
  },
  {
    quarter: "Q2",
    plan: "q2_plan",
    actual: "q2_actual",
    remark: "q2_remark",
  },
  {
    quarter: "Q3",
    plan: "q3_plan",
    actual: "q3_actual",
    remark: "q3_remark",
  },
  {
    quarter: "Q4",
    plan: "q4_plan",
    actual: "q4_actual",
    remark: "q4_remark",
  },
] as const satisfies ReadonlyArray<{
  quarter: string;
  plan: keyof OutputType;
  actual: keyof OutputType;
  remark: keyof OutputType;
}>;

export const plannedKeys = quarterKeyPairs.map((e) => e.plan);
export const actualKeys = quarterKeyPairs.map((e) => e.actual);
export const remarkKeys = quarterKeyPairs.map((e) => e.remark);

export function getQuarterTotal<T extends OutputType | ActivityType>(
  output: T,
  keys: readonly (keyof T)[],
): number | null {
  const values = keys.map((key) => {
    const val = output[key];
    const num = Number(val);
    return val != null && !isNaN(num) ? num : null;
  });

  const filtered = values.filter((v): v is number => v !== null);
  if (filtered.length === 0) return null;

  return filtered.reduce((sum, val) => sum + val, 0);
}

export const calculateImpactProgress = (outcomes: OutcomeType[]) => {
  if (outcomes.length === 0) return 0;
  const totalProgress = outcomes.reduce(
    (sum, outcome) => sum + calculateOutcomeProgress(outcome),
    0,
  );
  return totalProgress / outcomes.length;
};

export const calculateOutcomeProgress = (outcome: OutcomeType) => {
  if (!outcome.outputs || outcome.outputs.length === 0) return 0;

  const totalProgress = outcome.outputs.reduce(
    (sum: number, output: OutputType) => sum + calculateOutputProgress(output),
    0,
  );

  return totalProgress / outcome.outputs.length;
};

export function calculateOutputProgress<T extends OutputType | ActivityType>(
  output: T,
) {
  const totalPlanned = getQuarterTotal(output, plannedKeys);
  const totalActual = getQuarterTotal(output, actualKeys);

  if (!(totalPlanned && totalActual)) return 0;

  return totalPlanned > 0 ? (totalActual / totalPlanned) * 100 : 0;
}
