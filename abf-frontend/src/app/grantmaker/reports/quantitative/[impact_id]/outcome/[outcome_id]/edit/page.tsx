"use client";

import Layout from "@/components/grantmaker/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  OutcomeSchema,
  OutputSchema,
  ActivitySchema,
  OutcomeType,
  OutcomeTypeWithImpact,
} from "@/types/quantitative";
import { zodResolver } from "@hookform/resolvers/zod";
import { Control, useFieldArray, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import {
  actualKeys,
  plannedKeys,
  quarterKeyPairs,
  remarkKeys,
} from "../../../../../_lib/quantitative";
import { Fragment } from "react";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CirclePlusIcon, PlusIcon, SaveIcon, Undo2Icon } from "lucide-react";
import { notFound, useParams, useRouter } from "next/navigation";
import React from "react";
import apiClient from "@/lib/apiClient";
import { toast } from "sonner";
import { getGrantmakerOutcome } from "@/services/grantmaker/quantitative-service";
import { useQuery } from "@tanstack/react-query";

const newActivitiesSchema = ActivitySchema.omit({
  id: true,
}).extend({
  id: z.number().optional(),
});

const newOutputSchema = OutputSchema.omit({
  id: true,
  activities: true,
}).extend({
  id: z.number().optional(),
  activities: z.array(newActivitiesSchema),
});

const newOutcomeSchema = OutcomeSchema.omit({
  id: true,
}).extend({
  outputs: z.array(newOutputSchema),
});

type NewOutcomeType = z.infer<typeof newOutcomeSchema>;

export default function () {
  const router = useRouter();
  const params = useParams();

  const id = Number(params.impact_id);
  const outcome_id = params.outcome_id;

  const outcomeQuery = useQuery<OutcomeTypeWithImpact | undefined, Error>({
    queryKey: ["outcomes", id],
    queryFn: async () => {
      const outcomes = await getGrantmakerOutcome(id.toString());
      form.reset(outcomes);
      return outcomes;
    },
  });

  if (!outcomeQuery.isLoading && !outcomeQuery.data) return notFound();

  const form = useForm<NewOutcomeType>({
    defaultValues: {
      ...outcomeQuery.data,
    },
    resolver: zodResolver(newOutcomeSchema),
  });

  const outputsArray = useFieldArray({
    control: form.control,
    name: "outputs",
  });

  const onSubmit = async (values: NewOutcomeType) => {
    toast.promise(
      apiClient.patch(
        `/api/reports/grantmaker/quantitative/outcomes/${outcome_id}/`,
        {
          ...values,
          impact_id: id,
        },
      ),
      {
        loading: `Editing ${values.outcome_statement}`,
        success: (data: any) => {
          // router.back();
          return `Edited ${values.outcome_statement}`;
        },
        error: `Failed to edit ${values.outcome_statement}`,
      },
    );
  };

  const TotalDisplay: React.FC<{
    control: Control<any>;
    name: string;
    keys: string[];
    className?: string;
  }> = ({ control, name, keys, className = "" }) => {
    const watchedValues = useWatch({
      control,
      name: name,
    });

    const total = React.useMemo(() => {
      if (!watchedValues) return 0;

      return keys.reduce((sum, key) => {
        const value = parseFloat(watchedValues[key]) || 0;
        return sum + value;
      }, 0);
    }, [watchedValues, keys]);

    return total;
  };

  function ActivityForm({ outputIndex }: { outputIndex: number }) {
    const activityArray = useFieldArray({
      control: form.control,
      name: `outputs.${outputIndex}.activities`,
    });

    return (
      <div className="grid">
        <h3 className="text-2xl pt-6 ml-10 font-semibold tracking-tight mb-3">
          Activities
        </h3>

        {activityArray.fields.map((e, i) => (
          <div key={i}>
            <div className="flex gap-2 items-center relative">
              <div className="absolute size-8 bg-muted border rounded-2xl grid place-items-center">
                {i + 1}
              </div>
              <Separator />
            </div>

            <div className="space-y-3 border-l ml-4 px-4 py-4">
              <div className="grid grid-cols-[1fr_auto] gap-2">
                <FormField
                  control={form.control}
                  name={`outputs.${outputIndex}.activities.${i}.description`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`outputs.${outputIndex}.activities.${i}.unit`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-transparent border-transparent">
                    <TableHead className="p-0 h-fit"></TableHead>
                    {quarterKeyPairs.map((q) => (
                      <TableHead
                        key={q.quarter}
                        className="text-center p-0 h-fit text-muted-foreground"
                      >
                        {q.quarter}
                      </TableHead>
                    ))}
                    <TableHead>Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow className="hover:bg-transparent">
                    <TableCell className="h-fit min-w-[100px]">Plan</TableCell>
                    {plannedKeys.map((e) => (
                      <TableCell key={e} className="py-1">
                        <FormField
                          control={form.control}
                          name={`outputs.${outputIndex}.activities.${i}.${e}`}
                          render={({ field }) => (
                            <FormItem className="grid place-items-center">
                              <FormControl>
                                <Input
                                  className="text-center"
                                  type="number"
                                  {...field}
                                  value={field.value ?? 0}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TableCell>
                    ))}
                    <TableCell className="font-semibold text-center">
                      <TotalDisplay
                        name={`outputs.${outputIndex}.activities.${i}`}
                        control={form.control}
                        keys={plannedKeys}
                      />
                    </TableCell>
                  </TableRow>

                  <TableRow className="hover:bg-transparent">
                    <TableCell className="h-fit min-w-[100px]">
                      Actual
                    </TableCell>
                    {actualKeys.map((e) => (
                      <TableCell key={e} className="py-1">
                        <FormField
                          control={form.control}
                          name={`outputs.${outputIndex}.activities.${i}.${e}`}
                          render={({ field }) => (
                            <FormItem className="grid place-items-center">
                              <FormControl>
                                <Input
                                  className="text-center"
                                  type="number"
                                  {...field}
                                  value={field.value ?? 0}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TableCell>
                    ))}
                    <TableCell className="font-semibold text-center">
                      <TotalDisplay
                        name={`outputs.${outputIndex}.activities.${i}`}
                        control={form.control}
                        keys={actualKeys}
                      />
                    </TableCell>
                  </TableRow>

                  <TableRow className="hover:bg-transparent">
                    <TableCell className="h-fit min-w-[100px]">
                      Remarks
                    </TableCell>
                    {remarkKeys.map((e) => (
                      <TableCell key={e} className="py-1">
                        <FormField
                          control={form.control}
                          name={`outputs.${outputIndex}.activities.${i}.${e}`}
                          render={({ field }) => (
                            <FormItem className="grid place-items-center">
                              <FormControl>
                                <Input
                                  className="text-center"
                                  {...field}
                                  value={field.value ?? ""}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TableCell>
                    ))}
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        ))}

        <Button
          type="button"
          size="icon"
          // className="size-6"
          onClick={() => {
            activityArray.append({
              description:
                process.env.NODE_ENV === "development"
                  ? "Training of counsellors in RISE curriculum"
                  : "",
              unit: process.env.NODE_ENV === "development" ? "counsellors" : "",
              q1_plan: process.env.NODE_ENV === "development" ? 10 : 0,
              q1_actual: process.env.NODE_ENV === "development" ? 0 : 0,
              q2_plan: process.env.NODE_ENV === "development" ? 20 : 0,
              q2_actual: process.env.NODE_ENV === "development" ? 20 : 0,
              q3_plan: process.env.NODE_ENV === "development" ? 0 : 0,
              q3_actual: process.env.NODE_ENV === "development" ? 30 : 0,
              q4_plan: process.env.NODE_ENV === "development" ? 0 : 0,
              q4_actual: process.env.NODE_ENV === "development" ? 0 : 0,
              means_of_verification: "",
              remarks: "",
            });
          }}
        >
          <PlusIcon />
        </Button>
      </div>
    );
  }

  return (
    <Layout title="New Outcome">
      <div className="max-w-4xl py-8 mx-auto ">
        <Button variant="ghost" onClick={() => router.back()}>
          <Undo2Icon />
          Back
        </Button>
        <div className="mb-8 mt-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Edit Outcome
          </h1>
          <p className="text-gray-600">
            Complete your outcome report with detailed activities and outcomes.
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="py-2 grid gap-2">
              <FormField
                control={form.control}
                name="outcome_statement"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Statement</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pre_intervention_assessment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pre Intervention Assesment</FormLabel>
                    <FormControl>
                      <Textarea {...field} value={field.value ?? ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="post_intervention_assessment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Post Intervention Assement</FormLabel>
                    <FormControl>
                      <Textarea {...field} value={field.value ?? ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tools_used"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tools Used</FormLabel>
                    <FormControl>
                      <Textarea {...field} value={field.value ?? ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-6 pt-2">
                <h2 className="border-b mb-6 pb-2 text-3xl font-semibold  tracking-tight mt-6">
                  Outputs
                </h2>
                {outputsArray.fields.map((e, i) => (
                  <Card key={i}>
                    <CardHeader>
                      <CardTitle>
                        <Badge>Output {i + 1}</Badge>
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-[1fr_auto] gap-2">
                        <FormField
                          name={`outputs.${i}.description`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Statement</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`outputs.${i}.unit`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Unit</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      <Table className="rounded-xl overflow-hidden">
                        <TableHeader>
                          <TableRow className="border-transparent hover:bg-transparent">
                            <TableHead className="h-fit p-0"></TableHead>
                            {quarterKeyPairs.map((q) => (
                              <TableHead
                                key={q.quarter}
                                className="text-center h-fit text-muted-foreground"
                              >
                                {q.quarter}
                              </TableHead>
                            ))}
                            <TableHead>Total</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow className="hover:bg-transparent">
                            <TableCell className="min-w-[100px] font-medium">
                              Planned
                            </TableCell>

                            {plannedKeys.map((e) => (
                              <TableCell key={e} className="h-fit py-1">
                                <FormField
                                  name={`outputs.${i}.${e}`}
                                  render={({ field }) => (
                                    <FormItem className="grid place-items-center">
                                      <FormControl>
                                        <Input
                                          className="text-center"
                                          type="number"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </TableCell>
                            ))}
                            <TableCell className="font-semibold text-center">
                              <TotalDisplay
                                name={`outputs.${i}`}
                                control={form.control}
                                keys={plannedKeys}
                              />
                            </TableCell>
                          </TableRow>

                          <TableRow className="hover:bg-transparent">
                            <TableCell className="min-w-[100px] font-medium">
                              Actual
                            </TableCell>

                            {actualKeys.map((e) => (
                              <TableCell key={e} className="h-fit py-1">
                                <FormField
                                  name={`outputs.${i}.${e}`}
                                  render={({ field }) => (
                                    <FormItem className="grid place-items-center">
                                      <FormControl>
                                        <Input
                                          className="text-center"
                                          type="number"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </TableCell>
                            ))}
                            <TableCell className="font-semibold text-center">
                              <TotalDisplay
                                name={`outputs.${i}`}
                                control={form.control}
                                keys={actualKeys}
                              />
                            </TableCell>
                          </TableRow>

                          <TableRow className="hover:bg-transparent">
                            <TableCell className="min-w-[100px] font-medium">
                              Remarks
                            </TableCell>

                            {remarkKeys.map((e) => (
                              <TableCell key={e} className="h-fit py-1">
                                <FormField
                                  name={`outputs.${i}.${e}`}
                                  render={({ field }) => (
                                    <FormItem className="grid place-items-center">
                                      <FormControl>
                                        <Input
                                          className="text-center"
                                          {...field}
                                          value={field.value ?? ""}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableBody>
                      </Table>

                      <Separator />

                      <ActivityForm outputIndex={i} />
                    </CardContent>
                  </Card>
                ))}

                <Button
                  type="button"
                  onClick={() =>
                    outputsArray.append({
                      description:
                        process.env.NODE_ENV === "development"
                          ? "Cadre of trained 37 counsellors available in every EMRS"
                          : "",
                      unit:
                        process.env.NODE_ENV === "development"
                          ? "counsellors"
                          : "",
                      q1_plan: process.env.NODE_ENV === "development" ? 20 : 0,
                      q1_actual: process.env.NODE_ENV === "development" ? 0 : 0,
                      q2_plan: process.env.NODE_ENV === "development" ? 30 : 0,
                      q2_actual:
                        process.env.NODE_ENV === "development" ? 20 : 0,
                      q3_plan: process.env.NODE_ENV === "development" ? 0 : 0,
                      q3_actual:
                        process.env.NODE_ENV === "development" ? 30 : 0,
                      q4_plan: process.env.NODE_ENV === "development" ? 0 : 0,
                      q4_actual: process.env.NODE_ENV === "development" ? 0 : 0,
                      remarks: "",
                      means_of_verification: "",
                      activities: [
                        {
                          description:
                            process.env.NODE_ENV === "development"
                              ? "Training of counsellors in RISE curriculum"
                              : "",
                          unit:
                            process.env.NODE_ENV === "development"
                              ? "counsellors"
                              : "",
                          q1_plan:
                            process.env.NODE_ENV === "development" ? 10 : 0,
                          q1_actual:
                            process.env.NODE_ENV === "development" ? 0 : 0,
                          q2_plan:
                            process.env.NODE_ENV === "development" ? 20 : 0,
                          q2_actual:
                            process.env.NODE_ENV === "development" ? 20 : 0,
                          q3_plan:
                            process.env.NODE_ENV === "development" ? 0 : 0,
                          q3_actual:
                            process.env.NODE_ENV === "development" ? 30 : 0,
                          q4_plan:
                            process.env.NODE_ENV === "development" ? 0 : 0,
                          q4_actual:
                            process.env.NODE_ENV === "development" ? 0 : 0,
                          means_of_verification: "",
                          remarks: "",
                        },
                      ],
                    })
                  }
                >
                  <CirclePlusIcon />
                  Output
                </Button>
              </div>
              <Separator />
              <Button
                size="lg"
                className="bg-teal-500 place-self-end"
                type="submit"
              >
                <SaveIcon />
                Submit
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </Layout>
  );
}
