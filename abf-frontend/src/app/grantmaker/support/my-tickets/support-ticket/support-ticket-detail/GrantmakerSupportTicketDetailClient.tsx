"use client";

import { useEffect, useRef, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import SupportTabsLayout from "../../../SupportTabsLayout";
import { Button } from "@/components/ui/button";
import { ArrowDown, Paperclip, AlertCircle, Shield, User, MessageSquare, Info, ShieldCheck, Tag, Folder, Flag, CalendarClock, Clock } from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import {
  getTicketDetails,
  getTicketUpdates,
  sendTicketUpdate,
  updateTicketStatus,
} from "@/services/supportTicket.service";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function GrantmakerSupportTicketDetailClient() {
  const router = useRouter();
  const id = Number(useSearchParams().get("id"));

  const chatRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateIdRef = useRef<number | null>(null);
  const lastStatusCountRef = useRef<number | null>(null);
  const lastSentMessageIdRef = useRef<number | null>(null);

  const [ticket, setTicket] = useState<any>(null);
  const [timeline, setTimeline] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [isSending, setIsSending] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [newMessageArrived, setNewMessageArrived] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);

  const isClosed = ticket?.status === "resolved";

  const scrollToBottom = () => {
    setTimeout(() => {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: "smooth" });
      setNewMessageArrived(false);
    }, 100);
  };

  const buildTimeline = (ticketDetail: any, updates: any[]) => {
    const timelineItems: any[] = [];

    if (ticketDetail?.description) {
      timelineItems.push({
        type: "message",
        id: "desc",
        update_text: ticketDetail.description,
        updated_at: ticketDetail.created_at,
        user: {
          type: { code: "GRANTEE" },
          first_name: ticketDetail.created_by_first_name,
          last_name: ticketDetail.created_by_last_name,
          email: ticketDetail.created_by_email,
        },
        attachments: ticketDetail.attachments || [],
      });
    }

    ticketDetail?.status_history?.forEach((s: any, i: number) => {
      timelineItems.push({
        type: "status",
        id: `status-${s.id}-${i}`,
        updated_at: s.changed_at,
        changed_by: s.changed_by,
        to_status: s.to_status,
      });
    });

    updates.forEach((msg: any) => {
      timelineItems.push({ type: "message", ...msg });
    });

    timelineItems.sort((a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime());
    setTimeline(timelineItems);
  };

  useEffect(() => {
    if (!id) return;
    (async () => {
      try {
        const [ticketDetail, updates] = await Promise.all([
          getTicketDetails(String(id)),
          getTicketUpdates(id),
        ]);
        setTicket(ticketDetail);
        buildTimeline(ticketDetail, updates);
        lastUpdateIdRef.current = updates.at(-1)?.id || null;
        lastStatusCountRef.current = ticketDetail.status_history?.length || 0;
        scrollToBottom();
      } catch {
        toast.error("Ticket not found or already cancelled.");
        router.replace("/grantmaker/support/my-tickets");
      }
    })();
  }, [id, router]);

  useEffect(() => {
    pollingRef.current = setInterval(async () => {
      try {
        const [latestTicket, updates] = await Promise.all([
          getTicketDetails(String(id)),
          getTicketUpdates(id),
        ]);

        const newUpdateId = updates.at(-1)?.id || null;
        const newStatusCount = latestTicket.status_history?.length || 0;

        const hasNewUpdate = newUpdateId !== lastUpdateIdRef.current;
        const hasNewStatus = newStatusCount !== lastStatusCountRef.current;

        if (hasNewUpdate || hasNewStatus) {
          lastUpdateIdRef.current = newUpdateId;
          lastStatusCountRef.current = newStatusCount;
          setTicket(latestTicket);
          buildTimeline(latestTicket, updates);

          const isAtBottom =
            chatRef.current &&
            chatRef.current.scrollHeight - chatRef.current.scrollTop - chatRef.current.clientHeight < 100;

          if (hasNewUpdate) {
            const latestMessage = updates.at(-1);
            const latestMessageId = latestMessage?.id;
            const latestMessageUserEmail = latestMessage?.user?.email;

            const isOwnMessage =
              latestMessageId === lastSentMessageIdRef.current ||
              latestMessageUserEmail === ticket?.created_by_email;

            if (!isOwnMessage) {
              isAtBottom ? scrollToBottom() : setNewMessageArrived(true);
              toast.success("New message received");
            }

            lastSentMessageIdRef.current = null;
          }
        }
      } catch {}
    }, 2000);

    return () => clearInterval(pollingRef.current!);
  }, [ticket?.id, id]);

  useEffect(() => {
    const el = chatRef.current;
    if (!el) return;
    const onScroll = () => {
      const isAtBottom = el.scrollHeight - el.scrollTop - el.clientHeight < 100;
      setShowScrollButton(!isAtBottom);
      if (isAtBottom) setNewMessageArrived(false);
    };
    el.addEventListener("scroll", onScroll);
    return () => el.removeEventListener("scroll", onScroll);
  }, []);

  const handleSendMessage = async () => {
    if ((!newMessage.trim() && !file) || isClosed) return;

    try {
      setIsSending(true);
      const newUpdate = await sendTicketUpdate(id, newMessage, file);
      lastSentMessageIdRef.current = newUpdate.id;
      buildTimeline(ticket, [...timeline.filter(t => t.type === "message" && t.id !== "desc"), newUpdate]);
      setNewMessage("");
      setFile(null);
      if (fileInputRef.current) fileInputRef.current.value = "";
      scrollToBottom();
    } catch {
      toast.error("Failed to send message.");
    } finally {
      setIsSending(false);
    }
  };

  const handleStatusChange = async () => {
    try {
      setIsUpdatingStatus(true);
      const newStatus = ticket.status === "resolved" ? "under review" : "resolved";
      const updated = await updateTicketStatus(id, newStatus);
      setTicket((prev: any) => ({ ...prev, status: updated.status }));
    } catch {
      toast.error("Failed to update status.");
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status?.toLowerCase().replace(/\s+/g, "-")) {
      case "open":
        return "bg-gradient-to-r from-teal-50 to-teal-100 text-teal-800 border-teal-200 shadow-sm";
      case "under-review":
        return "bg-gradient-to-r from-amber-50 to-orange-100 text-amber-800 border-amber-200 shadow-sm";
      case "resolved":
        return "bg-gradient-to-r from-emerald-50 to-green-100 text-emerald-800 border-emerald-200 shadow-sm";
      default:
        return "bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 border-gray-200 shadow-sm";
    }
  };

  const renderStatusChange = (item: any, idx: number) => {
    const date = format(new Date(item.updated_at), "dd MMM yyyy, HH:mm");
    return (
      <div
        key={item.id}
        className="text-center text-sm text-gray-500 my-4"
        style={{ animation: `fadeIn 0.4s ease-in-out ${idx * 0.05}s` }}
      >
        Status changed to{" "}
        <span
          className={`inline-flex items-center px-3 py-1 text-xs font-medium border rounded-lg ${getStatusBadgeStyle(
            item.to_status
          )}`}
        >
          {item.to_status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
        </span>{" "}
        - {date}
      </div>
    );
  };

  const renderMessage = (msg: any, idx: number) => {
    const isGM = msg.user?.type?.code === "GRANT_MAKER";
    const fullName = [msg.user?.first_name, msg.user?.last_name].filter(Boolean).join(" ") || msg.user?.email;

    return (
      <div
        key={msg.id}
        className={`flex ${isGM ? "justify-end" : "justify-start"} transition-all duration-300 ease-in-out rounded-lg p-2`}
        style={{ animation: `fadeIn 0.4s ease-in-out ${idx * 0.05}s` }}
      >
        <div
          className={`rounded-xl px-4 py-3 text-sm max-w-md shadow-lg border transition-all duration-300 ease-in-out hover:shadow-xl ${
            isGM
              ? "bg-gradient-to-br from-teal-600 to-teal-700 text-white border-teal-600"
              : "bg-gradient-to-br from-blue-50 to-white text-gray-700 border-blue-200"
          }`}
        >
          <div className={`font-medium mb-3 ${isGM ? "text-teal-100" : "text-blue-700"}`}>
            <div className="flex items-center gap-2 mb-1">
              {isGM ? (
                <Shield className="w-4 h-4" />
              ) : (
                <User className="w-4 h-4" />
              )}
              {fullName}
            </div>
            <div className={`text-xs ${isGM ? "text-teal-200" : "text-blue-500"}`}>
              {msg.updated_at && formatDistanceToNow(new Date(msg.updated_at), { addSuffix: true })}
            </div>
          </div>
          {msg.id === "desc" && <p className={`font-medium mb-2 ${isGM ? "text-white" : "text-gray-800"}`}>{ticket?.title}</p>}
          <p className="whitespace-pre-line leading-relaxed mb-2">{msg.update_text}</p>
          {msg.attachments?.length > 0 && (
            <div className="mt-2">
              {msg.attachments.map((url: string, i: number) => (
                <a
                  key={i}
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`inline-flex items-center gap-1 text-xs underline mt-2 p-2 rounded-lg transition-colors duration-200 ${
                    isGM
                      ? "text-teal-100 hover:text-white bg-teal-800 hover:bg-teal-700"
                      : "text-blue-600 hover:text-blue-800 bg-blue-100 hover:bg-blue-200"
                  }`}
                >
                  <Paperclip className="w-3 h-3" />
                  {new URL(url).pathname.split("/").pop()?.split("?")[0]}
                </a>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderPriorityBadge = (priority: string) => {
    const styles: Record<string, string> = {
      low: "bg-gray-100 text-gray-700 border-gray-50 border-opacity-50",
      medium: "bg-amber-100 text-amber-800 border-amber-50 border-opacity-50",
      high: "bg-orange-100 text-orange-800 border-amber-50 border-opacity-50",
      urgent: "bg-red-100 text-red-800 border-amber-50 border-opacity-50",
    };
    return (
      <span
        className={`text-xs font-medium px-3 py-1 rounded-lg ${
          styles[priority?.toLowerCase()] || "bg-gray-100 text-gray-600 border-gray-50 border-opacity-50"
        }`}
      >
        {priority?.replace(/\b\w/g, (l) => l.toUpperCase())}
      </span>
    );
  };

  return (
    <SupportTabsLayout>
      <div className="w-full h-[calc(100vh-64px)] overflow-hidden my-0 py-0">
        <Card className="bg-white border-none rounded-lg h-full flex flex-col p-0 m-0">
          <CardContent className="flex-1 overflow-hidden flex flex-col lg:flex-row gap-6 h-full p-0">
            <div className="lg:w-2/3 flex flex-col">
              <div className="px-6 py-5 bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
                <div className="flex flex-col space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
                        <MessageSquare className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h1 className="text-xl font-bold text-gray-900">Ticket #{ticket?.id}</h1>
                        <p className="text-sm text-gray-600">Support Conversation</p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => router.push("/grantmaker/support/my-tickets/support-chat")}
                      className="bg-gray-800 hover:bg-gray-900 text-white rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105"
                    >
                      ← Back to Chat
                    </Button>
                  </div>
                  {ticket && (
                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Grant Program</p>
                          <p className="text-sm font-semibold text-gray-900 mt-1">{ticket.grant_name || "N/A"}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Issue Title</p>
                          <p className="text-sm font-semibold text-gray-900 mt-1">{ticket.title}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <section
                ref={chatRef}
                className="flex-1 overflow-y-auto p-6 space-y-4 bg-gradient-to-br from-gray-50 via-white to-teal-50 border-x border-gray-200"
              >
                {timeline.length > 0 ? (
                  timeline.map((item, idx) =>
                    item.type === "status" ? renderStatusChange(item, idx) : renderMessage(item, idx)
                  )
                ) : (
                  <div
                    className="p-8 text-center text-gray-600"
                    style={{ animation: `fadeIn 0.4s ease-in-out` }}
                  >
                    <AlertCircle className="w-8 h-8 mb-3 mx-auto text-gray-400" />
                    <p className="text-sm font-medium text-gray-700">No messages available.</p>
                  </div>
                )}
              </section>

              {newMessageArrived && showScrollButton && (
                <div className="absolute bottom-28 right-6 animate-pulse">
                  <Button
                    onClick={scrollToBottom}
                    size="sm"
                    className="bg-gray-800 hover:bg-gray-900 text-white rounded-full px-4 py-2 text-xs font-medium shadow-md transition-all duration-300 ease-in-out hover:scale-110"
                  >
                    New messages <ArrowDown className="w-4 h-4 ml-1" />
                  </Button>
                </div>
              )}

              <footer className="p-4 border border-gray-200 bg-white rounded-b-xl">
                {isClosed ? (
                  <p className="text-center text-sm text-gray-600">This ticket is resolved. You cannot send new messages.</p>
                ) : (
                  <div className="flex items-center gap-4 ">
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="group"
                      title="Attach file"
                    >
                      <Paperclip className="w-6 h-6 text-gray-400 group-hover:text-gray-600 transition-all duration-300 ease-in-out hover:scale-110" />
                    </button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      onChange={(e) => setFile(e.target.files?.[0] || null)}
                      className="hidden"
                    />
                    {file && (
                      <span className="text-xs text-gray-600 truncate max-w-[160px] bg-gray-50 px-3 py-1.5 rounded-lg border border-gray-100 border-opacity-50">
                        {file.name}
                      </span>
                    )}
                    <textarea
                      placeholder="Type your message... (Press Enter to send, Shift+Enter for new line)"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          if (!isSending && (newMessage.trim() || file)) {
                            handleSendMessage();
                          }
                        }
                      }}
                      className="flex-grow mx-5 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-700 placeholder:text-gray-400 bg-gray-50 focus:outline-none focus:border-gray-400 focus:ring-2 focus:ring-gray-200 transition-all duration-300 ease-in-out min-h-[80px] max-h-[200px] resize-y hover:shadow-sm focus:shadow-md focus:bg-white focus:scale-[1.02]"
                    />
                    <Button
                      onClick={handleSendMessage}
                      disabled={isSending}
                      className="bg-gray-800 m-2 hover:bg-gray-900 text-white px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 disabled:bg-gray-300 disabled:text-gray-500 disabled:scale-100"
                    >
                      {isSending ? "Sending..." : "Send"}
                    </Button>
                  </div>
                )}
              </footer>
            </div>

            <aside className="lg:w-1/3">
              <Card className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <CardHeader className="pb-4 bg-gradient-to-r from-gray-50 to-white rounded-t-lg">
                  <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <Info className="w-5 h-5 text-gray-600" />
                    Ticket Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-sm space-y-4">
                  {ticket && (
                    <>
                      <div className="space-y-4">
                        {[
                          {
                            icon: <ShieldCheck className="w-4 h-4" />,
                            label: "Status",
                            value: (
                              <span
                                className={`inline-flex items-center px-3 py-1 text-xs font-medium border rounded-lg ${getStatusBadgeStyle(
                                  ticket.status
                                )}`}
                              >
                                {ticket.status?.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                              </span>
                            )
                          },
                          {
                            icon: <Tag className="w-4 h-4" />,
                            label: "Title",
                            value: <span className="font-medium text-gray-800">{ticket.title}</span>
                          },
                          {
                            icon: <Folder className="w-4 h-4" />,
                            label: "Grant Program",
                            value: <span className="font-medium text-gray-800">{ticket.grant_name || "N/A"}</span>
                          },
                          {
                            icon: <Flag className="w-4 h-4" />,
                            label: "Priority",
                            value: ticket.priority && renderPriorityBadge(ticket.priority)
                          },
                          {
                            icon: <User className="w-4 h-4" />,
                            label: "Point of Contact",
                            value: <span className="text-gray-700">{ticket.point_of_contact_name || "N/A"}</span>
                          },
                          {
                            icon: <CalendarClock className="w-4 h-4" />,
                            label: "Created",
                            value: <span className="text-gray-700">{format(new Date(ticket.created_at), "dd MMM yyyy")}</span>
                          },
                          {
                            icon: <Clock className="w-4 h-4" />,
                            label: "Updated",
                            value: <span className="text-gray-700">{format(new Date(ticket.updated_at), "dd MMM yyyy")}</span>
                          }
                        ].map((item, i) => (
                          item.value && (
                            <div key={i} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg" style={{ animation: `fadeIn 0.4s ease-in-out ${i * 0.05}s` }}>
                              <div className="flex items-center gap-2 text-gray-600">
                                {item.icon}
                                <span className="font-medium">{item.label}</span>
                              </div>
                              <div className="text-right">
                                {item.value}
                              </div>
                            </div>
                          )
                        ))}
                      </div>
                      <Button
                        onClick={handleStatusChange}
                        disabled={isUpdatingStatus}
                        className="w-full mt-4 bg-gray-800 hover:bg-gray-900 text-white rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 disabled:bg-gray-300 disabled:text-gray-500 disabled:scale-100"
                      >
                        {isUpdatingStatus
                          ? ticket.status === "resolved"
                            ? "Reopening..."
                            : "Closing..."
                          : ticket.status === "resolved"
                          ? "Reopen Ticket"
                          : "Close Ticket"}
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            </aside>
          </CardContent>
        </Card>
      </div>
    </SupportTabsLayout>
  );
}