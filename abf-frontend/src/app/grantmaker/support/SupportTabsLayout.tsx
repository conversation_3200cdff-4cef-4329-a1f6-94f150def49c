"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Layout from "@/components/grantmaker/Layout";

const tabs = [
  { name: "My Tickets", path: "/grantmaker/support/my-tickets/support-ticket" },
  { name: "Chat History", path: "/grantmaker/support/my-tickets/support-chat" },
  { name: "FAQs", path: "/grantmaker/articles-and-faq" },
  { name: "Resources", path: "/grantmaker/resources" },
];

export default function SupportTabsLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [activePath, setActivePath] = useState<string | null>(null);

  useEffect(() => {
    setActivePath(pathname);
  }, [pathname]);

  const isActiveTab = (tabPath: string) => {
    return activePath?.startsWith(tabPath);
  };

  return (
    <Layout>
      <div className="h-full flex flex-col bg-white m-0 p-0">
        {/* Tabs Header */}
        <div className="bg-white border-b border-gray-200 m-0">
          <div className="w-full px-6 py-3">
            <div className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.name}
                  onClick={() => router.push(tab.path)}
                  className={`pb-3 px-1 text-sm font-medium transition-colors duration-200 border-b-2 ${
                    isActiveTab(tab.path)
                      ? "text-teal-600 border-teal-600"
                      : "text-gray-600 hover:text-teal-600 hover:border-teal-300 border-transparent"
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 w-full px-6 py-6 bg-white overflow-y-auto m-0">
          {children}
        </div>
      </div>
    </Layout>
  );
}
