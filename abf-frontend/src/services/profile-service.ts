import apiClient from "@/lib/apiClient";
import { format } from "date-fns";
import { HistoricalGrant, HistoricalGrantAPIPayload, HistoricalGrantAPIResponse, ProfileData, OrganizationAPIResponse, OrganizationGrant, HistoricalGrantAPIResponse, OrganizationGrantAPIResponse, AddressAPIResponse, Address, Organization, OrganizationAttachmentAPIResponse, OrganizationAttachment, KMPAPIResponse, KMP, OrganizationUpdateRequest, KMPUpdateRequest, AddressUpdateRequest, AddKMPResponse, EditKMPResponse, DeleteKMPResponse, EditHistoricalGrantResponse, AddHistoricalGrantResponse, getOrganizationDetailsResponse, updateOrganizationDetailsResponse } from "@/types/profile";
import { z } from "zod";
import { organizationDetailsFormSchema } from "@/schemas/organization.schema";
import { BasicDetailsFormType } from "@/schemas/BasicDetailsSchema";
import { OrganizationDetailsFormType } from "@/schemas/OrganizationDetailsSchema";
import { AxiosResponse } from "axios";
import { HistoricalGrantFormType } from "@/schemas/AddHistoricalGrantSchema";

export const transformOrganizationAttachmentAPIResponseToOrganizationAttachment = (att: OrganizationAttachmentAPIResponse): OrganizationAttachment => ({
  id: att.id,
  objectKey: att.object_key,
  attachmentType: att.attachment_type,
  attachmentTypeName: att.attachment_type_name,
  originalFilename: att.original_filename,
  remarks: att.remarks,
  status: att.status,
  uploadedAt: att.uploaded_at,
  uploadedBy: att.uploaded_by,
  uploadedByEmail: att.uploaded_by_email,
});

export const transformKMPAPIResponseToKMP = (kmp: KMPAPIResponse): KMP => ({
  id: kmp.id,
  name: kmp.name,
  designation: kmp.designation,
  din: kmp.din,
  phoneNumber: kmp.phone_number,
  email: kmp.email,
  organization: kmp.organization.toString()
});

export const transformHistoricalGrantAPIResponseToHistoricalGrant = (grant: HistoricalGrantAPIResponse): HistoricalGrant => ({
  id: grant.id,
  organization: grant.organization,
  grantName: grant.grant_name,
  grantPurpose: grant.grant_purpose,
  startDate: grant.start_date,
  endDate: grant.end_date,
  amount: parseFloat(grant.budget),
  status: grant.status,
  statusDisplay: grant.status_display,
  createdAt: grant.created_at,
  updatedAt: grant.updated_at,
});


export const getProfile = async (): Promise<getOrganizationDetailsResponse> => {
  const endpoint = '/api/profiles/v1/organization';
  const response = await apiClient.get(endpoint);
  return response.data;
};

export const updateProfile = async (profileData: Partial<OrganizationUpdateRequest>): Promise<updateOrganizationDetailsResponse> => {
  const response = await apiClient.patch("/api/profiles/v1/organization/me/", profileData);
  return response.data;
};

export const createProfile = async (profileData: Partial<OrganizationAPIResponse>): Promise<OrganizationAPIResponse> => {
  const response = await apiClient.post('/api/profiles/v1/organization/', profileData);
  return response.data;
};

export const transformAddressAPIResponseToAddress = (address: AddressAPIResponse): Address => {
  return {
    id: address.id,
    addressLine1: address.address_line_1 || '',
    addressLine2: address.address_line_2 || '',
    postalCode: address.postal_code || '',
    locality: address.locality || '',
    state: address.state || '',
    city: address.city || '',
    country: address.country || ''
  }
}

export const transformOrganizationAPIResponseToOrganization = (
  organization: OrganizationAPIResponse
): Organization => {
  return {
    id: organization.id,
    pointOfContactName: organization.point_of_contact_name || '',
    budget: organization.budget || null,
    logoKey: organization.logo_key || null,
    organizationFunctionType: organization.organization_function_type,
    organizationLegalType: organization.organization_legal_type,
    organizationFunctionTypeName: organization.organization_function_type_name,
    organizationLegalTypeName: organization.organization_legal_type_name,
    organizationName: organization.organization_name || '',
    panNumber: organization.pan_number,
    phoneNumber: organization.phone_number,
    emailAddress: organization.email_address,
    websiteUrl: organization.website_url,
    numberOfTeamMembers: organization.number_of_team_members,
    mission: organization.mission,
    vision: organization.vision,
    registeredYear: organization.registered_year,
    backgroundHistory: organization.background_history,
    csrRegistrationNumber: organization.csr_registration_number,
    taxRegistrationNumber: organization.tax_registration_number,
    taxRegistrationNumberUnder12A: organization.tax_registration_number_under_12_a,
    fcraRegistrationNumber: organization.fcra_registration_number,
    trustRegistrationNumber: organization.trust_registration_number,
    darpanId: organization.darpan_id,
    createdAt: organization.created_at,
    updatedAt: organization.updated_at,
    previousGrants: organization.previous_grants.map(transformHistoricalGrantAPIResponseToHistoricalGrant),
    kmps: organization.kmps.map(transformKMPAPIResponseToKMP),
    grants: organization.grants.map(transformGrantAPIResponseToOrganizationGrant),
    attachments: organization.attachments.map(transformOrganizationAttachmentAPIResponseToOrganizationAttachment),
    address: transformAddressAPIResponseToAddress(organization.address),
  };
};

// Transform form values to API request payload for organization update
export function transformBasicDetailsFormToOrganizationAPIRequestPayload(values: BasicDetailsFormType): Partial<OrganizationUpdateRequest> {
  const payload: Partial<OrganizationUpdateRequest> = {};

  if (values.pointOfContactName !== undefined) payload.point_of_contact_name = values.pointOfContactName;
  if (values.phone !== undefined) payload.phone_number = values.phone;
  if (values.email !== undefined) payload.email_address = values.email;

  const address: Partial<AddressUpdateRequest> = {};
  if (values.addressLine1 !== undefined) address.address_line_1 = values.addressLine1;
  if (values.addressLine2 !== undefined) address.address_line_2 = values.addressLine2;
  if (values.locality !== undefined) address.locality = values.locality;
  if (values.city !== undefined) address.city = values.city;
  if (values.state !== undefined) address.state = values.state;
  if (values.postalCode !== undefined) address.postal_code = values.postalCode;
  if (values.country !== undefined) address.country = values.country;

  if (Object.keys(address).length > 0) {
    payload.address = address as AddressUpdateRequest;
  }

  if (values.organizationName !== undefined) payload.organization_name = values.organizationName;
  if (values.organizationLegalType !== undefined) payload.organization_legal_type = values.organizationLegalType;
  if (values.csrRegistrationNumber !== undefined) payload.csr_registration_number = values.csrRegistrationNumber;
  if (values.taxRegistrationNumber !== undefined) payload.tax_registration_number = values.taxRegistrationNumber;
  if (values.taxRegistrationNumberUnder12A !== undefined) payload.tax_registration_number_under_12_a = values.taxRegistrationNumberUnder12A;
  if (values.fcraRegistrationNumber !== undefined) payload.fcra_registration_number = values.fcraRegistrationNumber;
  if (values.trustRegistrationNumber !== undefined) payload.trust_registration_number = values.trustRegistrationNumber;
  if (values.darpanId !== undefined) payload.darpan_id = values.darpanId;
  if (values.panNumber !== undefined) payload.pan_number = values.panNumber;
  if (values.mission !== undefined) payload.mission = values.mission;
  if (values.vision !== undefined) payload.vision = values.vision;
  if (values.backgroundHistory !== undefined) payload.background_history = values.backgroundHistory;

  return payload;
}

export function transformOrganizationDetailsFormToOrganizationAPIRequestPayload (values: OrganizationDetailsFormType): Partial<OrganizationUpdateRequest> {
  return {
    organization_name: values.organizationName,
    organization_legal_type: values.organizationLegalType,
    tax_registration_number: values.taxRegistrationNumber,
    pan_number: values.panNumber,
    number_of_team_members: parseInt(values.teamMembers),
    website_url: values.websiteUrl,
    budget: parseInt(values.budget),
    registered_year: parseInt(values.registeredYear),
    mission: values.mission,
    vision: values.vision,
    background_history: values.backgroundHistory

  };
}

export const transformProfileData = (backendData: OrganizationAPIResponse): ProfileData => {
  return {
    organization: {
      organizationName: backendData.organization_name,
      organizationLegalType: backendData.organization_legal_type_name || '',
      organizationFunctionType: backendData.organization_function_type_name || '',
      taxRegistrationNumber: backendData.tax_registration_number || '',
      panNumber: backendData.pan_number || ''
    },
    contact: {
      fullName: backendData.point_of_contact_name || '', // Use point_of_contact_name first
      phone: backendData.phone_number || '',
      email: backendData.email_address || '',
      address: backendData.mailing_address || ''
    },
    details: {
      teamMembers: backendData.number_of_team_members?.toString() || '',
      website: backendData.website_url || '',
      mission: backendData.mission || '',
      history: backendData.background_history || ''
    },
    identificationDetails: {
      csrRegistrationNumber: backendData.csr_registration_number || '',
      taxRegistrationNumber: backendData.tax_registration_number || '',
      taxRegistrationNumberUnder12A: backendData.tax_registration_number_under_12_a || '',
      trustRegistrationNumber: backendData.trust_registration_number || '',
      darpanId: backendData.darpan_id || '',
      fcraRegistrationNumber: backendData.fcra_registration_number || ''
    },
    previousGrants: (backendData.previous_grants || []).map((grant: HistoricalGrantAPIResponse) => ({
      id: grant.id,
      organization: grant.organization,
      grantName: grant.grant_name,
      grantPurpose: grant.grant_purpose,
      startDate: grant.start_date,
      endDate: grant.end_date,
      amount: parseFloat(grant.budget),
      status: grant.status,
      statusDisplay: grant.status_display,
      createdAt: grant.created_at,
      updatedAt: grant.updated_at
    })),
    keyPersonnel: backendData.kmps || []
  };
};

export const prepareOrganizationPayload = (formValues: z.infer<typeof organizationDetailsFormSchema>): Partial<OrganizationAPIResponse> => {
  return {
    organization_name: formValues.organizationName,
    pan_number: formValues.panNumber,
    phone_number: formValues.phoneNumber,
    email_address: formValues.emailAddress,
    website_url: formValues.websiteUrl,
    number_of_team_members: parseInt(formValues.numberOfTeamMembers),
    mission: formValues.missionVision,
    background_history: formValues.backgroundHistory,
    csr_registration_number: formValues.csrRegistrationNumber,
    tax_registration_number: formValues.taxRegistrationNumber,
    tax_registration_number_under_12_a: formValues.taxRegistrationNumberUnder12A,
    fcra_registration_number: formValues.fcraRegistrationNumber,
    trust_registration_number: formValues.trustRegistrationNumber,
    darpan_id: formValues.darpanId,
    organization_legal_type: formValues.organizationLegalType,
    address_line_1: formValues.addressLine1,
    address_line_2: formValues.addressLine2,
    postal_code: formValues.postalCode,
    locality: formValues.locality,
    state: formValues.state,
    city: formValues.city,
    country: formValues.country,
    point_of_contact_name: formValues.pointOfContactName // Add point_of_contact_name
  };
};

export const transformHistoricalGrantToOrganizationGrantHistoryAPIPayload = (grant: HistoricalGrant): HistoricalGrantAPIPayload => ({
  grant_name: grant.grantName,
  budget: grant.amount,
  start_date: grant.startDate,
  end_date: grant.endDate,
  status: grant.status,
  grant_purpose: grant.grantPurpose
});

export const transformHistoricalGrantFormTypeToHistoricalGrantAPIPayload = (historicalGrant: HistoricalGrantFormType): HistoricalGrantAPIPayload => {
  return {
    grant_name: historicalGrant.grantName,
    budget: historicalGrant.budget,
    start_date: format(historicalGrant.startDate, "yyyy-MM-dd"),
    end_date: format(historicalGrant.endDate.toString(), "yyyy-MM-dd"),
    status: historicalGrant.status,
    grant_purpose: historicalGrant.grantPurpose
  }

}

export const transformGrantAPIResponseToOrganizationGrant = (grant: OrganizationGrantAPIResponse): OrganizationGrant => ({
  id: grant.id,
  name: grant.grant_name,
  startDate: grant.start_date,
  endDate: grant.end_date,
  purpose: grant.grant_purpose,
  amount: parseFloat(grant.annual_budget),
  fundingSources: grant.funding_sources,
  createdAt: grant.created_at,
  updatedAt: grant.updated_at,
  organization: grant.organization,
  grantMakerOrganization: grant.grant_maker_organization,
  recipientOrganizationName: grant.recipient_organization_name
  
})

export const transformGrantsAPIResponseToOrganizationGrants = (
  grants: OrganizationGrantAPIResponse[]
): OrganizationGrant[] => {
  return grants.map(transformGrantAPIResponseToOrganizationGrant);
};

export const transformFrontendToBackend = (frontendData: any): Partial<OrganizationAPIResponse> => {
  const baseData: Partial<OrganizationAPIResponse> = {};

  if (frontendData.organization?.organizationName) {
    baseData.organization_name = frontendData.organization.organizationName;
  } else {
    console.warn('Organization name is required but not provided in the update data');
    throw new Error('Organization name is required');
  }

  if (frontendData.organization?.type) {
    baseData.organization_legal_type = frontendData.organization.type;
  }

  if (frontendData.identificationDetails?.taxRegistrationNumber) {
    baseData.tax_registration_number = frontendData.identificationDetails.taxRegistrationNumber;
  }

  if (frontendData.identificationDetails?.taxRegistrationNumberUnder12A) {
    baseData.tax_registration_number_under_12_a = frontendData.identificationDetails.taxRegistrationNumberUnder12A;
  }

  if (frontendData.identificationDetails?.csrRegistrationNumber) {
    baseData.csr_registration_number = frontendData.identificationDetails.csrRegistrationNumber;
  }

  if (frontendData.identificationDetails?.fcraRegistrationNumber) {
    baseData.fcra_registration_number = frontendData.identificationDetails.fcraRegistrationNumber;
  }

  if (frontendData.identificationDetails?.trustRegistrationNumber) {
    baseData.trust_registration_number = frontendData.identificationDetails.trustRegistrationNumber;
  }

  if (frontendData.identificationDetails?.darpanId) {
    baseData.darpan_id = frontendData.identificationDetails.darpanId;
  }

  if (frontendData.identificationDetails?.pan || frontendData.organization?.pan) {
    baseData.pan_number = frontendData.identificationDetails?.pan || frontendData.organization?.pan;
  }

  if (frontendData.contact?.address) {
    baseData.mailing_address = frontendData.contact.address;
  }

  if (frontendData.contact?.phone) {
    baseData.phone_number = frontendData.contact.phone;
  }

  if (frontendData.contact?.email) {
    baseData.email_address = frontendData.contact.email;
  }

  if (frontendData.contact?.fullName) {
    baseData.point_of_contact_name = frontendData.contact.fullName; // Map fullName to point_of_contact_name
  }

  if (frontendData.details?.website) {
    baseData.website_url = frontendData.details.website;
  }

  if (frontendData.details?.teamMembers) {
    baseData.number_of_team_members = parseInt(frontendData.details.teamMembers) || 1;
  }

  if (frontendData.details?.mission) {
    baseData.mission = frontendData.details.mission;
  }

  if (frontendData.details?.history) {
    baseData.background_history = frontendData.details.history;
  }

  return baseData;
};

export const transformKMPToKMPUpdateRequest = (data: Partial<KMP>): Partial<KMPUpdateRequest> => {
  const payload: Partial<KMPUpdateRequest> = {};

  if (data.name !== undefined) payload.name = data.name;
  if (data.designation !== undefined) payload.designation = data.designation;
  if (data.din !== undefined) payload.din = data.din;
  if (data.phoneNumber !== undefined) payload.phone_number = data.phoneNumber;
  if (data.email !== undefined) payload.email = data.email;

  return payload;
};

export const transformBackendToFrontend = (backendData: any) => {
  const profileData: any = {};

  profileData.organization = {
    organizationName: backendData.organization_name || "",
    organizationLegalType: backendData.organization_legal_type_name || "",
    panNumber: backendData.pan_number || "",
    taxRegistrationNumber: backendData.tax_registration_number || ""
  };

  profileData.contact = {
    phone: backendData.phone_number || "",
    email: backendData.email_address || "",
    address: backendData.mailing_address || "",
    fullName: backendData.point_of_contact_name || ""
  };

  profileData.identificationDetails = {
    csrRegistrationNumber: backendData.csr_registration_number || "",
    taxRegistrationNumber: backendData.tax_registration_number || "",
    taxRegistrationNumberUnder12A: backendData.tax_registration_number_under_12_a || "",
    fcraRegistrationNumber: backendData.fcra_registration_number || "",
    trustRegistrationNumber: backendData.trust_registration_number || "",
    darpanId: backendData.darpan_id || "",
    panNumber: backendData.pan_number || ""
  };

  profileData.details = {
    website: backendData.website_url || "",
    teamMembers: backendData.number_of_team_members?.toString() || "",
    mission: backendData.mission_vision || "",
    history: backendData.background_history || ""
  };

  try {
    if (backendData.previous_grants) {
      profileData.previousGrants = (backendData.previous_grants || []).map((grant: any) => ({
        id: grant.id,
        grantName: grant.grant_name,
        amount: grant.budget,
        startDate: grant.start_date,
        endDate: grant.end_date,
        status: grant.status,
        purpose: grant.grant_purpose
      }));
    } else {
      profileData.previousGrants = [];
    }
  } catch (e) {
    console.error("Failed to parse previous_grants_info:", e);
    profileData.previousGrants = [];
  }

  return profileData;
};

export const addKMP = async (kmpData: Partial<KMPUpdateRequest>): Promise<AddKMPResponse> => {
  const response = await apiClient.post("/api/profiles/v1/kmp/", kmpData);
  return response.data;
};

export const editKMP = async (id: number, kmpData: Partial<KMPUpdateRequest>): Promise<EditKMPResponse> => {
  const response  = await apiClient.patch(`/api/profiles/v1/kmp/${id}/`, kmpData);
  return response.data;

}

export const deleteKMP = async (id: number): Promise<AxiosResponse> => {
  const response  = await apiClient.delete(`/api/profiles/v1/kmp/${id}/`);
  return response;
}

export const getAllKMPs = async () => {
  try {
    const response = await apiClient.get('/api/profiles/v1/kmp');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching KMPs:', error);
    throw error;
  }
};


export const addHistoricalGrant = async (historicalGrant: HistoricalGrantAPIPayload): Promise<AddHistoricalGrantResponse> => {
  const response = await apiClient.post('/api/v1/organization-grant-history/', historicalGrant);
  console.log("response = ", JSON.stringify(response));
  return response.data;
};

export const editHistoricalGrant = async (id: number, historicalGrant: Partial<HistoricalGrantAPIPayload>): Promise<EditHistoricalGrantResponse> => {
  const response = await apiClient.patch(`/api/v1/organization-grant-history/${id}/`, historicalGrant);
  return response.data;
};

export const deleteHistoricalGrant = async (id: number): Promise<AxiosResponse> => {
  const response = await apiClient.delete(`/api/v1/organization-grant-history/${id}/`);
  return response;
}

export const updateDocumentStatus = async (documentId: number, payload: { status: string; remarks?: string }) => {
  const response = await apiClient.patch(`/api/grantmaker/v1/documents/${documentId}/`, payload);
  return response.data;
};
// Grant Attachments API
export const getGrantAttachments = async () => {
  const response = await apiClient.get('/api/v1/grant-attachments/');
  return response.data;
};

export const addGrantAttachment = async (attachmentData: any) => {
  const response = await apiClient.post('/api/v1/grant-attachments/', attachmentData);
  return response.data;
};

interface GrantAttachmentPayload {
  grant: string;
  attachment_type: string;
  filename: string;
  s3_key: string;
}

export const uploadOrReplaceGrantAttachment = async (attachmentData: GrantAttachmentPayload) => {
  const response = await apiClient.put('/api/v1/grant-attachments/upload-or-replace/', attachmentData);
  return response.data;
};
export const getGrantAttachmentTypes = async () => {
  const response = await apiClient.get('/api/v1/grant-attachment-types/');
  return response.data;
};