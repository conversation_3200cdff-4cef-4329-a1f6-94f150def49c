// src/services/disbursement-service.ts

import apiClient from '@/lib/apiClient';

export const fetchAllDisbursements = async () => {
  try {
    const response = await apiClient.get('/api/funding/v1/disbursements/list/'); // Update port if needed
    return response.data;
  } catch (error: any) {
    console.error('fetchAllDisbursements error:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    });
    throw error;
  }
};


export const uploadDisbursementFile = async (
  disbursementId: string,
  file: File,
  type: 'request_letter' | 'acknowledgement_letter'
) => {
  try {
    const formData = new FormData();
    formData.append(type, file);

    const response = await apiClient.patch(
      `/api/funding/v1/disbursements/${disbursementId}/upload-${type}/`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  } catch (error: any) {
    console.error(`uploadDisbursementFile (${type}) error:`, {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    });
    throw error;
  }
};

export const fetchAllGrants = async () => {
  try {
    const { data } = await apiClient.get("/api/support/v1/grants-list/");
    return data;
  } catch (error: any) {
    console.error("getGrants error:", error);
    throw error;
  }
};
export const createDisbursement = async (payload: {
  grant: number;
  tranche_number: number;
  scheduled_date: string;
  amount: string;
  remarks?: string;
}) => {
  const response = await apiClient.post('/api/funding/v1/disbursements/create/', payload);
  return response.data;
};

// services/disbursement-service.ts
export const updateDisbursementStatus = async (id: string, status: string) => {
  const res = await apiClient.patch(`/api/funding/v1/disbursements/${id}/update-status/`, {
    status,  // <-- must be 'disbursed' or similar
  });
  return res.data;
};