/**
 * Grantee Service Module
 *
 * This module provides a consolidated service for grantee data.
 * It includes interfaces and functions for working with grantee data,
 * which represents organizations that receive grants.
 *
 * This service is used by the GranteeTable component and other components
 * that need to display grantee data in a consistent format.
 */

import apiClient from "@/lib/apiClient";
import { Organization } from "./grantmaker-service";
import { Grantee } from "../grantee-service";
import { OrganizationAPIResponse, OrganizationAttachment, OrganizationAttachmentAPIResponse, OrganizationGrant, OrganizationGrantAPIResponse } from "@/types/profile";
import { GranteeProfile } from "@/types/profile";

/**
 * Represents a unified organization entity that can be either a grantmaker organization or a grantee
 */
export interface UnifiedOrganization {
  id: string;
  name: string;
  sector: string;
  totalBudget: number;
  totalDisbursed: number;
  remainingBalance: number;
  utilizationPercentage: number;
  lastDisbursementDate: string;
  status: string;
  type: 'organization' | 'grantee';
  contactPerson?: string;
  contactEmail?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
  orgType?: 'TRUST' | 'NON_PROFIT' | 'SOCIETY';
}

const mockUnifiedOrganizations: UnifiedOrganization[] = [
  {
    id: "1",
    name: "Education First Foundation",
    sector: "Education",
    totalBudget: 250000,
    totalDisbursed: 200000,
    remainingBalance: 50000,
    utilizationPercentage: 80,
    lastDisbursementDate: "2024-07-15",
    status: "Active",
    type: "organization",
    contactPerson: "Rahul Sharma",
    contactEmail: "<EMAIL>",
    location: "Bangalore, Karnataka",
    startDate: "2023-01-15",
    orgType: "TRUST"
  },
  {
    id: "2",
    name: "Healthcare Initiative",
    sector: "Healthcare",
    totalBudget: 350000,
    totalDisbursed: 300000,
    remainingBalance: 50000,
    utilizationPercentage: 86,
    lastDisbursementDate: "2024-08-10",
    status: "Active",
    type: "organization",
    contactPerson: "Priya Patel",
    contactEmail: "<EMAIL>",
    location: "Mumbai, Maharashtra",
    startDate: "2023-02-10",
    orgType: "NON_PROFIT"
  },
  {
    id: "3",
    name: "Community Development Trust",
    sector: "Community",
    totalBudget: 180000,
    totalDisbursed: 150000,
    remainingBalance: 30000,
    utilizationPercentage: 83,
    lastDisbursementDate: "2024-07-05",
    status: "Pending",
    type: "organization",
    contactPerson: "Amit Kumar",
    contactEmail: "<EMAIL>",
    location: "Delhi, NCR",
    startDate: "2023-03-05",
    orgType: "TRUST"
  },
  {
    id: "4",
    name: "Green Earth Project",
    sector: "Environment",
    totalBudget: 200000,
    totalDisbursed: 180000,
    remainingBalance: 20000,
    utilizationPercentage: 90,
    lastDisbursementDate: "2024-08-20",
    status: "Active",
    type: "organization",
    contactPerson: "Lakshmi Narayan",
    contactEmail: "<EMAIL>",
    location: "Chennai, Tamil Nadu",
    startDate: "2023-01-20",
    orgType: "SOCIETY"
  },
  {
    id: "5",
    name: "Arts & Culture Foundation",
    sector: "Arts",
    totalBudget: 120000,
    totalDisbursed: 120000,
    remainingBalance: 0,
    utilizationPercentage: 100,
    lastDisbursementDate: "2024-06-12",
    status: "Completed",
    type: "organization",
    contactPerson: "Sanjay Ghosh",
    contactEmail: "<EMAIL>",
    location: "Kolkata, West Bengal",
    startDate: "2022-11-15",
    endDate: "2023-11-15",
    orgType: "NON_PROFIT"
  }
];

// Mock data for grantee profiles
const mockGranteeProfiles: Record<string, GranteeProfile> = {
  "1": {
    id: "1",
    name: "Education First Foundation",
    organization: "Education First Foundation",
    sector: "Education",
    contactPerson: "Rahul Sharma",
    contactEmail: "<EMAIL>",
    location: "Bangalore, Karnataka",
    startDate: "2023-01-15",
    status: "Active",
    totalFunding: 250000,
    disbursedAmount: 200000,
    remainingAmount: 50000,
    utilizationPercentage: 80,

    // Organization details
    organization_name: "Education First Foundation",
    organization_legal_type: "TRUST",
    organization_function_type: "EDUCATION",
    organization_legal_type_name: "Trust",
    organization_function_type_name: "Education",
    pan_number: "**********",
    mailing_address: "123, Education Street, Bangalore, Karnataka - 560001",
    phone_number: "+91 9876543210",
    email_address: "<EMAIL>",
    website_url: "https://www.educationfoundation.org",
    number_of_team_members: 25,
    mission_vision: "To provide quality education to underprivileged children across rural Karnataka.",
    background_history: "Founded in 2010, Education First Foundation has been working in the education sector for over a decade, focusing on improving access to quality education in rural areas.",
    previous_grants_info: "",

    // Registration details
    tax_registration_number: "**********",
    csr_registration_number: "CSR00001234",
    tax_registration_number_under_12_a: "12A-123456",
    trust_registration_number: "TRUST-KA-1234",
    darpan_id: "KA/2010/0012345",
    fcra_registration_number: "FCRA-123456",

    // Grant details
    grant_id: "PF-2025-DCRS-001",
    grant_name: "Digital Classroom Project",
    project_start_date: "2025-04-01",
    project_end_date: "2026-03-31",
    purpose_of_grant: "Setting up digital classrooms for government schools in rural Karnataka.",
    annual_budget: 9000000,
    funding_sources: "NIL",

    // KMP details
    kmp_details: [
      {
        id: "1",
        name: "Rahul Sharma",
        email: "<EMAIL>",
        phone: "+91 9876543210",
        din: "00123456",
        designation: "Director"
      },
      {
        id: "2",
        name: "Anita Desai",
        email: "<EMAIL>",
        phone: "+91 **********",
        din: "00123457",
        designation: "Secretary"
      },
      {
        id: "3",
        name: "Vikram Mehta",
        email: "<EMAIL>",
        phone: "+91 **********",
        din: "00123458",
        designation: "Treasurer"
      }
    ],

    // Grant history
    grant_history: [
      {
        id: "1",
        grant_name: "Digital Classroom Initiative",
        amount: 1500000,
        start_date: "2022-04-01",
        end_date: "2023-03-31",
        status: "completed",
        purpose: "Setting up digital classrooms in 10 government schools in rural Karnataka"
      },
      {
        id: "2",
        grant_name: "Teacher Training Program",
        amount: 800000,
        start_date: "2023-06-01",
        end_date: "2024-05-31",
        status: "active",
        purpose: "Training 50 teachers on modern teaching methodologies and digital tools"
      },
      {
        id: "3",
        grant_name: "STEM Education Project",
        amount: 1200000,
        start_date: "2024-01-01",
        end_date: "2024-12-31",
        status: "active",
        purpose: "Implementing STEM education programs in 15 schools across Karnataka"
      }
    ],
    supportingDocuments: [
      {
        id: "1",
        name: "Registration Certificate",
        attachment_type: "registration",
        status: "verified",
        uploadedAt: "2023-01-10T10:30:00Z",
        url: "#"
      },
      {
        id: "2",
        name: "Tax Exemption Certificate",
        attachment_type: "tax-exemption",
        status: "pending",
        uploadedAt: "2023-01-10T10:35:00Z",
        url: "#"
      },
      {
        id: "3",
        name: "FCRA Certificate",
        attachment_type: "fcra",
        status: "update-required",
        uploadedAt: "2023-01-20T09:15:00Z",
        url: "#",
        comments: "Please provide a valid FCRA certificate."
      }
    ],
    expenseBreakdown: [
      { name: 'Personnel', value: 100000, color: '#FF9800' },
      { name: 'Operations', value: 75000, color: '#FFC107' },
      { name: 'Programs', value: 50000, color: '#FFD54F' },
      { name: 'Equipment', value: 25000, color: '#FFECB3' },
    ],
    monthlyExpenses: [
      { month: 'Jan', budget: 20833, actual: 19791 },
      { month: 'Feb', budget: 20833, actual: 20416 },
      { month: 'Mar', budget: 20833, actual: 21250 },
      { month: 'Apr', budget: 20833, actual: 20208 },
      { month: 'May', budget: 20833, actual: 21041 },
      { month: 'Jun', budget: 20833, actual: 20625 },
      { month: 'Jul', budget: 20833, actual: 21666 },
      { month: 'Aug', budget: 20833, actual: 20833 },
      { month: 'Sep', budget: 20833, actual: 19791 },
      { month: 'Oct', budget: 20833, actual: 11458 },
      { month: 'Nov', budget: 20833, actual: 0 },
      { month: 'Dec', budget: 20833, actual: 0 },
    ]
  },
  "2": {
    id: "2",
    name: "Healthcare Initiative",
    organization: "Healthcare Initiative",
    sector: "Healthcare",
    contactPerson: "Priya Patel",
    contactEmail: "<EMAIL>",
    location: "Mumbai, Maharashtra",
    startDate: "2023-02-10",
    status: "Active",
    totalFunding: 350000,
    disbursedAmount: 300000,
    remainingAmount: 50000,
    utilizationPercentage: 86,

    // Organization details
    organization_name: "Healthcare Initiative",
    organization_legal_type: "NON_PROFIT",
    organization_function_type: "HEALTHCARE",
    organization_legal_type_name: "Non-Profit",
    organization_function_type_name: "Healthcare",
    pan_number: "**********",
    mailing_address: "456, Health Avenue, Mumbai, Maharashtra - 400001",
    phone_number: "+91 **********",
    email_address: "<EMAIL>",
    website_url: "https://www.healthcareinitiative.org",
    number_of_team_members: 40,
    mission_vision: "To provide affordable healthcare services to underserved communities in Maharashtra.",
    background_history: "Established in 2012, Healthcare Initiative has been working to improve healthcare access in rural and urban slum areas of Maharashtra.",
    previous_grants_info: "",

    // Registration details
    tax_registration_number: "**********",
    csr_registration_number: "CSR00005678",
    tax_registration_number_under_12_a: "12A-567890",
    trust_registration_number: "",
    darpan_id: "MH/2012/0054321",
    fcra_registration_number: "FCRA-567890",

    // Grant details
    grant_id: "PF-2025-MHCP-002",
    grant_name: "Mobile Health Clinic Project",
    project_start_date: "2025-01-01",
    project_end_date: "2025-12-31",
    purpose_of_grant: "Operating mobile health clinics in remote villages of Maharashtra.",
    annual_budget: 12000000,
    funding_sources: "Partial funding from state government",

    // KMP details
    kmp_details: [
      {
        id: "1",
        name: "Priya Patel",
        email: "<EMAIL>",
        phone: "+91 **********",
        din: "00234567",
        designation: "CEO"
      },
      {
        id: "2",
        name: "Rajesh Kumar",
        email: "<EMAIL>",
        phone: "+91 **********",
        din: "00234568",
        designation: "CFO"
      }
    ],

    // Grant history
    grant_history: [
      {
        id: "1",
        grant_name: "Mobile Health Clinic",
        amount: 2500000,
        start_date: "2022-01-01",
        end_date: "2022-12-31",
        status: "completed",
        purpose: "Operating mobile health clinics in 20 remote villages of Maharashtra"
      },
      {
        id: "2",
        grant_name: "COVID-19 Relief Program",
        amount: 1800000,
        start_date: "2023-03-01",
        end_date: "2023-08-31",
        status: "completed",
        purpose: "Providing medical supplies and support during the COVID-19 pandemic"
      },
      {
        id: "3",
        grant_name: "Maternal Health Initiative",
        amount: 3000000,
        start_date: "2023-09-01",
        end_date: "2024-08-31",
        status: "active",
        purpose: "Improving maternal healthcare access in rural communities"
      }
    ],
    supportingDocuments: [
      {
        id: "4",
        name: "Registration Certificate",
        attachment_type: "registration",
        status: "verified",
        uploadedAt: "2023-02-05T10:30:00Z",
        url: "#"
      },
      {
        id: "5",
        name: "Tax Exemption Certificate",
        attachment_type: "tax-exemption",
        status: "verified",
        uploadedAt: "2023-02-05T10:35:00Z",
        url: "#"
      }
    ],
    expenseBreakdown: [
      { name: 'Personnel', value: 140000, color: '#FF9800' },
      { name: 'Operations', value: 105000, color: '#FFC107' },
      { name: 'Programs', value: 70000, color: '#FFD54F' },
      { name: 'Equipment', value: 35000, color: '#FFECB3' },
    ],
    monthlyExpenses: [
      { month: 'Jan', budget: 29166, actual: 27708 },
      { month: 'Feb', budget: 29166, actual: 28583 },
      { month: 'Mar', budget: 29166, actual: 29750 },
      { month: 'Apr', budget: 29166, actual: 28291 },
      { month: 'May', budget: 29166, actual: 29458 },
      { month: 'Jun', budget: 29166, actual: 28875 },
      { month: 'Jul', budget: 29166, actual: 30333 },
      { month: 'Aug', budget: 29166, actual: 29166 },
      { month: 'Sep', budget: 29166, actual: 27708 },
      { month: 'Oct', budget: 29166, actual: 16041 },
      { month: 'Nov', budget: 29166, actual: 0 },
      { month: 'Dec', budget: 29166, actual: 0 },
    ]
  },
  "7": {
    "id": "7",
    "name": "Education Access Foundation",
    "organization": "Education Access Foundation",
    "sector": "Education",
    "contactPerson": "Anil Sharma",
    "contactEmail": "<EMAIL>",
    "location": "Pune, Maharashtra",
    "startDate": "2023-02-10",
    "status": "Active",
    "totalFunding": 350000,
    "disbursedAmount": 300000,
    "remainingAmount": 50000,
    "utilizationPercentage": 86,
    "organization_name": "Education Access Foundation",
    "organization_legal_type": "NON_PROFIT",
    "organization_function_type": "EDUCATION",
    "organization_legal_type_name": "Non-Profit",
    "organization_function_type_name": "Education",
    "pan_number": "**********",
    "mailing_address": "101, Knowledge Park, Pune, Maharashtra - 411001",
    "phone_number": "+91 9*********",
    "email_address": "<EMAIL>",
    "website_url": "https://www.edaccess.org",
    "number_of_team_members": 55,
    "mission_vision": "To ensure equitable access to quality education for underprivileged children.",
    "background_history": "Founded in 2015, EAF has launched education programs across 6 districts in Maharashtra.",
    "grant_id": "PF-2025-EDU-003",
    "grant_name": "Scholarship Support Program",
    "project_start_date": "2025-04-01",
    "project_end_date": "2026-03-31",
    "tax_registration_number": "12523",
    "purpose_of_grant": "Providing scholarships and learning material to students in tribal regions.",
    "funding_sources": "CSR funds, educational NGOs, and crowdfunding",
    "kmp_details": [
      {
        "id": "1",
        "name": "Anil Sharma",
        "email": "<EMAIL>",
        "phone": "+91 9*********",
        "din": "00345678",
        "designation": "Director"
      },
      {
        "id": "2",
        "name": "Sonal Mehta",
        "email": "<EMAIL>",
        "phone": "+91 9234567890",
        "din": "00345679",
        "designation": "Program Head"
      }
    ]
  }
};

/**
 * Fetches detailed grant information by grant ID
 * @param grantId - The ID of the grant to fetch details for
 * @returns Promise with detailed grant information
 */
export const getGrantDetails = async (grantId: string): Promise<any> => {
  try {
    const response = await apiClient.get(`/api/v1/grants/${grantId}/`);
    if (response.data) {
      return response.data;
    }
    throw new Error('Grant details not found');
  } catch (error) {
    console.error('Error fetching grant details:', error);
    throw error;
  }
};

/**
 * Fetches grant history for a specific organization
 * @param organizationId - The ID of the organization to fetch grant history for
 * @returns Promise with grant history data
 */
export const getGrantHistory = async (organizationId: string): Promise<any[]> => {
  try {
    // First try the new grantmaker endpoint
    const response = await apiClient.get(`/api/grantmaker/v1/grantees/${organizationId}/grant-history/`);
    if (response.data && response.data.status === 'SUCCESS') {
      return response.data.data || [];
    }
    return [];
  } catch (error) {
    console.error('Error fetching grant history from grantmaker endpoint:', error);

    // Fallback to the original endpoint
    try {
      const fallbackResponse = await apiClient.get(`/api/v1/organization-grant-history/?organization=${organizationId}`);
      if (fallbackResponse.data && fallbackResponse.data.status === 'SUCCESS') {
        return fallbackResponse.data.data || [];
      }
      return [];
    } catch (fallbackError) {
      console.error('Error fetching grant history from fallback endpoint:', fallbackError);
      // Return empty array instead of throwing to handle gracefully
      return [];
    }
  }
};

/**
 * Fetches all grantees
 * @returns Promise with an array of grantees
 */
export const getGrantees = async (): Promise<UnifiedOrganization[]> => {
  try {
    // Try to fetch from the backend API
    try {
      const response = await apiClient.get('/api/grantmaker/v1/grantees/all/');
      if (response.data && response.data.status === 'success') {
        // Transform the API response to match our UnifiedOrganization interface
        return response.data.data.map((org: any) => {
          // Generate random financial values for demonstration
          const totalBudget = Math.floor(Math.random() * 1000000) + 500000;
          const totalDisbursed = Math.floor(Math.random() * totalBudget);
          const remainingBalance = totalBudget - totalDisbursed;
          const utilizationPercentage = Math.round((totalDisbursed / totalBudget) * 100);

          return {
            id: org.id.toString(),
            name: org.organization_name,
            sector: org.organization_legal_type_name || 'NON_PROFIT',
            totalBudget,
            totalDisbursed,
            remainingBalance,
            utilizationPercentage,
            lastDisbursementDate: org.updated_at || new Date().toISOString(),
            status: 'active',
            type: 'organization',
            contactPerson: org.contact_person || 'Contact Person',
            contactEmail: org.contact_email || '<EMAIL>',
            location: org.location || 'Mumbai, India',
            startDate: org.created_at,
            orgType: org.organization_legal_type || 'NON_PROFIT'
          };
        });
      }
    } catch (apiError) {
      console.warn('API call failed, using mock data:', apiError);
    }

    // Fallback to mock data if API call fails
    return new Promise(resolve => {
      setTimeout(() => resolve(mockUnifiedOrganizations), 500);
    });
  } catch (error) {
    console.error('Error fetching grantees:', error);
    throw error;
  }
};

/**
 * Fetches a grantee by ID
 * @param id Grantee ID
 * @returns Promise with the grantee
 */
export const getGranteeById = async (id: string): Promise<UnifiedOrganization | undefined> => {
  try {
    // Try to fetch from the backend API
    try {
      const response = await apiClient.get(`/api/grantmaker/v1/grantees/${id}/`);
      if (response.data && response.data.status === 'success') {
        const orgData = response.data.data.organization;
        const grantsData = response.data.data.grants;
        const expensesData = response.data.data.expenses;
        const disbursementsData = response.data.data.disbursements;

        // Calculate financial metrics
        const totalBudget = grantsData.reduce((sum: number, grant: any) => sum + (parseFloat(grant.annual_budget) || 0), 0);
        const totalDisbursed = disbursementsData.reduce((sum: number, disb: any) => sum + (parseFloat(disb.received_amount) || 0), 0);
        const remainingBalance = totalBudget - totalDisbursed;

        return {
          id: orgData.id.toString(),
          name: orgData.organization_name,
          sector: orgData.organization_legal_type_name || 'NON_PROFIT',
          totalBudget,
          totalDisbursed,
          remainingBalance,
          utilizationPercentage: totalBudget > 0 ? Math.round((totalDisbursed / totalBudget) * 100) : 0,
          lastDisbursementDate: disbursementsData.length > 0 ?
            disbursementsData.sort((a: any, b: any) => new Date(b.payment_received_date).getTime() - new Date(a.payment_received_date).getTime())[0].payment_received_date :
            orgData.updated_at,
          status: 'active',
          type: 'organization',
          contactPerson: '', // Would need to be fetched from KMP data
          contactEmail: '',
          location: '',
          startDate: orgData.created_at,
          orgType: orgData.organization_legal_type || 'NON_PROFIT'
        };
      }
    } catch (apiError) {
      console.warn(`API call for grantee ${id} failed, using mock data:`, apiError);
    }

    // Fallback to mock data if API call fails
    return new Promise(resolve => {
      setTimeout(() => {
        const grantee = mockUnifiedOrganizations.find(org => org.id === id);
        resolve(grantee);
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching grantee with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Fetches a grantee profile by ID
 * @param id Grantee ID
 * @returns Promise with the grantee profile
 */
export const getGranteeProfile = async (id: string): Promise<GranteeProfile> => {
  try {
    // Try to fetch from the backend API
    try {
      const response = await apiClient.get(`/api/grantmaker/v1/grantees/${id}/`);
      console.log("Response = ", response.data);

      // Log the full response for debugging
      console.log("Full API Response:", JSON.stringify(response.data, null, 2));

      if (response.data && response.data.status === 'success') {
        const orgData: OrganizationAPIResponse = response.data.data.organization;
        const grantsData = response.data.data.grants;
        const expensesData = response.data.data.expenses;
        const disbursementsData = response.data.data.disbursements;

        // Log specific sections for debugging
        console.log("Organization Data:", orgData);
        console.log("KMP Data:", response.data.data.kmp_details);
        console.log("Grant Histories:", response.data.data.grant_histories);
        console.log("Number of Team Members:", orgData.number_of_team_members);
        console.log("Mission Vision:", orgData.mission);
        console.log("Background History:", orgData.background_history);

        // Calculate financial metrics
        const totalBudget = grantsData.reduce((sum: number, grant: any) => sum + (parseFloat(grant.annual_budget) || 0), 0);
        const totalDisbursed = disbursementsData.reduce((sum: number, disb: any) => sum + (parseFloat(disb.received_amount) || 0), 0);
        const remainingBalance = totalBudget - totalDisbursed;

        // Map grants data
        const today = new Date().toISOString().split('T')[0]; // e.g. "2025-05-06"
        const grants: OrganizationGrant[] = grantsData.map((grant: OrganizationGrantAPIResponse) => ({
          id: grant.id.toString(),
          name: grant.grant_name,
          amount: parseFloat(grant.annual_budget) || 0,
          startDate: grant.start_date,
          endDate: grant.end_date,
          status: grant.end_date < today ? 'completed' : 'active',
          purpose: grant.grant_purpose || 'Grant purpose not specified'
        }));

        const attachments: OrganizationAttachment[] = orgData.attachments.map((attachment: OrganizationAttachmentAPIResponse) => ({

          id: attachment.id,
          objectKey: attachment.object_key,
          attachmentType: attachment.attachment_type,
          attachmentTypeName: attachment.attachment_type_name,
          originalFilename: attachment.original_filename,
          remarks: attachment.remarks,
          status: attachment.status,
          uploadedAt: attachment.uploaded_at,
          uploadedBy: attachment.uploaded_by,
          uploadedByEmail: attachment.uploaded_by_email
        }));

        // Log the entire API response structure
        console.log("FULL API RESPONSE STRUCTURE:", JSON.stringify(response.data, null, 2));
        console.log("ORGANIZATION DATA STRUCTURE:", JSON.stringify(orgData, null, 2));

        // Extract KMP details from the API response - check multiple possible locations
        let kmpDetails = [];

        // Try to get KMP data from different possible locations in the API response
        if (response.data.data.kmp_details && Array.isArray(response.data.data.kmp_details)) {
          kmpDetails = response.data.data.kmp_details;
        } else if (orgData.kmps && Array.isArray(orgData.kmps)) {
          // Map backend KMP structure to frontend structure
          kmpDetails = orgData.kmps.map((kmp: any) => ({
            id: kmp.id,
            name: kmp.name,
            email: kmp.email,
            phone: kmp.phone_number,
            phone_number: kmp.phone_number,
            din: kmp.din,
            designation: kmp.designation
          }));
        } else if (orgData.key_management_personnel_in_organization && Array.isArray(orgData.key_management_personnel_in_organization)) {
          // Alternative backend structure
          kmpDetails = orgData.key_management_personnel_in_organization.map((kmp: any) => ({
            id: kmp.id,
            name: kmp.name,
            email: kmp.email,
            phone: kmp.phone_number,
            phone_number: kmp.phone_number,
            din: kmp.din,
            designation: kmp.designation
          }));
        }

        console.log("KMP DETAILS STRUCTURE:", JSON.stringify(kmpDetails, null, 2));
        console.log("KMP DETAILS TYPE:", typeof kmpDetails);
        console.log("KMP DETAILS IS ARRAY:", Array.isArray(kmpDetails));
        console.log("KMP DETAILS LENGTH:", kmpDetails.length);

        // Extract grant histories from the API response - ensure it's properly extracted
        const grantHistories = response.data.data.grant_histories || [];
        console.log("GRANT HISTORIES STRUCTURE:", JSON.stringify(grantHistories, null, 2));
        console.log("GRANT HISTORIES TYPE:", typeof grantHistories);
        console.log("GRANT HISTORIES IS ARRAY:", Array.isArray(grantHistories));
        console.log("GRANT HISTORIES LENGTH:", grantHistories.length);

        console.log("NUMBER OF TEAM MEMBERS:", orgData.number_of_team_members);
        console.log("MISSION VISION:", orgData.mission);
        console.log("BACKGROUND HISTORY:", orgData.background_history);

        // Create profile object with raw API response data
        const profileData = {
          id: orgData.id.toString(),
          name: orgData.organization_name,
          sector: orgData.organization_legal_type_name || 'NON_PROFIT',
          contactPerson: 'Contact Person', // Would need to be fetched from KMP data
          contactEmail: orgData.email_address,
          contactPhone: orgData.phone_number,
          location: "Mumbai",
          startDate: grantsData.length > 0 ? grantsData[0].project_start_date : orgData.created_at,
          endDate: grantsData.length > 0 ? grantsData[0].project_end_date : undefined,
          status: 'active' as 'active' | 'pending' | 'completed',

          // Make sure these fields are properly populated from the backend
          description: orgData.background_history || "", // Background history
          mission: orgData.mission || "", // Mission statement
          vision: orgData.vision || "", // Vision statement from backend

          foundedYear: new Date(orgData.created_at).getFullYear(),
          registrationNumber: orgData.trust_registration_number || '',
          csrRegistrationNumber: orgData.csr_registration_number || '', // Add CSR registration number directly
          taxExemptionNumber: orgData.tax_registration_number || '',
          taxRegistrationNumberUnder12A: orgData.tax_registration_number_under_12_a || '',
          darpanId: orgData.darpan_id || '',
          panCard: orgData.pan_number || '',
          website: orgData.website_url || '',
          logoKey: orgData.logo_key || null, // Add logo key for profile photo
          totalFunding: totalBudget,
          disbursedAmount: totalDisbursed,
          remainingAmount: remainingBalance,
          grants: grants,
          supportingDocuments: attachments,

          // Store the raw API response for direct access
          _rawApiResponse: response.data.data,

          // Add these fields directly to the profile object with proper fallbacks
          kmp_details: kmpDetails || [], // Ensure this is never undefined
          grant_histories: grantHistories || [], // Ensure this is never undefined
          previousGrants: grantHistories || [], // Add previousGrants directly for easier access
          teamMembers: orgData.number_of_team_members || 0, // Add teamMembers directly for easier access
          number_of_team_members: orgData.number_of_team_members || 0,

          // Add organization data directly for easier access
          organization: orgData
        };

        return profileData;
      }
    } catch (apiError) {
      console.warn(`API call for grantee profile ${id} failed, using mock data:`, apiError);
    }

    // Fallback to mock data if API call fails
    return new Promise(resolve => {
      setTimeout(() => {
        const profile = mockGranteeProfiles[id];

        // If profile doesn't exist in mock data, try to create one from unified organization
        if (!profile) {
          const org = mockUnifiedOrganizations.find(o => o.id === id);
          if (org) {
            const newProfile: GranteeProfile = {
              id: org.id,
              name: org.name,
              organization: org.name,
              sector: org.sector,
              contactPerson: org.contactPerson || 'Contact Person',
              contactEmail: org.contactEmail || '<EMAIL>',
              location: org.location || 'Location',
              startDate: org.startDate || new Date().toISOString().split('T')[0],
              endDate: org.endDate,
              status: org.status === 'Active' ? 'Active' : org.status === 'Completed' ? 'Completed' : 'Pending',
              totalFunding: org.totalBudget,
              disbursedAmount: org.totalDisbursed,
              remainingAmount: org.remainingBalance,
              utilizationPercentage: org.utilizationPercentage,

              // Organization details
              organization_name: org.name,
              organization_legal_type: org.orgType || 'NON_PROFIT',
              organization_function_type: org.sector.toUpperCase(),
              organization_legal_type_name: org.orgType === 'TRUST' ? 'Trust' : org.orgType === 'SOCIETY' ? 'Society' : 'Non-Profit',
              organization_function_type_name: org.sector,
              pan_number: `AAAA${org.id}1234Z`,
              mailing_address: `${org.location || 'Address not available'}`,
              phone_number: `+91 98765${org.id}210`,
              email_address: org.contactEmail || `contact@${org.name.toLowerCase().replace(/\s+/g, '')}.org`,
              website_url: `https://www.${org.name.toLowerCase().replace(/\s+/g, '')}.org`,
              number_of_team_members: 20,
              mission_vision: `To improve ${org.sector.toLowerCase()} outcomes in communities across India.`,
              background_history: `Founded as a ${org.orgType?.toLowerCase() || 'non-profit'} organization focused on ${org.sector.toLowerCase()}.`,
              previous_grants_info: '',

              // Registration details
              tax_registration_number: `AAAA${org.id}1234Z`,
              csr_registration_number: `CSR0000${org.id}234`,
              tax_registration_number_under_12_a: `12A-${org.id}23456`,
              trust_registration_number: org.orgType === 'TRUST' ? `TRUST-${org.id}-1234` : '',
              darpan_id: `${org.location?.substring(0, 2).toUpperCase() || 'IN'}/2020/00${org.id}2345`,
              fcra_registration_number: `FCRA-${org.id}23456`,

              // Grant details
              grant_id: `PF-2025-${org.id}-001`,
              grant_name: `${org.sector} Development Project`,
              project_start_date: org.startDate || '2025-01-01',
              project_end_date: org.endDate || '2025-12-31',
              purpose_of_grant: `Supporting ${org.sector.toLowerCase()} initiatives in the community.`,
              annual_budget: org.totalBudget,
              funding_sources: 'NIL',

              // KMP details
              kmp_details: [
                {
                  id: "1",
                  name: org.contactPerson || 'Contact Person',
                  email: org.contactEmail || `contact@${org.name.toLowerCase().replace(/\s+/g, '')}.org`,
                  phone: `+91 98765${org.id}210`,
                  din: `0012345${org.id}`,
                  designation: "Director"
                }
              ],

              // Grant history
              grant_history: [
                {
                  id: "1",
                  grant_name: `${org.sector} Development Project`,
                  amount: org.totalBudget * 0.8,
                  start_date: org.startDate || '2022-01-01',
                  end_date: org.endDate || '2022-12-31',
                  status: "completed",
                  purpose: `Previous ${org.sector.toLowerCase()} development initiative`
                },
                {
                  id: "2",
                  grant_name: `Current ${org.sector} Grant`,
                  amount: org.totalBudget,
                  start_date: org.startDate || '2023-01-01',
                  end_date: org.endDate || '2023-12-31',
                  status: "active",
                  purpose: `Supporting ${org.sector.toLowerCase()} initiatives in the community`
                }
              ]
            };
            resolve(newProfile);
            return;
          }
        }

        resolve(profile);
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching grantee profile with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Adapts an Organization object to the UnifiedOrganization format
 */
export function adaptOrganizationToUnified(org: Organization): UnifiedOrganization {
  // Calculate disbursed amount (70% of total for demo purposes)
  const totalDisbursed = Math.floor(org.totalFunding * 0.7);

  return {
    id: org.id,
    name: org.name,
    sector: org.sector,
    totalBudget: org.totalFunding,
    totalDisbursed: totalDisbursed,
    remainingBalance: org.totalFunding - totalDisbursed,
    utilizationPercentage: 70, // Approximation for demo
    lastDisbursementDate: "2023-09-15", // Placeholder
    status: org.status,
    type: "organization",
    contactPerson: org.contactPerson,
    contactEmail: `contact@${org.name.toLowerCase().replace(/\s+/g, '')}.org`,
    location: org.location,
    startDate: org.startDate,
    endDate: org.endDate,
    orgType: org.orgType
  };
}

/**
 * Adapts a Grantee object to the UnifiedOrganization format
 */
export function adaptGranteeToUnified(grantee: Grantee): UnifiedOrganization {
  // Calculate disbursed amount (70% of total for demo purposes)
  const totalDisbursed = Math.floor(grantee.totalFunding * 0.7);

  return {
    id: grantee.id,
    name: grantee.organization, // Use organization name as the name
    sector: grantee.orgType || 'NON_PROFIT', // Use organization type as sector
    totalBudget: grantee.totalFunding,
    totalDisbursed: totalDisbursed,
    remainingBalance: grantee.totalFunding - totalDisbursed,
    utilizationPercentage: 70, // Approximation for demo
    lastDisbursementDate: "2023-09-15", // Placeholder
    status: grantee.status,
    type: "grantee",
    contactPerson: grantee.contactPerson,
    contactEmail: grantee.contactEmail,
    location: grantee.location,
    startDate: grantee.startDate,
    endDate: grantee.endDate,
    orgType: grantee.orgType
  };
}

/**
 * Adapts an array of Organization objects to the UnifiedOrganization format
 */
export function adaptOrganizationsToUnified(orgs: Organization[]): UnifiedOrganization[] {
  return orgs.map(adaptOrganizationToUnified);
}

/**
 * Adapts an array of Grantee objects to the UnifiedOrganization format
 */
export function adaptGranteesToUnified(grantees: Grantee[]): UnifiedOrganization[] {
  return grantees.map(adaptGranteeToUnified);
}

/**
 * Helper function to format currency
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0,
  }).format(amount);
};

export const getMyGrants = async (): Promise<OrganizationGrantAPIResponse[]> => {
    const response = await apiClient.get('/api/v1/me/grants/');
    return response.data;
}
