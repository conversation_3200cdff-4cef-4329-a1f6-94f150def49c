// BasicDetailsSchema.ts
import { z } from "zod";

export const basicDetailsSchema = z.object({
  pointOfContactName: z.string().min(1, "Name is required"),
  phone: z.string().optional(),
  email: z.string().email("Invalid email").optional(),
  addressLine1: z.string().optional(),
  addressLine2: z.string().optional(),
  locality: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  organizationName: z.string().optional(),
  organizationLegalType: z.string().optional(),
  csrRegistrationNumber: z.string().optional(),
  taxRegistrationNumber: z.string().optional(),
  taxRegistrationNumberUnder12A: z.string().optional(),
  fcraRegistrationNumber: z.string().optional(),
  trustRegistrationNumber: z.string().optional(),
  darpanId: z.string().optional(),
  panNumber: z.string().optional(),
  mission: z.string().min(1, "Mission is required").optional(),
  vision: z.string().min(1, "Vision is required").optional(),
  backgroundHistory: z.string().max(300, "Background history must be 300 characters or less").optional(),
});

export type BasicDetailsFormType = z.infer<typeof basicDetailsSchema>;

export const basicDetailsFormDefaultValues: BasicDetailsFormType = {
  pointOfContactName: "",
  phone: "",
  email: "",
  addressLine1: "",
  addressLine2: "",
  locality: "",
  city: "",
  state: "",
  postalCode: "",
  country: "",
  organizationName: "",
  organizationLegalType: "",
  csrRegistrationNumber: "",
  taxRegistrationNumber: "",
  taxRegistrationNumberUnder12A: "",
  fcraRegistrationNumber: "",
  trustRegistrationNumber: "",
  darpanId: "",
  panNumber: "",
  mission: "",
  vision: "",
  backgroundHistory: ""
};